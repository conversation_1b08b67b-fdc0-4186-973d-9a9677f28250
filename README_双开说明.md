# 微信双开版本修改说明

## 已完成的修改

### 1. 主要Bundle ID修改
- **主应用**: `com.tencent.xin` → `com.tencent.xin.dual`
- **应用显示名称**: `微信` → `微信双开`

### 2. 扩展Bundle ID修改
所有扩展的Bundle ID都已更新：
- WeChatNotificationServiceExtension: `com.tencent.xin.dual.WeChatNotificationServiceExtension`
- WeChatScreenCapture: `com.tencent.xin.dual.WeChatScreenCapture`
- WeChatShareExtensionNew: `com.tencent.xin.dual.WeChatShareExtensionNew`
- WeChatSiriExtension: `com.tencent.xin.dual.WeChatSiriExtension`
- WeChatSiriExtensionUI: `com.tencent.xin.dual.WeChatSiriExtensionUI`
- WeChatWidgetExtension: `com.tencent.xin.dual.WeChatWidgetExtension`

### 3. URL Schemes修改
所有关键的URL Schemes都添加了"dual"后缀，避免与原版微信冲突：
- `weixin` → `weixindual`
- `wechat` → `wechatdual`
- `weixinapp` → `weixinappdual`
- 等等...

### 4. Watch功能支持
- 更新了Apple Watch复杂功能的client ID
- 从 `com.tencent.qy.xin.watchkitapp.watchkitextension` 
- 到 `com.tencent.qy.xin.dual.watchkitapp.watchkitextension`

### 5. 用户活动类型
- NSUserActivityTypes中的Bundle ID也已更新
- 包括Watch相关的活动类型

## 生成的文件
- `WeChat_Dual.ipa` - 修改后的双开版本IPA文件

## 下一步操作

### 重新签名
您需要使用自己的开发者证书对IPA进行重新签名：

```bash
# 使用Xcode或第三方工具如iOS App Signer进行签名
# 或使用命令行工具如codesign
```

### 安装方式
1. **开发者账号**: 使用Xcode或Apple Configurator 2安装
2. **企业证书**: 直接安装
3. **第三方工具**: 如AltStore、Sideloadly等

### 注意事项
1. 此修改版本与原版微信可以同时安装
2. 数据是独立的，不会相互影响
3. 需要重新登录微信账号
4. Watch功能需要重新配对

## 技术细节
- 保持了原有的所有功能
- 修改了关键标识符以实现双开
- 保留了Watch复杂功能支持
- 所有扩展功能都已适配

## 免责声明
此修改仅供学习和个人使用，请遵守相关法律法规和微信服务条款。
