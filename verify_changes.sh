#!/bin/bash

echo "=== 验证微信双开版本修改 ==="
echo

# 检查主应用Info.plist
echo "1. 检查主应用Bundle ID..."
MAIN_BUNDLE_ID=$(plutil -extract CFBundleIdentifier raw "Payload/WeChat.app/Info.plist")
echo "主应用Bundle ID: $MAIN_BUNDLE_ID"

# 检查应用显示名称
DISPLAY_NAME=$(plutil -extract CFBundleDisplayName raw "Payload/WeChat.app/Info.plist")
echo "应用显示名称: $DISPLAY_NAME"

echo

# 检查扩展Bundle IDs
echo "2. 检查扩展Bundle IDs..."
for ext in WeChatNotificationServiceExtension WeChatScreenCapture WeChatShareExtensionNew WeChatSiriExtension WeChatSiriExtensionUI WeChatWidgetExtension; do
    if [ -f "Payload/WeChat.app/PlugIns/$ext.appex/Info.plist" ]; then
        # 先转换为XML格式以便读取
        plutil -convert xml1 -o "/tmp/$ext.plist" "Payload/WeChat.app/PlugIns/$ext.appex/Info.plist" 2>/dev/null
        if [ -f "/tmp/$ext.plist" ]; then
            BUNDLE_ID=$(plutil -extract CFBundleIdentifier raw "/tmp/$ext.plist" 2>/dev/null)
            echo "$ext: $BUNDLE_ID"
            rm "/tmp/$ext.plist"
        else
            echo "$ext: 无法读取Bundle ID"
        fi
    else
        echo "$ext: 文件不存在"
    fi
done

echo

# 检查Watch复杂功能
echo "3. 检查Watch复杂功能配置..."
if [ -f "Payload/WeChat.app/gallery.ckcomplication/complicationManifest.json" ]; then
    WATCH_CLIENT_ID=$(grep "client ID" "Payload/WeChat.app/gallery.ckcomplication/complicationManifest.json" | cut -d'"' -f4)
    echo "Watch Client ID: $WATCH_CLIENT_ID"
else
    echo "Watch复杂功能配置文件不存在"
fi

echo

# 检查URL Schemes
echo "4. 检查URL Schemes..."
echo "检查是否包含dual后缀的schemes..."
plutil -extract CFBundleURLTypes.0.CFBundleURLSchemes raw "Payload/WeChat.app/Info.plist" | grep -o "dual" | wc -l | xargs echo "包含dual后缀的schemes数量:"

echo

# 检查生成的IPA文件
echo "5. 检查生成的IPA文件..."
if [ -f "WeChat_Dual.ipa" ]; then
    IPA_SIZE=$(ls -lh WeChat_Dual.ipa | awk '{print $5}')
    echo "WeChat_Dual.ipa 文件大小: $IPA_SIZE"
    echo "✅ IPA文件生成成功"
else
    echo "❌ IPA文件未找到"
fi

echo
echo "=== 验证完成 ==="
