#!/bin/bash

# 扩展列表
extensions=(
    "WeChatScreenCapture.appex"
    "WeChatShareExtensionNew.appex"
    "WeChatSiriExtension.appex"
    "WeChatSiriExtensionUI.appex"
    "WeChatWidgetExtension.appex"
)

# 处理每个扩展
for ext in "${extensions[@]}"; do
    echo "Processing $ext..."
    
    # 备份原文件
    cp "Payload/WeChat.app/PlugIns/$ext/Info.plist" "Payload/WeChat.app/PlugIns/$ext/Info.plist.bak"
    
    # 转换为XML格式
    plutil -convert xml1 -o "Payload/WeChat.app/PlugIns/$ext/Info_temp.plist" "Payload/WeChat.app/PlugIns/$ext/Info.plist.bak"
    
    # 修改Bundle ID
    sed -i '' 's/com\.tencent\.xin\./com.tencent.xin.dual./g' "Payload/WeChat.app/PlugIns/$ext/Info_temp.plist"
    
    # 转换回二进制格式
    plutil -convert binary1 "Payload/WeChat.app/PlugIns/$ext/Info_temp.plist"
    
    # 替换原文件
    mv "Payload/WeChat.app/PlugIns/$ext/Info_temp.plist" "Payload/WeChat.app/PlugIns/$ext/Info.plist"
    
    echo "Completed $ext"
done

echo "All extensions updated!"
