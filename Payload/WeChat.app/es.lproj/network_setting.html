<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="Content-Language" content="en" charset="utf-8">
	<meta charset="UTF-8">
	<meta id="viewport" name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1; user-scalable=no;" />
	<style> html,p,h1,ul,li{margin:0px;padding:0px;} ul{list-style-type:none;} body{color:#000;font:14px/1.5 微软雅黑,Helvetica,&quot;Helvetica Neue&quot;,&quot;segoe UI Light&quot;,&quot;Kozuka Gothic Pro&quot;;} h1{font-size:16px; padding-bottom:12px;margin-bottom:5px;} .articleTitle{margin-bottom:6px;} .sec_body { margin:25px 15px;} p{margin-bottom:6px;} .bold{font-weight:bold;} .article{margin-bottom:32px;} .sec_body .content { padding:10px;border-width:0px; } @media (prefers-color-scheme: dark) { body { background-color: #232323; color: rgba(255, 255, 255, .8); } } </style>
	<title>Sin conexi&oacute;n a Internet</title>
</head>
<body class="sec_body">
	<div class="container">
		<h1>Su dispositivo no est&aacute; conectado a Internet</h1>
		<div class="article">
			<p class="articleTitle">Para conectarse a Internet, pruebe los siguientes m&eacute;todos:</p>
			<ul>
				<li>En su dispositivo, vaya a &quot;<strong>Ajustes</strong>&quot; - &quot;<strong>Wi-Fi</strong>&quot;, y &uacute;nase a una red Wi-Fi disponible.</li>
				<li>En su dispositivo, vaya a &quot;<strong>Ajustes</strong>&quot; - &quot;<strong>Celular</strong>&quot;, y habilite &quot;<strong>Datos m&oacute;viles</strong>&quot;. (Es posible que su proveedor de servicios le cobre el uso de los datos).</li>
			</ul>
		</div>
		<div class="article">
			<p class="articleTitle">Si emparej&oacute; su dispositivo con un Apple Watch:</p>
			<ul>
				<li>Abra la app &quot;<strong>Watch</strong>&quot;, vaya a &quot;<strong>Datos celulares</strong>&quot; - &quot;<strong>WeChat</strong>&quot; y permita que WeChat acceda a los datos.</li>
			</ul>
		</div>
		<div class="article">
			<p class="articleTitle">Si se conect&oacute; a una red Wi-Fi:</p>
			<ul>
				<li>Compruebe si el punto de acceso Wi-Fi est&aacute; conectado a Internet o si su dispositivo tiene permiso para acceder al punto de acceso.</li>
			</ul>
		</div>
	</div>
</body>
</html>

