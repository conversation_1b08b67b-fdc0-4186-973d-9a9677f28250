{"version": "1.0.0", "sdk_settings": {"ocr_settings": {"app_id": "10198923", "ocrtype": "idcard", "cardtype": 0, "mode_type": 2, "auto_timeout_ms": 10000, "user_id": "360566935", "secret_id": "AKIDfEbnkRTNXkkVt0pjsoXyCE1Yu63lqQQC", "secret_key": "e3HH3Zq01mHcicJkywgATWFjxzqYzIPG", "idcard": {"result_api_url": "RESULT_REQUEST", "request_options": {"border_check_flag": true, "enable_reshoot": true, "enable_detect_copy": true, "enable_quality_value": true, "ret_portrait_flag": true, "enable_detect_ps": true}}, "creditcard": {"result_api_url": "RESULT_REQUEST", "request_options": {"enable_border_check": true, "enable_reshoot_check": true, "enable_copy_check": true, "enable_quality_value": true}}}, "silent_settings": {"app_id": "youtu_ios_0823", "timeout_countdown_ms": 50000, "check_eye_open": false, "manual_trigger": false, "same_tips_filter": false, "action_security_level": 1, "action_default_seq": [5], "result_api_url": "RESULT_REQUEST"}, "action_settings": {"app_id": "youtu_ios_0823", "timeout_countdown_ms": 25000, "action_security_level": 1, "config_api_url": "CONFIG_REQUEST", "result_api_url": "RESULT_REQUEST", "backend_proto_type": 2, "action_default_seq": [1, 2], "smallface_ratio_threshold": 0.3, "request_options": {"best_image": true, "do_live": true, "do_mouth_detect": true, "do_eye_detect": true}}, "reflect_settings": {"app_id": "youtu_ios_0823", "timeout_countdown_ms": 25000, "check_eye_open": true, "reflect_security_level": 2, "backend_proto_type": 2, "config_api_url": "CONFIG_REQUEST", "result_api_url": "RESULT_REQUEST", "pitch_threshold": 30, "yaw_threshold": 25, "manual_trigger": true, "force_pose_check": true}, "action+reflect_settings": {"app_id": "youtu_ios_0823", "timeout_countdown_ms": 25000, "check_eye_open": true, "action_security_level": 1, "reflect_security_level": 2, "action_default_seq": [0], "config_api_url": "CONFIG_REQUEST", "result_api_url": "RESULT_REQUEST", "force_pose_check": true, "reflect_tips_countdown_ms": 500, "smallface_ratio_threshold": 0.3, "pitch_threshold": 30, "yaw_threshold": 25, "manual_trigger": true}, "lipread_settings": {"manual_trigger": true, "resource_online": false, "timeout_countdown_ms": 25000, "num_interval_ms": 1000, "app_id": "youtu_android_demo", "config_api_url": "CONFIG_REQUEST", "result_api_url": "RESULT_REQUEST", "backend_proto_type": 2, "action_default_seq": [6, 0, 9, 8], "smallface_ratio_threshold": 0.3, "force_pose_check": false, "pitch_threshold": 20, "yaw_threshold": 25, "request_options": {"business_id": "wx_default", "person_id": "wx_default4", "person_type": "youtu", "req_type": "live", "live_type": 0}}, "need_network": true}}