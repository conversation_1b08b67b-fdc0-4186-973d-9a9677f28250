﻿<!DOCTYPE html>
<html>
<head>

	<meta http-equiv="content-type" content="text/html;charset=utf-8"/>
	<meta id="viewport" name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1; user-scalable=no;" />
	<style>
		html,p,h1,ul,li{margin:0px;padding:0px;}
		ul{list-style-type:none;}
		body{color:#000;font:14px/1.5 微软雅黑,Helvetica,"Helvetica Neue","segoe UI Light","Kozuka Gothic Pro";}
		h1{font-size:16px; padding-bottom:12px;margin-bottom:5px;}
		.articleTitle{margin-bottom:6px;}
		.sec_body { margin:25px 15px;}
		p{margin-bottom:6px;}
		.bold{font-weight:bold;}
		.container { }
		.article{margin-bottom:32px;}
		.sec_body .content { padding:10px;border-width:0px; }
        @media (prefers-color-scheme: dark) {
          body {
              background-color: #232323;
              color: rgba(255, 255, 255, .8);
          }
        }
	</style>
	
	<title>未能连接到互联网</title>
	
</head>
<body class="sec_body">
	<div class="container">
		<div>
			<h1>您的设备未启用移动网络或 Wi-Fi 网络</h1>
			<div class="article">
				<p class="articleTitle">如需要连接到互联网，可以参照以下方法：</p>
				<ul>
					<li>在设备的 “<strong>设置</strong>” - “<strong>Wi-Fi 网络</strong>” 设置面板中选择一个可用的 Wi-Fi 热点接入。</li>
					<li>在设备的 “<strong>设置</strong>” -“<strong>蜂窝网络</strong>” 设置面板中启用<strong>蜂窝数据</strong> (启用后运营商可能会收取数据通信费用)。</li>
				</ul>
			</div>
			<div class="article">
				<p class="articleTitle">如果您有配对支持蜂窝网络的 Apple Watch：</p>
				<ul>
					<li>在设备的“<strong>Watch</strong>”应用 - “<strong>蜂窝网络</strong>” - “<strong>微信</strong>” 设置面板中允许微信使用数据。</li>
				</ul>
			</div>
			<div class="article">
				<p class="articleTitle">如果您已接入 Wi-Fi 网络：</p>
				<ul>
					<li>请检查您所连接的 Wi-Fi 热点是否已接入互联网，或该热点是否已允许您的设备访问互联网。</li>
				</ul>
			</div>
		</div>

	</div>
</body>
</html>
