 (function (global) {
     var GameGlobal = global
     global.GameGlobal = GameGlobal
     var wx = {}
     global.wx = wx
     if (typeof global.__wxConfig === "undefined") {
         global.__wxConfig = {};
     }
     global.__wxConfig.platform = "iOS"
     
     if (!global.WeixinJSBridge) {
         global.WeixinJSBridge = global.mb.JSBridge;
     }

     NativeGlobal.gc = function () {
         var x = {}
         for (var i = 0; i < 100000; i++) {
             x[i] = []
         }
         x = null
     }

     // require/define
     var f = (function (global) {
         var MODULE_STATUS_UNLOAD = 1
         var MODULE_STATUS_LOADED = 2
         var MODULES = {}
         var notCacheScripts = {}

         var backupGlobalFlags = function () {
             return {
                 __wxRoute: global.__wxRoute,
                 __wxAppCurrentFile__: global.__wxAppCurrentFile__,
                 __wxRouteBegin: global.__wxRouteBegin,
             }
         }
         var restoreGlobalFlags = function (globalFlags) {
             global.__wxRoute = globalFlags.__wxRoute
             global.__wxAppCurrentFile__ = globalFlags.__wxAppCurrentFile__
             global.__wxRouteBegin = globalFlags.__wxRouteBegin
         }

         define = function (modId, factory) {
             MODULES[modId] = {
                 status: MODULE_STATUS_UNLOAD,
                 factory: factory
             }
         }

         var getModDir = function (modId) {
             var match = modId.match(/(.*)\/([^/]+)?$/)
             return !match || !match[1] ? './' : match[1]
         }

         var getSubPackageConfig = function (dir) {
             if (!dir) {
                 return undefined
             }

             if (__wxConfig.subPackages) {
                 for (var i = 0, len = __wxConfig.subPackages.length; i < len; i++) {
                     if (dir.indexOf(__wxConfig.subPackages[i].root) === 0) {
                         return __wxConfig.subPackages[i]
                     }
                 }
             }

             return undefined
         }

         var splitPath = function (path) {
             var realPath = [],
                 paths = path.split('/')

             for (var i = 0, len = paths.length; i < len; ++i) {
                 // 转换目录路径
                 var pathItem = paths[i]
                 if (pathItem == '' || pathItem == '.') {
                     continue
                 }
                 if (pathItem == '..') {
                     if (realPath.length == 0) {
                         realPath = null
                         break
                     }
                     realPath.pop()
                 } else if (i + 1 < len && paths[i + 1] == '..') {
                     i++
                 } else {
                     realPath.push(pathItem)
                 }
             }

             return realPath
         }

         var checkNodeModulesFile = function (dirPath) {
             var requirePath = splitPath(dirPath + '/index.js').join('/')
             if (MODULES[requirePath]) return requirePath

             requirePath = splitPath(dirPath).join('/')
             if (!/\.js$/.test(requirePath)) requirePath += '.js'
             if (MODULES[requirePath]) return requirePath

             return ''
         }

         var checkNodeModules = function (modId, modDir, requireId) {
             var id = modId
             if (!/\.js$/.test(id)) id += '.js'

             // 包内文件
             if (typeof id === 'string' && MODULES[id]) return id

             // 从 node_modules 里检查
             var realDirpath = splitPath(modDir)
             if (!realDirpath) throw new Error('can\'t find module : ' + requireId)

             var modRelativePath = modId.substring(realDirpath.join('/').length),
                 requirePath

             while (realDirpath.length) {
                 // 非根目录
                 var dirPath = realDirpath.join('/') + '/miniprogram_npm' + modRelativePath

                 requirePath = checkNodeModulesFile(dirPath)
                 if (requirePath) break

                 realDirpath.pop()
             }

             if (!requirePath) {
                 // 根目录
                 modRelativePath = modRelativePath[0] === '/' ? modRelativePath : '/' + modRelativePath
                 var dirPath = 'miniprogram_npm' + modRelativePath

                 requirePath = checkNodeModulesFile(dirPath)
             }

             return requirePath ? requirePath : modId
         }

         var _require = function (modId) {
             var modDir = getModDir(modId)
             return function (requireId) {
                 // modId
                 if (typeof requireId !== 'string') {
                     throw new Error('require args must be a string')
                 }

                 var realFilepath = splitPath(modDir + '/' + requireId)
                 if (requireId === '/__wx__/private-api') realFilepath = splitPath(requireId)
                 if (!realFilepath) throw new Error('can\'t find module : ' + requireId)

                 try {
                     var id = realFilepath.join('/')
                     id = checkNodeModules(id, modDir, requireId)
                     
                     var inFunctionalPage = function (id) {
                         return splitPath(id)[0] === 'functional-pages'
                     }
                     if (inFunctionalPage(id) !== inFunctionalPage(modId)) {
                         Reporter.thirdErrorReport({
                             error: new Error(
                                 'should not require across "functional-pages" folder'),
                             extend: 'at require("' + requireId + '") in ' + modId
                         })
                     }

                     if (__wxConfig.platform === 'devtools' && __wxConfig.subPackages) {
                         var distConfig = getSubPackageConfig(id)
                         var srcConfig = getSubPackageConfig(modId)

                         if (distConfig &&
                             distConfig !== srcConfig) {
                             throw new Error('should not require ' + requireId + ' in ' + modId +
                                 ' because they are in diffrent subPackages')
                         }
                     }
                     return require(id)
                 } catch (e) {
                     throw e
                 }
             }
         }

         require = function (modId, cache) {
             if (typeof cache === 'undefined') {
                 cache = true
             }

             if (typeof modId !== 'string') {
                 throw new Error('require args must be a string')
             }
             var mod = MODULES[modId]
             if (!mod) {
                 var rootModId = modId.indexOf('/') === -1 ? modId + '/index.js' : modId
                 rootModId = 'miniprogram_npm/' + rootModId
                 if (!/\.js$/.test(rootModId)) rootModId += '.js'
                 mod = MODULES[rootModId]
             }
             if (!modId.endsWith('.js')) modId += '.js'
             if (!mod) {
                 throw new Error('module "' + modId + '" is not defined')
             }

             var module = {
                 exports: {}
             } // module.exports
             var factory = mod.factory

             if (!cache || notCacheScripts[modId]) {
                 delete mod.exports
                 mod.status = MODULE_STATUS_UNLOAD
                 notCacheScripts[modId] = true

                 factory && factory(_require(modId), module, module.exports)
                 return module.exports
             }

             if (mod.status === MODULE_STATUS_UNLOAD) {
                 mod.exports = module.exports
                 mod.status = MODULE_STATUS_LOADED

                 var ret = undefined
                 try {
                     factory && (ret = factory(_require(modId), module, module.exports))
                 } catch (e) {
                     mod.status = MODULE_STATUS_UNLOAD
                     throw e
                 }

                 mod.exports = module.exports !== undefined ? module.exports : ret;
             }

             // else if (mod.status == MODULE_STATUS_LOADED)
             return mod.exports
         }

         requireOnce = function (modId) {
             return require(modId, false)
         }

         global.__modules__ = MODULES


     });
     f(global);

     var requireMiniProgramFile = require
     var defineMiniProgramFile = define

     global.require = require;
     global.define = define;


     (function (global) {
         var pack = function (params) {
             if (params == null) {
                 return params
             }
             var nativeBuffers = []

             for (var key in params) {
                 var value = params[key]
                 if (typeof value !== 'undefined' && _getDataType(value) === 'ArrayBuffer' && typeof value
                     .byteLength !==
                     'undefined') {
                     var buffer = _new(value)
                     buffer.key = key
                     nativeBuffers.push(buffer)
                 }
             }

             if (nativeBuffers.length > 0) {
                 for (var i = 0; i < nativeBuffers.length; i++) {
                     var buffer = nativeBuffers[i]
                     delete params[buffer.key]
                 }
                 params.__nativeBuffers__ = nativeBuffers
             }

             return params
         }

         var unpack = function (params) {
             if (params == null || params.__nativeBuffers__ == null) {
                 return params
             }

             var nativeBuffers = params.__nativeBuffers__
             delete params.__nativeBuffers__

             for (var i = 0; i < nativeBuffers.length; i++) {
                 var buffer = nativeBuffers[i]
                 if (buffer == null) continue;

                 var arrayBuffer = _get(buffer)
                 if (typeof arrayBuffer !== 'undefined' && _getDataType(arrayBuffer) === 'ArrayBuffer') {
                     params[buffer.key] = arrayBuffer
                 }
             }

             return params
         }

         var _getDataType = function (data) {
             return Object.prototype.toString.call(data).split(' ')[1].split(']')[0]
         }

         var _get = function (bufferObj) {
             if (bufferObj == null) {
                 return
             }

             if (typeof bufferObj.base64 !== 'undefined') {
                 return base64ToArrayBuffer(bufferObj.base64)
             }
         }

         var base64ToArrayBuffer = function (base64) {
             var binaryString = atob(base64)
             var len = binaryString.length
             var bytes = new Uint8Array(len)
             for (var i = 0; i < len; i++) {
                 bytes[i] = binaryString.charCodeAt(i)
             }
             return bytes.buffer
         }

         global.NativeBufferUtil = {}
         global.NativeBufferUtil.pack = pack
         global.NativeBufferUtil.unpack = unpack
     })(global)

     var invokeSync = function (apiName, args = {}) {
         var result;
         var cb = function (res) {
             if (res && res.errMsg) {
                 const isFail = res.errMsg.indexOf(`${apiName}:fail`) === 0
                 if (!isFail) {
                     result = res;
                 }
             }
         }
         
         global.mb.JSBridge.invoke(apiName, args, cb)

         if (result) {
             return result
         }
     }

     var invokeAsync = function (apiName, args = {}, callback) {
         var cb = function (res) {
             if (res && res.errMsg) {
                 const isFail = res.errMsg.indexOf(`${apiName}:fail`) === 0

                 if (isFail) {
                     callback.fail(res.errMsg)
                 } else {
                     callback.success(res)
                 }
             } else {
                 // ???
                 callback.fail(res)
             }
         }

         global.mb.JSBridge.invoke(apiName, args, cb);
     }
     
     global.invokeSync = invokeSync
     global.invokeAsync = invokeAsync
     
     // hook console
     if (global.console) {
         var originLog = global.console.log;
         var originWarn = global.console.warn;
         var originError = global.console.error;
         
         global.console.log = (...args) => {
             global.mb.JSBridge.log(...args);
             originLog(...args);
         }
         
         global.console.warn = (...args) => {
             global.mb.JSBridge.log(...args);
             originWarn(...args);
         }
         
         global.console.error = (...args) => {
             global.mb.JSBridge.log(...args);
             originError(...args);
         }
     }

 })(globalThis);
