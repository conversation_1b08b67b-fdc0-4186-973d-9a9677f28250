//
//  gamecenter_wxbridge.js
//  MicroMessenger
//
//  Created by <PERSON><PERSON><PERSON>(陈卫安) on 2020/8/20.
//  Copyright © 2020 Tencent. All rights reserved.
//

(function(global) {
    if (global.WeixinJSBridge) {
        return;
    }
    
    var MessageType = {
        NONE: 'NONE',
        READY: 'READY',
        INVOKE: 'INVOKE',
        CALLBACK: 'CALLBACK',
        EVENT: 'EVENT',
        SUBSCRIBE: 'SUBSCRIBE',
        UNSUBSCRIBE: 'UNSUBSCRIBE'
    }
    
    var invokeCallbackId = 0;
    var invokeCallbacks = {};
    
    var invoke = function(func, params, callback) {
        if (!func || typeof func !== 'string') {
            return;
        };
        if (typeof params !== 'object') {
            params = {};
        };
        var callbackId = 0;
        var callbackFunc = 'undefined';
        
        switch (typeof callback) {
            case 'undefined':
                break;
            case 'function':
                callbackId = (++invokeCallbackId).toString();
                callbackFunc = callback;
                break;
            default:
                return;
        }
        
        if (callbackId !== 0) {
            invokeCallbacks[callbackId] = callback;
        }
        
        var msg = {
            func: func,
            params: params || {},
            callbackId: callbackId
        };
        WeixinJSCore.invoke(msg);
        
    };
    
    var event_cb_map = {};
    
    var subscribe = function(event, callback) {
        event_cb_map[event] = callback;
    };
    
    var processCallbackFromNative = function (data) {
        if (typeof invokeCallbacks[data.callbackId] === 'function') {
            invokeCallbacks[data.callbackId](data.data);
            delete invokeCallbacks[data.callbackId];
        } else {
            console.log("invoke callback not function: " + data.callbackId);
        }
        return;
    };
    
    
    var processEventFromNative = function (data) {
        if (!(data.event in event_cb_map)) {
            return;
        }
        event_cb_map[data.event](data.data);
        
    };
    
    global.WeixinJSBridge = {
        invoke : invoke,
        on : subscribe,
        _processCallbackFromNative: processCallbackFromNative,
        _processEventFromNative:processEventFromNative
    }
})(this);

