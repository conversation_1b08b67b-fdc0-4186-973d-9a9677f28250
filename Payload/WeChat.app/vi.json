{"BindCard_Gender_Female": "<PERSON><PERSON>", "BindCard_Gender_Male": "Nam", "Choose_Deposit_Time": "Cài đặt thời gian", "Choose_Payment": "<PERSON><PERSON><PERSON> thức nạp tiền", "Continue_Pay": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h toán", "Day": "{}", "Each_Day_In_Month_Deposit": "{} hàng tháng", "Each_WeekDay_Deposit": "Cứ {}", "ExposureInfo_Waiting_Wording": "<PERSON><PERSON> lòng chờ...", "Fetch_Balance": "<PERSON><PERSON><PERSON>", "Fetch_Balance_Bank_Proccessing": "<PERSON><PERSON> hàng đang xử lý", "Fetch_Balance_Open_Order": "<PERSON><PERSON><PERSON>", "Fetch_Balance_Success": "<PERSON><PERSON> đến thành công", "FillCard_Info_ErrorTips_Format": "{} (tổng {} mục lỗi)", "FillCard_Number_Default_Mobile_Modify_Tips": "<PERSON><PERSON><PERSON> số điện thoại di động không ch<PERSON>h x<PERSON>c, chạm để chỉnh sửa.", "FillCard_Number_Reg_Hint": "S<PERSON> thẻ ngân hàng của bạn", "FillCard_Number_Unreg_Hint": "<PERSON><PERSON> thẻ ngân hàng của chủ tài khoản ___<BRAND>___", "Friday": "<PERSON><PERSON><PERSON>", "Give_Up": "Bỏ qua", "HHC_Check_PWD_To_Add_Plan": "<PERSON><PERSON><PERSON><PERSON> mật khẩu thanh toán để bắt đầu Chi tiêu & T<PERSON><PERSON><PERSON> kiệm", "HHC_Check_PWD_To_Edit_Plan": "<PERSON><PERSON><PERSON><PERSON> mật khẩu thanh toán để sửa đổi Chi tiêu & <PERSON><PERSON><PERSON><PERSON> kiệm", "HHC_Check_PWD_To_Pause_Plan": "<PERSON><PERSON><PERSON><PERSON> mật khẩu thanh toán để vô hiệu hóa Chi tiêu & <PERSON><PERSON><PERSON><PERSON> kiệm", "HHC_Check_PWD_To_Start_Plan": "<PERSON><PERSON><PERSON><PERSON> mật khẩu thanh toán để bật Chi tiêu & <PERSON><PERSON><PERSON><PERSON> kiệm", "HHC_Choose_Payment": "<PERSON><PERSON><PERSON> thẻ", "HHC_Deposit_Plan": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON> n<PERSON> tiền", "HHC_Did_Modify": "Đã chỉnh sửa", "HHC_Did_Open": "<PERSON><PERSON> b<PERSON>t đầu", "HHC_Did_Pause": "Đ<PERSON> vô hiệu hóa", "HHC_Name": "<PERSON> tiê<PERSON> & <PERSON><PERSON><PERSON><PERSON> ki<PERSON>", "HHC_Plan_Check_Amount": "<PERSON><PERSON> tiền không hợp lệ. <PERSON><PERSON><PERSON> tra và thử lại.", "HHC_Plan_Set_Bank_Card_Tip": "<PERSON><PERSON> sử dụng Chi tiêu & <PERSON><PERSON><PERSON><PERSON>, trư<PERSON><PERSON> tiên hãy chọn thẻ nạp tiền.", "LQT_Fixed_Deposit": "<PERSON><PERSON><PERSON> tiền đ<PERSON><PERSON>", "LQT_Fixed_Deposit_Check_PWD_To_Add_Plan": "<PERSON><PERSON><PERSON><PERSON> mật khẩu thanh toán để bật nạp tiền đ<PERSON><PERSON> kỳ", "LQT_Fixed_Deposit_Check_PWD_To_Delete_Plan": "<PERSON><PERSON><PERSON><PERSON> mật khẩu thanh toán để xóa lịch biểu.", "LQT_Fixed_Deposit_Check_PWD_To_Pause_Plan": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u thanh toán để tạm dừng lịch biểu", "LQT_Fixed_Deposit_Check_PWD_To_Start_Plan": "<PERSON><PERSON><PERSON><PERSON> mật khẩu thanh toán để bật lịch biểu.", "LQT_Fixed_Deposit_Did_Delete": "Đã xóa tất cả", "LQT_Fixed_Deposit_Did_Modify": "<PERSON><PERSON> cập nh<PERSON>t", "LQT_Fixed_Deposit_Did_Open": "<PERSON><PERSON> bật", "LQT_Fixed_Deposit_Did_Pause": "<PERSON><PERSON> tạm ngưng", "LQT_Fixed_Deposit_No_Plan": "<PERSON><PERSON><PERSON><PERSON> có lịch nạp tiền", "LQT_Fixed_Deposit_Plan": "<PERSON><PERSON><PERSON> t<PERSON>", "LQT_Fixed_Deposit_Plan_Set_Bank_Card_Tip": "<PERSON><PERSON> cài đặt nạp tiền định kỳ, trướ<PERSON> tiên hãy chọn một thẻ để khấu trừ tiền.", "LQT_Fixed_Deposit_Plan_Set_Time_Tip": "<PERSON><PERSON> cài đặt nạp tiền đ<PERSON><PERSON> kỳ, trư<PERSON><PERSON> tiên hãy chọn thời gian nạp tiền.", "LQT_Fixed_Deposit_Plan_Should_Input": "<PERSON><PERSON> cài đặt nạp tiền đị<PERSON> kỳ, trư<PERSON><PERSON> tiên hãy nhập số tiền nạp.", "ModifyPwdUseCase_ModifyPwd_Desc": "<PERSON><PERSON><PERSON><PERSON> mật khẩu thanh toán để xác minh danh tính", "ModifyPwdUseCase_ModifyPwd_GiveUp_Title": "Hủy bỏ thay đổi mật khẩu thanh toán của bạn?", "ModifyPwdUseCase_ModifyPwd_Success": "<PERSON><PERSON> thay đổi mật kh<PERSON>u", "ModifyPwdUseCase_ModifyPwd_Title": "<PERSON><PERSON><PERSON> mật kh<PERSON>u", "Monday": "<PERSON><PERSON><PERSON>", "Monthly": "<PERSON><PERSON><PERSON>g", "OfflinePay_CreateOfflinePay_ConfirmBtn_Title": "<PERSON><PERSON><PERSON> b<PERSON>y giờ", "OfflinePay_CreateOfflinePay_Euro_Tips": "<PERSON><PERSON><PERSON> b<PERSON>t <PERSON> toán nhanh. <PERSON><PERSON> <PERSON><PERSON> b<PERSON><PERSON>, mã hiển thị để thu ngân nhanh chóng thanh toán. (Chỉ hỗ trợ giao dịch bằng CNY)", "OfflinePay_CreateOfflinePay_Tips": "<PERSON><PERSON><PERSON> bật <PERSON> toán nhanh. <PERSON><PERSON><PERSON> b<PERSON>, bạn có thể hiển thị mã để nhanh chóng thanh toán cho người bán hàng.", "OfflinePay_ReCreateOfflinePay_ConfirmBtn_Title": "<PERSON><PERSON><PERSON>", "OfflinePay_ReCreateOfflinePay_Euro_Tips": "<PERSON>h toán nhanh chưa được bật. <PERSON><PERSON><PERSON> hoạt tính năng để giúp người bán thanh toán nhanh chóng và dễ dàng - chỉ cần hiển thị mã và bắt đầu. Chỉ hỗ trợ các giao dịch CNY.", "OfflinePay_ReCreateOfflinePay_Tips": "<PERSON><PERSON> toán nhanh chưa đư<PERSON><PERSON> bật. <PERSON><PERSON><PERSON> ho<PERSON> tính năng để giúp người bán thanh toán nhanh chóng và dễ dàng - chỉ cần hiển thị mã và bắt đầu.", "Saturday": "<PERSON><PERSON><PERSON>", "Sunday": "Chủ Nhậ<PERSON>", "Thursday": "<PERSON><PERSON><PERSON>", "Tuesday": "<PERSON><PERSON><PERSON>", "WCPay_BankCardBindTo0_0_5D_Detail": "$0,05 sẽ được khấu trừ để xác minh tài k<PERSON>n", "WCPay_BankCardBindTo0_0_5D_Detail_back": "$0,05 sẽ được khấu trừ để xác minh tài khoản và sẽ được hoàn lại sau khi xác minh.", "WCPay_BankCardBindTo1B_Detail": "¥0,01 sẽ được khấu trừ để xác minh tài khoản và sẽ được hoàn lại sau khi xác minh.", "WCPay_BankCardBindTo1B_NotReturnDetail": "¥0,01 sẽ bị khấu trừ để xác minh tài khoản của bạn hợp lệ", "WCPay_CountryCode_Title": "Quốc gia/khu vực", "WCPay_FaceID_Auth_Tip": "<PERSON><PERSON><PERSON> minh khuôn mặt để thanh toán", "WCPay_GiveUpReset_Title": "Dừng đặt lại mật khẩu thanh toán?", "WCPay_NeedChangeCard_Error_Btn": "<PERSON>hay đ<PERSON>i ph<PERSON><PERSON><PERSON> thức thanh toán", "WCPay_TouchID_Auth_Tip": "<PERSON><PERSON><PERSON> minh dấu vân tay hiện có để thanh toán", "WCPay_TouchID_Confirm_Alert_Cancel": "Hủy bỏ", "WCPay_TouchID_Confirm_Alert_Content": "Dấu vân tay đã đư<PERSON>c xác minh. <PERSON><PERSON><PERSON> nh<PERSON>n thanh toán?", "WCPay_TouchID_Confirm_Alert_OK": "<PERSON><PERSON><PERSON>", "WCPay_TouchID_Confirm_Alert_Title": "<PERSON><PERSON> toán", "WeChatPay_Name": "___<BRAND_Pay>___", "Wednesday": "<PERSON><PERSON><PERSON>", "Weekly": "<PERSON><PERSON><PERSON> t<PERSON>", "address_item_key": "Địa chỉ", "address_item_place_holder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "agree": "Đồng ý", "agree_user_protocal": "<PERSON><PERSON><PERSON> nhận Thỏa thuận người dùng", "agreement_alert": "<PERSON><PERSON><PERSON><PERSON><PERSON> ti<PERSON>, vui lòng xem và đồng ý với \"Thỏa thuận người dùng\".", "alertChargeFee": "<PERSON><PERSON> vụ", "area_item_key": "<PERSON><PERSON><PERSON><PERSON>", "ask_verify_fingerprint": "<PERSON><PERSON><PERSON> minh dấu vân tay", "assign_pay_dialog_content": "Thông tin nhận dạng đã nhập khác với thông tin được liên kết với tài khoản ___<BRAND>___ hiện tại. Sử dụng tài khoản ___<BRAND>___ đ<PERSON><PERSON><PERSON> liên kết với thẻ ngân hàng của bạn và xác minh lại.", "balance": "Số dư", "bank_card_item_key": "Thẻ ngân hàng", "bank_select_item_key": "<PERSON><PERSON> h<PERSON>", "bind_new_card": "Thê<PERSON> thẻ ngân hàng", "bind_new_card_section_footer": "<PERSON><PERSON> lý do bảo mật, các thẻ hiện đang được liên kết sẽ bị hủy liên kết.", "bind_new_card_section_header": "<PERSON><PERSON><PERSON> kết thẻ mới để truy xuất tài khoản", "bind_new_card_to_pay_tip": "<PERSON><PERSON><PERSON><PERSON> mật khẩu thanh toán để xác minh danh tính", "bind_new_card_to_reset_mobile_desc": "<PERSON>ên kết một số di động bằng cách liên kết một thẻ mới", "binded_card_list_page_title": "<PERSON><PERSON>n số di động để xác minh SMS", "birth_date_item_key": "<PERSON><PERSON><PERSON>", "can_not_bind_more_card": "Đã đạt đến giới hạn tối đa thẻ được liên kết.", "can_not_get_sms_with_question_mard_word": "Chưa nhận đư<PERSON>c mã xác minh?", "can_not_get_sms_word": "<PERSON><PERSON><PERSON> nhận đ<PERSON><PERSON><PERSON> mã xác minh", "cancel_time": "<PERSON><PERSON><PERSON><PERSON> gian h<PERSON>: {}", "cannot_receive_sms_code_content": "Mã SMS đã được gửi đến số điện thoại di động được liên kết với ngân hàng của bạn. Vui lòng xác nhận xem bạn có đang sử dụng số điện thoại di động đó và mã SMS chưa bị chặn bởi bất kỳ ứng dụng bảo mật nào. Nếu bạn không còn sử dụng số này, hãy liên hệ với nhân viên ngân hàng để được hỗ trợ. Để được trợ giúp thêm, hãy gọi đến ___<OfficialEntity_Service>___ theo số + 86-0755-95017.", "cannot_receive_sms_code_title": "<PERSON><PERSON><PERSON><PERSON> thể nhận mã x<PERSON>c minh", "card_holder_dialog_content": "1. <PERSON><PERSON> đảm bảo an toàn cho các khoản tiền, ID ___<BRAND>___ chỉ có thể liên kết các thẻ ngân hàng có chủ thẻ cùng tên.\n\n2. <PERSON><PERSON> liên kết một thẻ có chủ thẻ là tên kh<PERSON>, bạn phải cập nhật thông tin tên thật của mình.\n\n3. <PERSON>u khi thay đổi thông tin tên thật, thông tin chủ thẻ cũ sẽ bị xóa và bạn chỉ có thể thêm thẻ vào tên chủ thẻ mới.", "card_holder_dialog_title": "Tên trên thẻ", "card_holder_item_key": "Chủ thẻ", "card_holder_section_header": "<PERSON>h<PERSON>p thông tin bạn đã cung cấp trước đó cho ngân hàng. Chỉ có thể thêm thẻ ngân hàng có tên này trong tương lai.", "card_info_section_header": "<PERSON><PERSON><PERSON><PERSON> thông tin thẻ", "card_num_item_key": "Số thẻ", "card_number_input_tips_title": "<PERSON><PERSON><PERSON><PERSON> yêu cầu trả phí hoặc thanh toán qua ngân hàng trực tuyến", "card_select_item_key": "Loại thẻ", "card_type_section_header": "<PERSON><PERSON><PERSON> lo<PERSON>i thẻ", "change_realname_word": "<PERSON>hay đổi tên thật", "change_to_face_id": "Face Pay", "change_to_pwd": "Sử dụng mật kh<PERSON>u", "change_to_touch_id": "Sử dụng dấu vân tay", "check_pay_pwd_page_desc": "<PERSON><PERSON><PERSON><PERSON> mật khẩu thanh toán của bạn để xác nhận nhận dạng", "check_pay_pwd_page_title": "Xác minh Nhận dạng", "check_sms_desc": "Mã xác minh đã được gửi đến số điện thoại di động mà bạn đã đăng ký tại ngân hàng của mình.\n\n1. <PERSON><PERSON>c nhận đây là số điện thoại di động hiện tại của bạn và được đăng ký tại ngân hàng của bạn.\n\n2. <PERSON><PERSON>m tra xem SMS có bị ứng dụng bảo mật trên điện thoại di động của bạn chặn không.\n\n3. Nếu bạn không thể truy cập số điện thoại này ngay bây giờ, vui lòng liên hệ với ngân hàng của bạn.\n\n4. <PERSON><PERSON> được hỗ trợ thêm, hãy gọi đến Dịch vụ khách hàng theo số 95017.", "check_sms_page_desc": "<PERSON>i<PERSON><PERSON> liên kết thẻ ngân hàng yêu cầu xác minh qua SMS. Mã xác minh được gửi tới điện thoại {}. <PERSON><PERSON><PERSON> làm theo chỉ dẫn.", "check_sms_page_favor": "Bạn sẽ thanh toán {}{:.2f} (<PERSON><PERSON> tiết kiệm đượ<PERSON> {}{:.2f})", "check_sms_page_title": "<PERSON><PERSON><PERSON>h số di động", "common_back": "Quay lại", "common_cancel_word": "Hủy bỏ", "common_close": "Đ<PERSON><PERSON>", "common_done_word": "<PERSON><PERSON>", "common_drop_out_word": "<PERSON><PERSON><PERSON><PERSON>", "common_i_know_word": "<PERSON><PERSON> hiểu", "common_more": "<PERSON><PERSON><PERSON><PERSON>", "common_next_word": "<PERSON><PERSON><PERSON><PERSON>", "common_question_word": "Câu hỏi thường gặp", "common_select": "<PERSON><PERSON><PERSON>", "common_tip_word": "Nhắc nhở", "confirm_mobile_no": "<PERSON><PERSON><PERSON>n số di động", "confirm_pay": "<PERSON><PERSON> to<PERSON> ngay bây giờ", "confirm_to_receive": "<PERSON><PERSON><PERSON> nhận đã nhận", "confrim_pay_and_open_deduct_word": "Thanh toán & Bật", "confrim_pay_word": "<PERSON><PERSON> toán", "coupon_change_should_change_payment": "<PERSON><PERSON> tiền thanh toán đã thay đổi. <PERSON><PERSON> lòng chọn lại.", "coupon_component_need_bank_pay_tips": "<PERSON><PERSON><PERSON> khẩu cho ph<PERSON><PERSON><PERSON> thức thanh toán được chỉ định", "cre_id_item_key": "Số ID", "cre_id_item_place_holder": "<PERSON>hập số ID", "cre_type_item_key": "Loại ID", "cre_type_item_place_holder": "Chọn lo<PERSON>i ID", "cvv_dialog_content": "Mã CVV là 3 hoặc 4 số được in trên mặt sau của thẻ tín dụng ngân hàng", "cvv_dialog_title": "CVV là gì?", "cvv_item_key": "CVV ", "cvv_item_place_holder": "3 hoặc 4 số thường ở mặt sau của thẻ", "default_delay_transfer_confirm_desc": "Chuyển tiền sẽ tới trong vòng {} tiếng", "email_item_key": "Đ<PERSON>a chỉ email", "email_item_place_holder": "Nhập email", "error_detail_title": "<PERSON><PERSON> g<PERSON>", "face_hongbao": "<PERSON><PERSON><PERSON> bao xung quanh", "fast_bind_card_support_bank_title_text": "Hiện đang hỗ trợ các ngân hàng sau", "fill_card_and_user_info_title": "<PERSON><PERSON><PERSON>n thông tin nhận dạng và thẻ ngân hàng", "fill_card_info_card_holder_assign_pay_header": "<PERSON><PERSON><PERSON> được chỉ định chủ thẻ để thanh toán.", "fill_card_info_page_favor_desc": "Sử dụng loại thẻ ngân hàng này để tiết kiệm thêm {}{:.2f}.", "fill_card_info_page_realname_cre_not_support": "Không thể sử dụng {} để liên kết thẻ này", "fill_card_info_page_title": "<PERSON><PERSON><PERSON><PERSON> thông tin thẻ", "fill_card_num_format_error": "Số thẻ không hợp lệ", "fill_card_num_of_card_holder_section_header": "<PERSON><PERSON><PERSON><PERSON> số thẻ ngân hàng của chủ thẻ", "fill_card_num_page_desc": "<PERSON><PERSON><PERSON> kết thẻ ngân hàng", "fill_card_num_page_favor_dialog_title": "<PERSON><PERSON><PERSON><PERSON> kiệm nhiều hơn với thẻ này", "fill_card_num_page_realname_desc": "Bạn phải thêm thẻ ngân hàng để hoàn tất xác thực tên thật.", "fill_card_num_page_sns_input_hint": "Chỉ hỗ trợ thẻ ghi nợ", "fill_card_num_page_title": "Thê<PERSON> thẻ ngân hàng", "fill_card_number_assign_pay": "Sử dụng thẻ ngân hàng của {} để thanh toán", "fill_card_number_more_favor": "Sử dụng thẻ ngân hàng được chỉ định để được giảm giá", "fill_complete_name": "<PERSON><PERSON><PERSON><PERSON> họ tên đ<PERSON>y đủ", "fill_id_format_error": "<PERSON><PERSON><PERSON> dạng số ID không đúng.", "fill_in_sms_key": "<PERSON><PERSON> x<PERSON>c <PERSON>h", "fill_in_sms_word": "<PERSON><PERSON><PERSON><PERSON> mã x<PERSON>c minh", "fill_phone_num_format_error": "<PERSON><PERSON><PERSON> dạng số di động không đúng.", "finger_print_err_tips": "Thử lại", "first_name_item_key": "<PERSON><PERSON><PERSON>", "first_name_item_place_holder": "<PERSON><PERSON><PERSON><PERSON> tên bạn", "float_paydesk_modal_no_select_favor": "Chưa sử dụng <PERSON> kh<PERSON>u", "foreign_mobile_header": "<PERSON><PERSON><PERSON><PERSON> số di động mới", "forget_pay_pwd_title": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u thanh toán", "get_sms_with_count_down_word": "<PERSON><PERSON><PERSON>n mã xác minh \n({})", "get_sms_word": "<PERSON>hận mã x<PERSON>c minh", "give_up_on_new_card": "<PERSON><PERSON><PERSON> liên kết thẻ?", "give_up_this_order_or_not": "Bỏ qua giao dịch này?", "group_aa": "<PERSON><PERSON> h<PERSON><PERSON> đơn", "has_send_sms": "Đ<PERSON> gửi", "has_send_sms_with_count": "<PERSON><PERSON> gửi ({})", "hongbao_refund_way_header_title": "<PERSON><PERSON><PERSON> bao không được mở trong vòng 24 giờ sau khi gửi sẽ được hoàn trả theo phương thức dưới đây.", "hongbao_refund_way_title": "<PERSON><PERSON><PERSON> tiền Hồng bao đến", "id_card_name": "Thẻ ID", "install_cert_error": "<PERSON><PERSON><PERSON><PERSON> thể cài đặt chứng nhận", "last_name_item_key": "Họ", "last_name_item_place_holder": "<PERSON><PERSON><PERSON><PERSON>", "loading_title": "<PERSON><PERSON> tả<PERSON>...", "lottery_network_error": "<PERSON><PERSON><PERSON>, chúc bạn lần sau may mắn!", "lqt": "<PERSON>u<PERSON> nhỏ", "lqt_reset_mobile_desc": "<PERSON>ọn một thẻ ngân hàng. <PERSON><PERSON><PERSON>h <PERSON>uỹ nhỏ của bạn bằng số điện thoại di động được ngân hàng phát hành thẻ đó nắm giữ.", "mobile_dialog_content": "Số điện thoại di động đã đăng ký trên tài khoản ngân hàng của bạn là số bạn đã cung cấp cho ngân hàng khi mở tài khoản. Nếu bạn không cung cấp số điện thoại di động cho ngân hàng, bạn đã quên nó hoặc không thể truy cập đư<PERSON><PERSON> nữa, hãy liên hệ với ngân hàng của bạn và cập nhật số điện thoại di động.", "mobile_dialog_title": "Số di động", "mobile_item_key": "Số di động", "mobile_item_place_holder": "<PERSON><PERSON><PERSON><PERSON> số điện thoại di động do ngân hàng nắm giữ", "name_item_key": "<PERSON><PERSON><PERSON>", "name_item_place_holder": "<PERSON><PERSON><PERSON><PERSON> tên trên thẻ", "nationality_item_key": "Quốc gia/khu vực", "nationality_place_holder": "<PERSON><PERSON><PERSON><PERSON> quốc gia/khu vực", "new_mobile_item_key": "Số di động mới", "new_mobile_item_place_holder": "<PERSON><PERSON><PERSON><PERSON> số điện thoại di động do ngân hàng nắm giữ", "new_user_card_num_input_safety_desc": "<PERSON><PERSON><PERSON> kết thẻ thuộc chủ sở hữu tài khoản ___<BRAND>___", "new_user_card_num_input_safety_desc_v2": "<PERSON><PERSON><PERSON> kết thẻ thuộc chủ sở hữu tài khoản ___<BRAND>___", "no": "K<PERSON>ô<PERSON>", "offline_choose_payment": "<PERSON><PERSON><PERSON> thức thanh toán mặc định", "offline_choose_payment_fail": "<PERSON><PERSON><PERSON> phương thức thanh toán mặc định không thành công, h<PERSON><PERSON> thử các phương thức khác để hoàn thành thanh toán.", "offline_click_view_code": "<PERSON><PERSON>m để xem mã thanh toán", "offline_pay_modify_limit": "Chỉnh sửa số tiền", "offline_pay_only_pay_title": "<PERSON><PERSON> toán", "offline_pay_select_card_invalid": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán đã chọn hiện không khả dụng. <PERSON><PERSON> lòng thử một ph<PERSON><PERSON>ng thức thanh toán kh<PERSON>c.", "offline_pay_title": "<PERSON><PERSON><PERSON><PERSON>", "offline_pay_to_merchant": "<PERSON>h toán cho vendor", "offline_prefer_payment": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán mặc định", "offline_show_code_warning": "Chỉ sử dụng mã số thanh toán để hiển thị cho nhân viên thu ngân khi thanh toán. V<PERSON> sự bảo mật, vui lòng không chia sẻ mã số này với bất kỳ ai khác.", "offline_view_code_warning": "Chỉ sử dụng mã số thanh toán để hiển thị cho nhân viên thu ngân khi thanh toán. V<PERSON> sự bảo mật, vui lòng không chia sẻ mã số này với bất kỳ ai khác.", "ok": "OK", "order_address_section_header": "<PERSON><PERSON><PERSON> chỉ thanh toán", "pay_card_detail_contact_user_info": "<PERSON><PERSON><PERSON> <PERSON> v<PERSON> kh<PERSON>ch hàng ", "pay_card_detail_tel_number": "95017", "pay_power_by_tenpay_word": "<PERSON><PERSON><PERSON><PERSON> cung cấp bởi Tenpay", "pay_success": "<PERSON><PERSON> to<PERSON> thành công", "paydesk_coupon_page_title": "<PERSON><PERSON> kh<PERSON>u thanh toán", "paydesk_float_page_title": "<PERSON><PERSON><PERSON>h to<PERSON>", "paydesk_main_page_more_favor": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON><PERSON> kh<PERSON>u", "paydesk_main_page_title": "Trang thanh toán", "paydesk_payment_page_title": "<PERSON>h sách thẻ thanh toán", "paydesk_sub_page_title": "Trang phụ thanh toán", "payee_remark_title": "<PERSON><PERSON><PERSON><PERSON> xét về người trả tiền", "paying_alert_tips": "<PERSON><PERSON> gửi thanh toán. <PERSON><PERSON><PERSON> thông báo kết quả thanh toán để kiểm tra xem bạn có cần gửi lại thanh toán không.", "payment_method": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "phone_number_item_key": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i", "phone_number_item_place_holder": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "profession_item_key": "<PERSON><PERSON><PERSON>", "pure_bind_card_succ_tips": "<PERSON><PERSON><PERSON> kết thành công", "pwd_repeat_error_tip": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp", "rebind_bank_card_section_header": "<PERSON><PERSON><PERSON> kết lại thẻ để truy xuất tài khoản", "receipt": "<PERSON><PERSON><PERSON><PERSON> tiền", "receive_done": "Đã nhận", "receive_remark_title": "<PERSON><PERSON>ậ<PERSON> xét về giấy biên nhận", "receive_time": "<PERSON>h<PERSON><PERSON> gian nhận: {}", "receiver_remark_title": "<PERSON><PERSON><PERSON><PERSON> xét về người nhận tiền", "refund_doing_tip": "<PERSON><PERSON> xử lý hoàn tiền. Số tiền sẽ được hoàn trả vào thẻ của bạn sau 1-3 ng<PERSON><PERSON> làm việc.", "refund_done": "<PERSON><PERSON> hoàn tiền", "refund_done_and_expired": "<PERSON><PERSON> trả lại (hết hạn)", "refund_time": "<PERSON><PERSON><PERSON><PERSON> gian trả lại: {}", "refund_transfer_or_not": "T<PERSON><PERSON> lại chuyển tiền từ {}?", "refund_word": "<PERSON><PERSON><PERSON> l<PERSON>", "renewal_time_item_key": "<PERSON><PERSON> lần thay đổi", "resend_message_or_not": "G<PERSON>i lại tin nhắn này?", "resend_sms": "<PERSON><PERSON><PERSON> l<PERSON>i", "resend_sms_with_count": "<PERSON><PERSON><PERSON> lại ({})", "resend_success_tip": "<PERSON><PERSON> gửi lại tin nh<PERSON>n", "resend_word": "<PERSON><PERSON><PERSON> l<PERSON>i", "reset_ban_mobile_fill_card_info_credit_tip_header": "<PERSON><PERSON><PERSON><PERSON> thông tin thẻ ngân hàng để kiểm tra", "reset_ban_mobile_fill_card_num_tip_header": "Thêm thẻ ngân hàng mới và sử dụng số điện thoại di động do ngân hàng của bạn giữ để xác minh thanh toán Số dư.", "reset_cvv_and_valid_date_tip": "Bạn đang cập nhật thông tin thẻ được liên kết và thực hiện thanh toán cùng một lúc. <PERSON><PERSON>u bạn không chắc chắn về bất kỳ điều gì, hãy gọi cho dịch vụ khách hàng của ngân hàng của bạn: ", "reset_cvv_title": "Thay đổi CVV", "reset_lqt_mobile_fill_card_num_tip_header": "Thêm thẻ mới và sử dụng số điện thoại di động đã đăng ký của thẻ đó để hoàn tất xác minh SMS cho Quỹ nhỏ.", "reset_mobile_bank_card_number": "Thẻ", "reset_mobile_card_desc_format": "{}{} ({}) <PERSON>ố điện thoại di động đã đăng ký", "reset_mobile_card_desc_with_update_format": "{}{} ({}) Số điện thoại di động đã đăng ký. ", "reset_mobile_new_mobile_info_btn": "<PERSON> ti<PERSON>", "reset_mobile_new_mobile_number": "Số di động mới", "reset_mobile_phone_page_title": "Chỉnh sửa số di động", "reset_phone_tip": "Bạn có thể thanh toán khi đã xác minh nhận dạng. <PERSON><PERSON> xác nhận số điện thoại do ngân hàng của bạn gi<PERSON>, hãy gọi ", "reset_pwd_fill_rebind_card_info_page_title": "<PERSON><PERSON><PERSON><PERSON> thông tin thẻ ngân hàng", "reward": "Mã phần thưởng", "safety_dialog_content": "<PERSON><PERSON><PERSON> biện pháp bảo mật: <PERSON><PERSON><PERSON> vệ tài <PERSON>, theo dõi thời gian thự<PERSON>, <PERSON><PERSON><PERSON> băng khẩn cấp. \n\n<PERSON><PERSON><PERSON> minh hai bước: <PERSON><PERSON><PERSON> khẩu thanh toán của bạn là bắt buộc đối với mỗi thanh toán. <PERSON><PERSON><PERSON> <PERSON>h qua SMS là bắt buộc đối với các khoản thanh toán lớn. \n\nBảo vệ bảo mật: Sử dụng mã hóa dữ liệu mạnh để bảo vệ dữ liệu người dùng. \n\n<PERSON><PERSON><PERSON> hiểm thanh toán: <PERSON><PERSON><PERSON> khoản thanh toán được PICC bảo hiểm.", "safety_dialog_title": "<PERSON><PERSON><PERSON> v<PERSON> an toàn", "scan_card_num_title": "<PERSON><PERSON><PERSON> thẻ", "select_payment": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "select_payment_card": "<PERSON><PERSON><PERSON> thức thanh toán", "send_verify_code_btn_wording": "<PERSON><PERSON><PERSON>", "send_verify_code_switch_btn_wording": "<PERSON>hay đ<PERSON>i ph<PERSON><PERSON><PERSON> thức xác minh", "send_verify_code_tips_format": "<PERSON><PERSON> x<PERSON>c minh SMS sẽ được gửi tới: \n{}", "set_pay_pwd_confirm_page_title": "<PERSON><PERSON><PERSON><PERSON> lại để xác nhận", "set_pay_pwd_page_desc": "<PERSON><PERSON><PERSON> một mật khẩu ___<BRAND_Pay>___ để xác minh thanh toán của bạn", "set_pay_pwd_page_title": "<PERSON>ài đặt M<PERSON>t kh<PERSON>u thanh toán", "set_pwd_success": "<PERSON><PERSON> cập nhật cài đặt", "succ_page_open_biometric_cancel_btn_title": "Không phải bây giờ", "succ_page_open_biometric_dialog_content": "Bạn có thể bật thanh toán bằng khuôn mặt hoặc vân tay để thanh toán nhanh hơn.", "succ_page_open_biometric_faceid_btn_title": "<PERSON>h toán bằng khuôn mặt", "succ_page_open_biometric_touchid_btn_title": "Thanh toán bằng vân tay", "succ_page_open_face_id_dialog_content": "Bật <PERSON>h toán bằng khuôn mặt để sử dụng nhận dạng khuôn mặt để hoàn tất thanh toán nhanh chóng và an toàn.", "succ_page_open_face_id_right_btn_title": "<PERSON><PERSON><PERSON> toán bằng khuôn mặt", "succ_page_open_touch_id_dialog_content": "B<PERSON>t <PERSON> toán bằng chạm để sử dụng nhận dạng dấu vân tay để hoàn tất thanh toán nhanh chóng và an toàn.", "succ_page_open_touch_id_left_btn_title": "<PERSON><PERSON> thể sau", "succ_page_open_touch_id_right_btn_title": "<PERSON><PERSON><PERSON> toán bằng chạm", "to_be_confirm_receive": "<PERSON><PERSON><PERSON><PERSON> nhận chưa đ<PERSON><PERSON> xác nhận", "transfer": "<PERSON><PERSON><PERSON><PERSON> tiền", "transfer_account": "<PERSON><PERSON> tiền chuyển", "transfer_amount_input_invalid_hint": "<PERSON><PERSON> tiền đã nhập không đúng", "transfer_bank": "Chuyển tiền tới thẻ ngân hàng", "transfer_explain": "<PERSON><PERSON><PERSON><PERSON> ghi chú chuyển tiền", "transfer_modify_explain": "<PERSON><PERSON> đ<PERSON>i", "transfer_second_left_button": "Hủy bỏ", "transfer_second_right_button": "<PERSON><PERSON><PERSON><PERSON>", "transfer_second_title": "Nhắc nhở về chuyển tiền", "transfer_time": "<PERSON><PERSON><PERSON> chuyển tiền: {}", "transfer_ui_title": "<PERSON><PERSON><PERSON><PERSON> tiền cho bạn bè", "understand_safety": "<PERSON><PERSON><PERSON> v<PERSON> an toàn", "update_word": "<PERSON><PERSON><PERSON>", "user_card_type_select_placeholder_v2": "Chọn thẻ ngân hàng và loại thẻ", "user_info_section_header": "Nhập thông tin cá nhân", "user_protocal": "\"Thỏa thuận người dùng\"", "valid_date_item_key": "<PERSON><PERSON><PERSON>", "verify_cre_tip": "Nhập 4 số cuối của {} {} để xác minh nhận dạng", "verify_fingerprint_fail": "<PERSON><PERSON><PERSON> minh bằng dấu vân tay thất bại", "verify_id_ui_true_name_tips": "{} (<PERSON><PERSON><PERSON><PERSON> họ tên đầy đủ)", "wechat_bank_agreement": "Thỏa thu<PERSON>n <PERSON> hàng", "wechat_mobile_phone_word": "Số di động đã liên kết với ___<BRAND>___", "wechat_user_agreement": "Thỏa thuận người dùng ___<BRAND_Pay>___", "wxp_common_cancel": "Hủy bỏ", "wxp_common_confirm": "OK", "wxp_common_i_know": "<PERSON><PERSON> hiểu", "wxp_common_remind": "Nhắc nhở", "wxp_network_error": "<PERSON><PERSON> thống bận. <PERSON><PERSON><PERSON> lại sau.", "wxp_payment_network_error": "<PERSON>iao dịch đã gửi. Bạn sẽ nhận được thông báo trạng thái thanh toán từ Tài khoản chính thức của ___<BRAND_Pay>___. <PERSON><PERSON><PERSON> nhận trạng thái thanh toán trước khi gửi lại thanh toán nếu cần thiết.", "wxp_system_error": "<PERSON><PERSON> thống bận. <PERSON><PERSON><PERSON> lại sau.", "wxp_wcpay_system_error": "<PERSON><PERSON> thống bận. <PERSON><PERSON><PERSON> lại sau.", "yes": "<PERSON><PERSON>", "zip_item_key": "<PERSON><PERSON> b<PERSON>u đi<PERSON>n", "zip_item_place_holder": "<PERSON><PERSON><PERSON><PERSON> mã b<PERSON>u đi<PERSON>n", "common_confirm_word": "<PERSON><PERSON><PERSON>", "ModifyPwdUseCase_ModifyPwd_GiveUp_Yes": "Bỏ thay đổi", "ModifyPwdUseCase_ModifyPwd_GiveUp_No": "<PERSON><PERSON><PERSON><PERSON>", "disagree": "<PERSON><PERSON><PERSON><PERSON> đồng <PERSON>", "card_user_agreement": "Thỏa thuận <PERSON><PERSON> vụ <PERSON> dùng", "card_bank_agreement": "Thỏa thu<PERSON>n <PERSON> hàng", "Card_UserAgreement_Title": "Bạn phải đồng ý với thỏa thuận dưới đây để thêm thẻ ngân hàng.", "pay_settings_delay_transfer_page_title": "<PERSON><PERSON><PERSON><PERSON> gian chuyển tiền", "pay_settings_delay_transfer_page_desc": "<PERSON>u khi đã chấ<PERSON>, số tiền sẽ được gửi vào Số dư của người dùng khác vào thời gian sau đó. Không thể thu hồi chuyển tiền sau khi gửi, vì vậy hãy kiểm tra kỹ thông tin của người nhận trước khi chuyển tiền.", "pay_settings_delay_transfer_no_delay": "<PERSON><PERSON><PERSON> thì", "pay_settings_delay_transfer_two_hour": "Trong 2 tiếng", "pay_settings_delay_transfer_one_day": "Trong 24 tiếng", "pay_settings_biometric_pay_enabled": "<PERSON><PERSON> bật", "pay_settings_biometric_pay_disabled": "Đã tắt", "pay_settings_biometric_pay_multi_support_title": "Thanh toán bằng dấu vân tay/quét mặt", "pay_settings_biometric_pay_faceid_enabled": "<PERSON><PERSON> bật thanh toán bằng khuôn mặt", "pay_settings_biometric_pay_touchid_enabled": "<PERSON><PERSON> bật t<PERSON>h năng thanh toán bằng dấu vân tay", "pay_settings_biometric_pay_multi_support_desc": "<PERSON><PERSON> <PERSON><PERSON> b<PERSON><PERSON>, bạn có thể sử dụng xác minh khuôn mặt hoặc vân tay để thực hiện thanh toán n<PERSON>h hơn.", "f2f_pay_extrabuy_detail_modal_original_price": "(<PERSON><PERSON><PERSON> gốc ¥{:.2f})", "common_button": "<PERSON><PERSON><PERSON>", "Accessibility_Type_SwitchView_Selected": "{}, nút chuyển đổi, bật", "Accessibility_Type_SwitchView_UnSelected": "{}, nút chuyển đổi, tắt", "YunShanFu_Loading_Wording": "<PERSON><PERSON> mở QuickPass...", "YunShanFu_Uninstalled_Error": "Bạn chưa cài đặt QuickPass. H<PERSON><PERSON> cài đặt rồi thử lại hoặc tiếp tục thanh toán bằng ___<BRAND_Pay>___.", "OfflinePay_Setting_CloseOfflinePay_AlertMessage_ShowFeaturePassword": "<PERSON><PERSON> đã bật kh<PERSON><PERSON> bảo mật, cần thiết phải có phương pháp mở khóa đã chọn để truy cập <PERSON> toán nhanh giúp bảo mật thanh toán của bạn. Tắt?", "OfflinePay_Setting_CloseOfflinePay_AlertMessage": "T<PERSON><PERSON> toán nhanh?", "OfflinePay_Setting_CloseOfflinePay_BtnTitle": "Tắt", "OfflinePay_Setting_CloseOfflinePay_Cancel": "<PERSON><PERSON><PERSON><PERSON>", "OfflinePay_Setting_CloseOfflinePay_OpenWalletLock": "Cài đặt <PERSON><PERSON><PERSON><PERSON> bảo mật", "Wallet_Mix_Paid_UnKnown_Error": "<PERSON><PERSON> gửi yêu cầu giao dịch. Bạn sẽ nhận được thông báo trạng thái từ Tài khoản chính thức của ___<BRAND_Pay>___ Hong Kong. Không thực hiện lại thanh toán cho tới khi trạng thái thanh toán được xác nhận.", "bank_card_info": "<PERSON><PERSON><PERSON>", "HHC_Did_Add": "<PERSON><PERSON> thêm", "VoiceOver_OfflinePay_barCode": "<PERSON>ã vạch thanh toán, có thể được hiển thị cho nhân viên thu ngân. Nhấn đúp để hiển thị mã thanh toán trên toàn màn hình", "VoiceOver_OfflinePay_barCode_short": "<PERSON><PERSON> thanh toán", "VoiceOver_OfflinePay_Qrcode": "Mã QR thanh toán", "VoiceOver_OfflinePay_barcode_clickHint": "<PERSON><PERSON><PERSON>n đ<PERSON><PERSON> để quay lại", "VoiceOver_OfflinePay_Qrcode_clickHint": "<PERSON><PERSON><PERSON>n đúp để hiển thị toàn màn hình", "common_link": "<PERSON><PERSON><PERSON>", "Accessibility_Collapse_Header_Collapsed": "{}, đã thu gọn", "Accessibility_Collapse_Header_Showed": "{}, đã mở rộng", "Pay_Android_Fingerprint_Prompt_Title": "<PERSON><PERSON><PERSON> minh dấu vân tay", "Pay_Android_Fingerprint_Prompt_SubTitle": "đ<PERSON> hoàn tất thanh toán.", "Pay_Android_Fingerprint_Prompt_Button": "Sử dụng mật kh<PERSON>u", "Accessibility_Collapse_Header_Collapsed({}": "<PERSON><PERSON>", "Accessibility_Collapse_Header_Showed({}": "<PERSON><PERSON><PERSON> thị tất cả", "Accessibility_State_Disabled": "<PERSON><PERSON><PERSON>", "Accessibility_String_PwdInputView": "Password box, 6 digits in total, {} digits entered", "Accessibility_Type_Button": "{}, button", "Accessibility_Type_CheckBox_Selected": "Check box, selected, {}, button", "Accessibility_Type_CheckBox_UnSelected": "Check box, unselected, {}, button", "Accessibility_Type_Selected": "Selected, {}, button", "Accessibility_Type_UnSelected": "Unselected, {}, button", "common_check_box_check": "Check box, selected", "common_check_box_uncheck": "Check box, unselected", "OfflinePay_ChangePayment_UnConnectedNetwork_Tips": "<PERSON><PERSON>ng không khả dụng. Không thể chọn ph<PERSON><PERSON><PERSON> thức thanh to<PERSON>.", "OfflinePay_EnablePage_UnConnectedNetwork_Tips": "<PERSON>ạng không khả dụng. <PERSON><PERSON><PERSON> thử lại sau.", "Fetch_Balance_To_Bank": "<PERSON><PERSON><PERSON> t<PERSON> về", "Fetch_Balance_Amount": "<PERSON><PERSON> tiền có thể rút", "Fetch_Balance_Amount_Tips": "Số dư: ¥{}.", "Fetch_Balance_Amount_Exceed": "Số tiền đã nhập v<PERSON><PERSON><PERSON> quá Số dư khả dụng", "Fetch_Balance_Fetch_All": "<PERSON><PERSON><PERSON> tiền tất cả", "HoneyPay_CheckPwd_Unbind_Title": "<PERSON><PERSON><PERSON> li<PERSON><PERSON> kết Relative Card", "HoneyPay_Modify_CreditLimit_Title": "Chỉnh sửa giới hạn hàng tháng", "HoneyPay_Modify_CreditLimit_Desc": "<PERSON><PERSON><PERSON><PERSON> hạn hàng tháng", "HoneyPay_Modify_CreditLimit_Max_Alert": "<PERSON><PERSON> tiền không thể vượt quá ¥{:.2f}", "balance_entry_balnce_title": "Số dư của tôi", "balance_entry_balnce_detail": "<PERSON><PERSON><PERSON>", "balance_entry_powered_by_tenpay": "<PERSON><PERSON><PERSON><PERSON> cung cấp", "balance_recharge_page_title": "<PERSON><PERSON><PERSON> t<PERSON>", "balance_recharge_card_info_title": "<PERSON><PERSON><PERSON><PERSON> thức n<PERSON> tiền", "balance_recharge_payment_new_card": "Thê<PERSON> thẻ mới", "HoneyPay_Add_Card": "Tặng Relative Card", "HoneyPay_Select_Contact_Title": "<PERSON><PERSON><PERSON> bạ", "HoneyPay_Modify_Comment": "Chỉnh sửa bình luận", "HoneyPay_MoneyInput_Hint": "<PERSON><PERSON><PERSON><PERSON> số tiền", "HoneyPay_CreateCard_Btn": "<PERSON><PERSON><PERSON>", "HoneyPay_Max_Amount_Notice": "<PERSON><PERSON> tiền không thể vượt quá ¥{:.2f}", "HoneyPay_Modify_Credit": "<PERSON><PERSON><PERSON><PERSON> hạn hàng tháng", "HoneyPay_Main_Title": "Relative Card", "hbrefund_info_tips": "<PERSON><PERSON><PERSON>", "hbrefund_set_button": "Cài đặt", "hbrefund_time_title": "<PERSON><PERSON><PERSON><PERSON> gian hoàn tiền hồng bao", "hbrefund_forbid_way": "<PERSON><PERSON><PERSON><PERSON> thức hoàn tiền này không còn được hỗ trợ.", "hbrefund_had_set": "<PERSON>ài đặt thành công", "hbrefund_origin_desc": "<PERSON><PERSON><PERSON> bao không được mở trong vòng 24 giờ sau khi gửi sẽ được hoàn tiền vào phương thức thanh toán ban đầu.", "hbrefund_set_tips": "<PERSON><PERSON> <PERSON>hi cài đặt, số tiền không được chấp nhận sẽ được hoàn lại theo phương thức thanh toán ban đầu. Điều này không thể được chuyển thành \"Hoàn lại tiền vào số dư\". Tiế<PERSON> tục?", "TeenagerPayDetailUIPage_NotSet": "K<PERSON>ô<PERSON>", "TeenagerPayDetailUIPage_LimitedAmount": "¥{:.{}f}", "TeenagerPaySetLimitModal_AmountTitle": "<PERSON><PERSON> tiền", "TeenagerPaySetLimitModal_MaxAmount": "<PERSON> phép tối đa 7 chữ số", "TeenagerPayGetDetailUseCase_LimitOn": "<PERSON><PERSON><PERSON> mức đã cài đặt", "TeenagerPayGetDetailUseCase_LimitOff": "<PERSON><PERSON><PERSON><PERSON> có hạn mức", "TeenagerPayUseCase_Set_Ok": "<PERSON><PERSON><PERSON><PERSON> lập thành công", "TeenagerPayUseCase_Close_Ok": "<PERSON><PERSON><PERSON> mức số tiền bị tắt", "TeenagerPayUseCase_Limit_Max": "<PERSON>ạn mức thanh toán cho mỗi giao dịch không được lớn hơn hạn mức thanh toán hàng ngày.", "TeenagerPayUseCase_Limit_Min": "<PERSON>ạn mức thanh toán hàng ngày không được nhỏ hơn hạn mức thanh toán cho mỗi giao dịch.", "Dcep_Loading_Wording": "<PERSON>ang tải...", "Dcep_Uninstalled_Error": "Bạn chưa cài đặt E-CNY. <PERSON><PERSON><PERSON> cài đặt rồi thử lại hoặc tiếp tục thanh toán bằng ___<BRAND_Pay>___.", "TeenagerPayUseCase_Input_Accesibility": "<PERSON><PERSON> v<PERSON><PERSON> bản", "bankcard_detail": "{} <PERSON><PERSON> đu<PERSON> {}", "bankcard_qmf_detail": "{} <PERSON><PERSON><PERSON><PERSON>i <PERSON>, {}", "FaceCheck_Agreement_title": "<PERSON><PERSON><PERSON>h khuôn mặt", "FaceCheck_Success_title": "Đ<PERSON> x<PERSON>c <PERSON>h", "FaceCheck_Result_Retry": "<PERSON><PERSON><PERSON> lại", "TabBar_NewBadge": "<PERSON><PERSON><PERSON>", "common_delete_alert_title": "<PERSON><PERSON><PERSON> nhận xóa?", "common_delete": "<PERSON><PERSON><PERSON> tất cả", "transfer_to_bank_name_input_placeholder": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> nhận tiền", "transfer_to_bank_card_input_placeholder": "<PERSON><PERSON> thẻ ngân hàng của người nhận tiền", "transfer_to_bank_bank_select_placeholder": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "transfer_to_bank_arrival_time_select_title": "<PERSON><PERSON><PERSON><PERSON> gian", "transfer_to_bank_arrival_time_modal_title": "<PERSON><PERSON><PERSON> thời gian chuyển tiền", "transfer_to_bank_arrival_time_modal_desc": "<PERSON><PERSON> <PERSON>hi bạn yêu cầu chuyển tiền, tiền sẽ được nạp vào tài khoản của người nhận tiền vào thời điểm tiếp theo.", "transfer_to_bank_history_page_title": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> nhận tiền", "transfer_to_bank_history_page_empty_prompt": "<PERSON><PERSON><PERSON><PERSON> có người nhận tiền trước đó", "transfer_to_bank_history_me_section_title": "<PERSON><PERSON><PERSON>", "transfer_to_bank_history_others_section_title": "<PERSON><PERSON><PERSON><PERSON> nhận tiền trư<PERSON>c đó", "transfer_to_bank_history_modify_remark_action": "<PERSON><PERSON><PERSON>", "transfer_to_bank_history_set_remark_title": "<PERSON><PERSON><PERSON><PERSON>", "transfer_to_bank_history_delete_action": "<PERSON><PERSON><PERSON> tất cả", "transfer_to_bank_bank_unavailable_alert": "<PERSON><PERSON> hàng đang bảo trì. <PERSON><PERSON><PERSON> không thể chuyển tiền.", "transfer_to_bank_money_input_title": "<PERSON><PERSON> tiền", "transfer_to_bank_info_receiver_format": "<PERSON><PERSON><PERSON><PERSON> nhận tiền: {}", "transfer_to_bank_info_charge_fee": "<PERSON><PERSON> vụ", "transfer_to_bank_info_charge_fee_rate_format": "(Tỷ giá: {:.2f})", "transfer_to_bank_info_total_amount": "Tổng số", "transfer_to_bank_info_transfer_explain": "<PERSON><PERSON><PERSON>", "transfer_to_bank_info_transfer_explain_edit_hint_format": "<PERSON><PERSON><PERSON> thị cho cả người thanh toán và người nhận tiền. Tối đa {} ký tự.", "transfer_to_bank_info_add_transfer_explain": "<PERSON><PERSON><PERSON><PERSON>", "transfer_to_bank_info_detail_title": "<PERSON> ti<PERSON>", "transfer_to_bank_info_detail_current_state": "<PERSON><PERSON><PERSON><PERSON> thái", "transfer_to_bank_info_detail_paid_success": "Khởi tạo <PERSON>n tiền", "transfer_to_bank_info_detail_withdrawn_success": "<PERSON><PERSON><PERSON> tất rút tiền", "HoneyPay_PrepareCardUI_Title": "Cài đặt Relative Card", "none": "K<PERSON>ô<PERSON>", "mobile_item_key_bank": "Số điện thoại di động đã đăng ký với ngân hàng", "mobile_item_place_holder_short": "<PERSON><PERSON><PERSON><PERSON> số điện thoại di động", "FillCard_Number_Default_Mobile_Modify_Tips_New": "Số điện thoại di động được liên kết lần trước đã được tự động điền. <PERSON><PERSON>n có thể sửa đổi nó khi cần thiết.", "HoneyPay_MoneyInput_Hint_New": "<PERSON><PERSON><PERSON><PERSON> số tiền", "AddPayCard_No_Card_Bind_Card_Title": "<PERSON><PERSON><PERSON> kết mà không cần nhập số thẻ", "AddPayCard_Manual_Bind_Card_Title": "<PERSON><PERSON><PERSON> kết bằng cách nhập số thẻ", "FillCard_Number_Reg_Hint_V3": "<PERSON><PERSON><PERSON><PERSON> số thẻ ngân hàng của {} ", "FastBindCardSelectBankUIV2_Title": "<PERSON><PERSON><PERSON> kết mà không cần nhập số thẻ", "FastBindCardSelectBankUIV2_Search_Hint": "<PERSON><PERSON><PERSON> k<PERSON> {} ngân hàng", "qrcode_collection_settings": "C<PERSON>i đặt nhận", "qrcode_collection_amount": "<PERSON><PERSON> tiền", "qrcode_collection_remark": "Hướng dẫn nhận tiền", "OfflinePay_Banner_Use_Tips": "<PERSON><PERSON> thanh toán", "OfflinePay_Banner_Expand_Tips": "Mở rộng", "OfflinePay_Banner_Collapse_Tips": "Ẩn", "OfflinePay_Close_WalletLock_HalfDialog_Title": "Tắt Mã thanh toán", "OfflinePay_Close_WalletLock_HalfDialog_Content": "<PERSON><PERSON> bảo mật khi sử dụng mã thanh toán, bạn có thể cài khóa bảo mật. <PERSON>u khi cài đặt, bạn sẽ phải xác minh bảo mật khi sử dụng mã thanh toán.", "FillCardNumberV2_CountryCode_Hint": "<PERSON><PERSON><PERSON><PERSON> mã quốc gia/khu vực", "FillCardNumberV2_CountryCodeView_Hint": "<PERSON><PERSON><PERSON>", "paydesk_main_page_not_use_favor": "Không sử dụng ưu đãi.", "paysecurity_digital_cert_not_install": "<PERSON><PERSON><PERSON> b<PERSON>", "WCPay_Digital_Cert_Desc_Not_Install": "<PERSON><PERSON><PERSON> bật chứng nhận kỹ thuật số", "WCPay_Digital_Cert_Desc_Already_Install": "<PERSON><PERSON> bật chứng nhận kỹ thuật số", "WCPay_Digital_Cert_Manage_Content_Desc": "<PERSON><PERSON><PERSON> hoạt chứng nhận kỹ thuật số trên thiết bị của bạn:", "WCPay_Digital_Cert_Manage_Content_Desc_1": "• Th<PERSON><PERSON> hiện thanh toán từ thiết bị của bạn thậm chí còn đư<PERSON><PERSON> bảo mật hơn", "WCPay_Digital_Cert_Manage_Content_Desc_2": "• <PERSON><PERSON><PERSON> hạn mứ<PERSON>h toán hàng ngày khi sử dụng Số dư", "WCPay_Digital_Cert_Install_Button_Title": "<PERSON><PERSON><PERSON>", "WCPay_Digital_Cert_Delete_Button_Title": "Tắt", "WCPay_Digital_Cert_Install_List_desc": "<PERSON><PERSON><PERSON> thiết bị đã được cài đặt chứng nhận", "WCPay_Digital_Cert_Delete_Confirm_Content": "Bạn có chắc chắn muốn tắt chứng nhận kỹ thuật số cho___<BRAND_ID>___trên thiết bị này?", "WCPay_Digital_Delete_Confirm_Btn_Title": "Tắt", "WCPay_Digital_Cert_Install_Action_Title": "<PERSON><PERSON><PERSON>h nhận dạng", "WCPay_Digital_Cert_Install_Action_Desc": "<PERSON><PERSON><PERSON> buộc phải xác minh nhận dạng trước khi cài đặt chứng nhận", "WCPay_Digital_Cert_Install_Input_Title_default": "Thẻ ID", "WCPay_Digital_Cert_Install_Input_Desc_default": "<PERSON><PERSON><PERSON><PERSON> số thẻ ID", "WCPay_Digital_Cert_Install_Input_Desc": "<PERSON><PERSON><PERSON><PERSON> số thẻ ID của {}", "WCPay_Digital_Cert_Verify_Button_Title": "<PERSON><PERSON><PERSON>", "WCPay_Digital_Cert_Install_Sccuess": "Đ<PERSON> x<PERSON>c <PERSON>h", "WCPay_Digital_Cert_Delete_Succ_Toast": "Đã tắt", "LQT_Purchase_Page_Title": "<PERSON><PERSON><PERSON><PERSON> tiền đến", "LQT_Purchase_Card_Info_Title": "<PERSON><PERSON><PERSON><PERSON> thức chuyển tiền", "LQT_MonetInputOutOfRange_Tips": "<PERSON><PERSON> dư không đủ. <PERSON><PERSON><PERSON> tiền và thử lại.", "LQT_Limit_Cashier_Modal_Balance_Desc": "Số tiền trong Số dư", "LQT_Limit_Cashier_Modal_LQT_Desc": "<PERSON>u<PERSON> nhỏ", "LQT_Limit_Cashier_Modal_Transfer_In_Tips": "<PERSON><PERSON><PERSON><PERSON> tiền đến", "LQT_Limit_Cashier_Modal_Confirm_Button_Title": "<PERSON><PERSON> hiểu", "LQT_SaveAmountLargeThanBankAvaible_Tips": "<PERSON><PERSON> tiền bạn nhập v<PERSON><PERSON><PERSON> quá hạn mức thanh toán của ngân hàng", "LQT_Redeem_Card_Info_Title": "<PERSON><PERSON><PERSON> t<PERSON> về", "LQT_Redeem_Page_Title": "<PERSON><PERSON><PERSON>", "LQT_Redeem_Confirm_View_Desc": "<PERSON><PERSON><PERSON>", "LQT_Redeem_Balance_Amount": "<PERSON><PERSON> dư quỹ nhỏ ¥{:.2f}", "LQT_Redeem_Balance_Insufficient": "<PERSON><PERSON> dư <PERSON> nhỏ không đủ", "LQT_Redeem_Balance_Fetch_All": "<PERSON><PERSON><PERSON> tiền tất cả", "LQT_Loading_Card_Data": "<PERSON><PERSON> l<PERSON>y danh sách thẻ ngân hàng", "LQT_Loading_LQT_Amount": "<PERSON><PERSON> l<PERSON>y số dư <PERSON> nhỏ", "LQT_Loading_LQ_Amount": "<PERSON><PERSON> lấy số tiền trong Số dư", "LQT_PerRedeem_Invalid_Default_Tips": "<PERSON><PERSON><PERSON><PERSON> quá giới hạn số tiền trong một giao dịch", "LQT_Redeem_Fetch_Amount_Large_Than_Bank_Avaible": "<PERSON>ố tiền tối đa cho mỗi giao dịch là ¥{:.2f}. <PERSON><PERSON>n có thể tách thành nhiều giao dịch.", "LQT_Redeem_Fetch_Amount_Large_Than_Max_Amount_Tips": "<PERSON><PERSON><PERSON> hi<PERSON>u thêm", "HoneyPay_Record_Receive_Title": "Relative Card cho tôi", "HoneyPay_Record_Donate_Title": "Relative Card từ tôi", "LQTDetail_balance_Accessibility": "Số dư ¥{:.2f}", "WCPay_LQT_OnClickPurchase_Fail_Network_Error": "Lỗi mạng. <PERSON><PERSON><PERSON><PERSON> thể lấy danh sách thẻ ngân hàng. <PERSON><PERSON> lòng thử lại sau.", "WCPay_LQT_OnClickRedeem_Fail_Network_Error": "Lỗi mạng. <PERSON><PERSON><PERSON><PERSON> thể lấy danh sách thẻ ngân hàng. <PERSON><PERSON> lòng thử lại sau.", "WCPay_LQT_PurchaseFund_Fail_Network_Error": "<PERSON><PERSON><PERSON><PERSON> thể mua hàng. <PERSON><PERSON><PERSON> thử lại sau.", "WCPay_LQT_QryPurchaseResult_Fail_Network_Error": "<PERSON><PERSON><PERSON><PERSON> thể kiểm tra kết quả mua hàng", "WCPay_LQT_PreRedeemFund_Fail_Network_Error": "<PERSON><PERSON><PERSON><PERSON> thể đặt đơn hàng đổi. <PERSON><PERSON><PERSON> thử lại sau.", "WCPay_LQT_RedeemFund_Fail_Network_Error": "<PERSON><PERSON><PERSON><PERSON> thể rút lại tiền. <PERSON><PERSON><PERSON> thử lại sau.", "WCPay_LQT_MoneyVC_Save_TextFieldTips": "<PERSON><PERSON> tiền chuyển k<PERSON>n", "WCPay_LQT_MoneyVC_Fetch_TextFieldTips": "<PERSON><PERSON> tiền", "LQT_Purchase_Keyboard_Confirm_Title": "<PERSON><PERSON><PERSON> t<PERSON>", "LQT_Redeem_Keyboard_Confirm_Title": "<PERSON><PERSON><PERSON>", "Wallet_Lock_Default_Title": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> mật", "Wallet_Lock_FaceLock": "Mở khóa bằng khuôn mặt", "Wallet_Lock_TouchLock": "Mở khóa bằng dấu vân tay", "Wallet_Lock_BioLock": "Mở khóa bằng khuôn mặt/vân tay", "Wallet_Lock_PatternLock": "Mở khóa bằng Mật khẩu mẫu hình", "Wallet_Lock_PatternLock_Modify_Verify_Title": "<PERSON><PERSON><PERSON><PERSON> mật khẩu mẫu hình cũ", "Wallet_Lock_PatternLock_Modify": "Thay đổi mật khẩu mẫu hình", "Wallet_Lock_PatternLock_Modify_SubTltle": "Đặt mật khẩu mẫu hình mới", "Wallet_Lock_Close_Tips": "<PERSON>u khi tắt khóa bảo mật, khô<PERSON> cần cách mở khóa nào để truy cập “Tô<PERSON>” > “Dịch vụ”.", "Wallet_Lock_TouchLock_Verify_Title": "<PERSON><PERSON><PERSON> <PERSON>h Touch ID để tiếp tục", "Wallet_Lock_FaceLock_Verify_Title": "<PERSON><PERSON><PERSON> <PERSON>h Face ID để tiếp tục", "Wallet_Lock_PatternLock_Verify_Title": "<PERSON><PERSON><PERSON><PERSON> mật khẩu mẫu hình", "Wallet_Lock_Verify_byPwd": "<PERSON><PERSON><PERSON>h <PERSON>h toán", "Wallet_Lock_Verify_Btn_FaceID": "<PERSON><PERSON><PERSON>h khuôn mặt", "Wallet_Lock_Verify_Btn_TouchID": "<PERSON><PERSON><PERSON> minh dấu vân tay", "Wallet_Lock_PatternLock_Setup_Title": "Đặt mật khẩu mẫu hình", "Wallet_Lock_PatternLock_Reset_Title": "<PERSON>u<PERSON>n <PERSON>t khẩu Mẫu hình?", "Wallet_Lock_PatternLock_Confirm_Title": "<PERSON><PERSON><PERSON><PERSON> lại để xác nhận", "Wallet_Lock_PatternLock_Confirm_Error_Tips": "Mẫu hình không thống nhất. Hãy đặt lại.", "Wallet_Lock_PatternLock_Verify_Error_Tips": "Mẫu hình không đúng. <PERSON><PERSON> {} c<PERSON> hội.", "Wallet_Lock_PatternLock_Verify_Limit_Tips": "<PERSON><PERSON><PERSON> nhiều lần thử. Thử lại sau {} ph<PERSON><PERSON>.", "Wallet_Lock_PatternLock_Modify_AfterVerify_Title": "Đặt mật khẩu mẫu hình mới", "Wallet_Lock_PatternLock_InvalidPattern_Tips": "<PERSON><PERSON><PERSON> buộc ít nhất 4 điểm. Hãy đặt lại.", "Wallet_Lock_PatternLock_Setup_Succ_Tips": "Đ<PERSON> đặt mật khẩu mẫu hình", "Wallet_Lock_PatternLock_Modify_Succ_Tips": "<PERSON><PERSON><PERSON> khẩu mẫu hình đã thay đổi", "Wallet_Lock_PatternLock_Setup_Cancel_Tips": "<PERSON><PERSON><PERSON><PERSON> bật khóa bằng mẫu hình?", "Wallet_Lock_PatternLock_Setup_Cancel_LeftBtn": "<PERSON><PERSON> bật", "Wallet_Lock_PatternLock_Setup_Cancel_RightBtn": "Để sau", "Wallet_Lock_Not_Support_TouchID_Tip": "Touch ID hiện không có ở thiết bị này. <PERSON><PERSON><PERSON> lại khóa bảo mật.", "Wallet_Lock_Not_Support_FaceID_Tip": "Face ID không khả dụng trên thiết bị này. Đặt lại khóa bảo mật.", "Wallet_Lock_Close_Wallet_Lock_Tip": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> mật", "Wallet_Lock_Setup_Pattern_Lock_Tip": "<PERSON><PERSON><PERSON> khẩu mẫu hình", "Wallet_Lock_TouchID_NotEnrolled_Tip": "Chưa bật Touch ID. Bật Touch ID trong cài đặt hệ thống hoặc cài lại khóa bảo mật.", "Wallet_Lock_FaceID_NotEnrolled_Tip": "<PERSON><PERSON><PERSON> bật Face ID. Bật Face ID trong cài đặt hệ thống hoặc cài lại khóa bảo mật.", "Wallet_Lock_FingerPrint_NotEnrolled_Tip": "<PERSON>h<PERSON>ng có dấu vân tay trong hệ thống. <PERSON>ưu dấu vân tay và cài đặt lại khóa bảo mật.", "Wallet_Lock_PatternLock_VerifyBlock_Tip": "<PERSON><PERSON><PERSON> nhiều lần thử. <PERSON><PERSON><PERSON> minh danh tính của bạn trong “Đặt lại Mật khẩu Mẫu hình” hoặc thử lại sau 10 phút.", "Wallet_Lock_PatternLock_VerifyBlock_Reset_Tip": "Đặt lại <PERSON>t khẩu mẫu hình", "Wallet_Lock_New_FingerPrint_Authen_Tips": "<PERSON><PERSON> nhập dấu vân tay mới. <PERSON><PERSON><PERSON><PERSON> mật khẩu thanh toán để xác minh nhận dạng.", "Wallet_Lock_New_TouchID_Authen_Tips": "Thông tin vân tay trên thiết bị này đã thay đổi. Nhập mật khẩu thanh toán để xác minh danh tính của bạn.", "Wallet_Lock_New_FaceID_Authen_Tips": "Thông tin khuôn mặt trên thiết bị này đã thay đổi. Nhập mật khẩu thanh toán để xác minh danh tính của bạn.", "Wallet_Lock_Forget_Pwd": "<PERSON><PERSON><PERSON><PERSON> mật k<PERSON>u", "Wallet_Lock_Retry_Pwd": "<PERSON><PERSON><PERSON> lại", "LQT_Detail_Operation_More_Product_Title": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m hơn", "pay_settings_biometric_pay_touchid_title": "Thanh toán bằng vân tay", "pay_settings_biometric_pay_faceid_title": "<PERSON>h toán bằng khuôn mặt", "pay_settings_biometric_pay_multi_title": "<PERSON>h toán bằng dấu vân tay/khuôn mặt", "pay_settings_biometric_pay_touchid_desc": "<PERSON><PERSON> đã bật, bạn có thể sử dụng xác minh dấu vân tay để thanh toán n<PERSON>h hơn.", "pay_settings_biometric_pay_faceid_desc": "<PERSON><PERSON> đã bật, bạn có thể sử dụng xác minh khuôn mặt để thanh toán n<PERSON>h hơn.", "pay_settings_biometric_pay_multi_desc": "<PERSON>ã bật thanh toán bằng khuôn mặt hoặc vân tay để thanh toán nhanh hơn.", "pay_settings_biometric_pay_enable_faceid": "<PERSON><PERSON><PERSON> toán bằng khuôn mặt", "pay_settings_biometric_pay_enable_touchid": "<PERSON><PERSON><PERSON> toán bằng dấu vân tay", "common_reddot_accessibility": "Bạn có tin nhắn mới", "common_help": "Hỗ trợ", "bind_new_card_input_name": "<PERSON><PERSON><PERSON><PERSON> tên đăng ký với ngân hàng", "paydesk_title_accessibility_selected": "<PERSON><PERSON> ch<PERSON>n", "RedDot_New": "NEW", "renewal_or_sign_time_item_key": "Cấp/<PERSON>ia hạn <PERSON> phép", "WCPay_Option_Item": "<PERSON><PERSON><PERSON>", "VoiceOver_OfflinePay_Unselected": "Đã bỏ chọn", "FillCard_Number_Reg_Hint_Self": "<PERSON><PERSON><PERSON><PERSON> số thẻ của bạn", "common_continue": "t<PERSON><PERSON><PERSON> t<PERSON>c", "WCPay_Risk_Dialog_Title": "<PERSON><PERSON> phát hiện rủi ro tiềm ẩn. <PERSON><PERSON> lòng xác nhận xem bạn có muốn tiếp tục không. <PERSON><PERSON><PERSON> c<PERSON>, danh tính của bạn sẽ cần được xác minh.", "WCPay_Risk_Not_Support_Dialog_Title": "<PERSON><PERSON> phát hiện rủi ro tiềm ẩn. <PERSON><PERSON> lòng gi<PERSON>i quyết các rủi ro trư<PERSON>c khi tiếp tục.", "WCPay_Risk_Failed_Dialog_Title": "<PERSON><PERSON><PERSON>h danh tính không thành công, p<PERSON><PERSON><PERSON> chấm dứt hoạt động <PERSON>g", "bind_card_agreement_protocal_and_next": "Đồng ý và <PERSON>êm", "wallet": "Ví", "awaiting_real_name_verification": "<PERSON><PERSON> chờ xác minh danh t<PERSON>h", "five_stars": "*****", "Card_UserAgreement_Read_And_Agreement_Title": "<PERSON>ần có sự đồng ý khi thêm", "FaceCheck_Common_Error": "<PERSON><PERSON> thống đang bận. <PERSON><PERSON><PERSON> thử lại.", "FaceCheck_MP_Request_Use": " <PERSON><PERSON><PERSON> c<PERSON><PERSON> quyền truy cập", "FaceCheck_MP_Confirm_Tip": "___<BRAND>___ sử dụng nhận dạng khuôn mặt để xác minh danh tính của bạn. T<PERSON>h năng này chỉ do chủ tài khoản cài đặt.", "FaceCheck_MP_Front_Feedback": "Trung tâm trợ giúp", "FaceCheck_Recoging": "<PERSON><PERSON> nhận dạng...", "waiting_for_real_name_authentication": "<PERSON><PERSON> chờ xác minh danh t<PERSON>h", "collect_sub_title": "<PERSON><PERSON> tiền", "collect_main_add_desc_title_simple_change": "<PERSON><PERSON> đ<PERSON>i", "collect_main_add_desc_title": "<PERSON><PERSON><PERSON><PERSON>", "remittance_amount_lowest_limit": "<PERSON><PERSON> tiền chuyển thấp nhất là ¥0,01.", "collect_main_fixed": "Đặt <PERSON><PERSON> tiền", "collect_main_first_enter_tips_title": "<PERSON><PERSON> nh<PERSON>n tiền", "collect_main_first_enter_tips_new": "Tiền nhận được sẽ được gửi vào ___<BRAND_Balance>___ (\"Tôi\" > \"Dịch vụ\" > \"Số dư\"), tại đó bạn có thể chi tiêu hoặc rút tiền.", "collect_main_close_ring_tone": "<PERSON><PERSON>t âm báo biên n<PERSON>n", "collect_main_close_ring_tone_tips": "Đã tắt", "collect_main_open_ring_tone": "<PERSON><PERSON><PERSON> âm báo biên n<PERSON>n", "collect_main_open_ring_tone_tips": "Đã bật. <PERSON><PERSON> lòng kiểm tra xem âm lượng phương tiện của bạn đã đư<PERSON><PERSON> bật chưa.", "collect_main_qrcode_usage_other": "K<PERSON><PERSON><PERSON>", "collect_main_qrcode_usage_other_placeholder": "<PERSON><PERSON><PERSON><PERSON> thông tin (tối đa 16 ký tự)", "collect_main_payer_desc_default_placeholder": "<PERSON><PERSON><PERSON><PERSON> ghi chú cho ng<PERSON>ời nhận.", "collect_qrcode_save_failed": "<PERSON><PERSON><PERSON> không thành công", "collect_material_guide_save_text_toast": "<PERSON><PERSON> lưu và<PERSON> vi<PERSON><PERSON>nh", "collect_mch_module_title": "Mã QR do<PERSON>h nghi<PERSON>", "collect_personal_module_title": "Mã QR cá nhân", "collect_setting_title": "Cài đặt biên lai", "collect_main_fixed_cancel": "Xoá Số tiền", "collect_main_more_function": "<PERSON>hê<PERSON> cài đặt", "collect_main_save_qrcode": "<PERSON><PERSON><PERSON>", "collect_main_receive_title": "<PERSON><PERSON><PERSON> cộng", "collect_main_paying": "<PERSON><PERSON>h toán...", "collect_main_pay_suc": "<PERSON><PERSON> to<PERSON> thành công", "collect_main_pay_cancel": "<PERSON><PERSON><PERSON> thanh toán", "collect_main_loading_title": "<PERSON><PERSON> tải mã QR...", "collect_main_ring_not_support": "<PERSON><PERSON><PERSON><PERSON> được hỗ trợ trên hệ thống này", "WCPay_Transfer_To_Format": "<PERSON><PERSON><PERSON><PERSON> tiền tới {}", "WCPay_Transfer_Weixin_Alias_Prefix": "___<BRAND_ID>___:", "WCPay_Transfer_InputAmount_Tips": "<PERSON><PERSON><PERSON><PERSON> số tiền trước", "WCPay_Transfer_Cashier_Desc_Format": "<PERSON><PERSON><PERSON><PERSON> tiền tới {}", "WCPay_Transfer_Succ_Tips": "<PERSON><PERSON> chờ nhận tiền từ {}", "WCPay_Service": "<PERSON><PERSON><PERSON> v<PERSON>", "recognize_and_pay": "<PERSON><PERSON><PERSON> n<PERSON>n và thanh toán", "bizf2f_input_ui_page_to_person": "<PERSON><PERSON><PERSON> cho cá nhân", "bizf2f_input_ui_page_add_remark": "<PERSON><PERSON><PERSON><PERSON>", "bizf2f_input_ui_page_amount_title": "<PERSON><PERSON> tiền thanh toán", "WCPay_Verify_Password_Get_SMS_Code": "<PERSON>hận <PERSON> x<PERSON>c <PERSON>h", "WCPay_VerifyCode_Header_Description": "<PERSON><PERSON><PERSON> c<PERSON>u x<PERSON>c minh qua SMS cho giao dịch.", "bizf2f_input_ui_page_pay_action": "<PERSON><PERSON> toán", "bizf2f_input_ui_page_change_remark": "<PERSON><PERSON> đ<PERSON>i", "bizf2f_input_ui_page_pay_title": "<PERSON><PERSON> toán", "bizf2f_favor_title": "Ưu đãi", "bizf2f_favor_total_fee": "<PERSON><PERSON><PERSON> số tiền", "bizf2f_favor_calculating": "<PERSON><PERSON> t<PERSON> toán...", "bizf2f_favor_select_favor": "<PERSON><PERSON><PERSON> ch<PERSON> k<PERSON>u", "UN_BIND_CARD_TITLE": "<PERSON><PERSON><PERSON> liên kết thẻ ngân hàng", "WCPay_system_version_limitation_tip": "<PERSON><PERSON> biết thêm nhiều t<PERSON>h n<PERSON>ng <PERSON>, h<PERSON><PERSON> kiểm tra HarmonyOS 4.2 trở về trước hoặc các thiết bị khác.", "reconfirm_payment_amount_title": "<PERSON><PERSON><PERSON> nhận lại số tiền thanh toán", "reconfirm_payment_amount_des": "<PERSON><PERSON> đảm bảo an toàn cho tài sản của bạn, h<PERSON><PERSON> x<PERSON><PERSON> nhận số tiền để tránh nhầm lẫn.", "reconfirm_amount_page_tip": "<PERSON> các yêu cầu theo quy định, cá<PERSON> thanh toán vượt quá giới hạn mã QR tĩnh phải được hoàn tất bằng cách quét mã QR động bên dưới. Nhấn vào nút để xác minh và hoàn tất thanh toán.", "Hongbao_SendUI_NavigationBar_Title": "<PERSON><PERSON><PERSON> bao", "Hongbao_SendUI_Send_Button_Titlle": "<PERSON><PERSON><PERSON> bị <PERSON> bao", "Hongbao_SendUI_Count_Title": "Số lượng", "Hongbao_SendUI_Amount_Title_Group": "<PERSON><PERSON><PERSON> cộng", "Hongbao_SendUI_Amount_Title_Single": "<PERSON><PERSON> tiền", "Hongbao_SendUI_RandomLuckyMode_Title": "<PERSON><PERSON> tiền ngẫu nhiên", "Hongbao_SendUI_Count_Tips": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "Hongbao_SendUI_Amount_Tips": "¥0.00", "Hongbao_SendUI_Default_Wishing": "<PERSON><PERSON><PERSON> mọi điều tốt đẹp", "Hongbao_Per_Hongbao_Max_Amount_Format": "<PERSON><PERSON><PERSON> t<PERSON> {}CNY cho mỗi hổng bao", "HongBao_SendTips": "<PERSON><PERSON> g<PERSON>i hồng bao", "HongBao_OpenTips": "Mở", "HongBao_AmoutTips": "CNY", "HongBao_MainTitle": "<PERSON><PERSON><PERSON> bao", "HongBao_Cashier_Desc": "___<BRAND_hongbao>___", "Hongbao_SendUI_RandomMode_Title": "<PERSON><PERSON> tiền ngẫu nhiên", "Hongbao_SendUI_NormalMode_Title": "Số tiền gi<PERSON>ng nhau", "Hongbao_SendUI_ExclusiveMode_Title": "<PERSON><PERSON><PERSON>", "Hongbao_SendUI_Select_Count_Suffix": "", "Hongbao_SendUI_Group_Member_Count_Format": "<PERSON><PERSON><PERSON><PERSON> này có {} thành viên", "Hongbao_SendUI_Amount_Title_Group_Normal": "<PERSON><PERSON> tiền Mỗi", "Hongbao_SendUI_Perperson_Maxvalue_Error_Tips": "<PERSON><PERSON><PERSON> t<PERSON> {}CNY cho mỗi hổng bao", "Hongbao_SendUI_Perperson_Minvalue_Error_Tips": "<PERSON>t nhất {:.2f} cho mỗi Hồng bao", "Hongbao_SendUI_Count_Larger_Than_Group_Mem_Count_Error_Tips": "Số lượng bao lì xì không đ<PERSON><PERSON><PERSON> vư<PERSON><PERSON> quá số lượng thành viên trong nhóm.", "Hongbao_SendUI_Total_Num_Error_Tips": "<PERSON><PERSON> lượng tối đa là {}.", "Hongbao_SendUI_Select_Count_Data_Invalid_Error_Tips": "<PERSON><PERSON><PERSON> nh<PERSON> \"<PERSON><PERSON> lượng\"", "Hongbao_SendUI_Amount_Data_Invalid_Error_Tips": "<PERSON><PERSON><PERSON> nh<PERSON>p \"<PERSON>ổng\"", "Hongbao_SendUI_Select_Count_Zero_Error_Tips": "<PERSON><PERSON><PERSON> s<PERSON> l<PERSON>.", "Hongbao_SendUI_Amount_Larger_Than_Max_Amount_Error_Tips": "Tổng số tiền không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá {}CNY.", "Hongbao_ReceiveModal_Detail_Link": "<PERSON>em chi tiết", "Hongbao_DetailUI_Load_More_Text": "<PERSON><PERSON><PERSON><PERSON> để tải nhi<PERSON>u h<PERSON>n", "TransferPhone_Entry_Title": "<PERSON><PERSON><PERSON> thức chuyển tiền", "TransferPhone_To_Bank_Title": "Chuyển tiền tới Thẻ ngân hàng", "TransferPhone_To_Bank_Desc": "Nhập thẻ ngân hàng của người thụ hưởng để chuyển tiền vào tài khoản ngân hàng của họ.", "TransferPhone_To_Phone_Title": "<PERSON>yển tiền đến số điện thoại di động", "TransferPhone_To_Phone_Desc": "<PERSON><PERSON><PERSON><PERSON> số điện thoại di động của người nhận tiền để chuyển tiền đến ___<BRAND_Balance>___.", "TransferPhone_To_PaySetting": "Cài đặt số điện thoại di động chuyển tiền", "WCPay_ThirdParty_Tips_Title": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> miễn trừ", "WCPay_Service_Manage": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> vụ", "identify_and_pay": "<PERSON><PERSON><PERSON> n<PERSON>n và thanh toán", "Hongbao_SendUI_Exclusive_Mode_Send_To_Who": "<PERSON><PERSON><PERSON>", "Hongbao_SendUI_Exclusive_Mode_Send_To_Who_Error_Tips": "<PERSON><PERSON><PERSON> “<PERSON><PERSON><PERSON> đến”", "Hongbao_SendUI_Amount_Data_Invalid_Error_Tips_2": "<PERSON><PERSON><PERSON> “<PERSON><PERSON> tiền”", "MerchantPay_Input_Remark_Hint_Format": "<PERSON><PERSON><PERSON><PERSON> nhận tiền có thể nhìn thấy. Tối đa  {}ký tự.", "MerchantPay_Input_Remark_Title": "<PERSON><PERSON><PERSON><PERSON>", "MerchantPay_Transfer_To_Format": "<PERSON><PERSON> toán cho {}", "Greeting_Hongbao_Random_Change_Amount": "<PERSON>hay đổi số tiền", "Greeting_Hongbao_Who_Receive_Hongbao_Format": "{} đã mở", "set_amount": "Đặt <PERSON><PERSON> tiền"}