{"BindCard_Gender_Female": "Kadın", "BindCard_Gender_Male": "<PERSON><PERSON><PERSON>", "Choose_Deposit_Time": "Tetapkan Waktu", "Choose_Payment": "<PERSON><PERSON><PERSON>", "Continue_Pay": "Lanjut<PERSON>", "Day": "{}", "Each_Day_In_Month_Deposit": "{} <PERSON><PERSON><PERSON> bulan", "Each_WeekDay_Deposit": "Setiap {}", "ExposureInfo_Waiting_Wording": "<PERSON>p tunggu...", "Fetch_Balance": "<PERSON><PERSON>", "Fetch_Balance_Bank_Proccessing": "Bank sedang memproses", "Fetch_Balance_Open_Order": "<PERSON><PERSON>", "Fetch_Balance_Success": "<PERSON> be<PERSON>", "FillCard_Info_ErrorTips_Format": "{} ({} toplam hata)", "FillCard_Number_Default_Mobile_Modify_Tips": "<PERSON>ka nomor ponsel tidak benar, ketuk untuk edit.", "FillCard_Number_Reg_Hint": "Nomor kartu bank Anda", "FillCard_Number_Unreg_Hint": "Nomor kartu bank pemilik akun ___<BRAND>___", "Friday": "<PERSON><PERSON>", "Give_Up": "<PERSON><PERSON><PERSON><PERSON>", "HHC_Check_PWD_To_Add_Plan": "<PERSON><PERSON><PERSON>n kata sandi pembayaran untuk memulai <PERSON> & Simpan <PERSON>a", "HHC_Check_PWD_To_Edit_Plan": "<PERSON><PERSON>kkan kata sandi pembayaran untuk mengubah Belanja & Simpan Anda", "HHC_Check_PWD_To_Pause_Plan": "Ma<PERSON>kkan kata sandi pembayaran untuk menangguhkan Belanja & Simpan", "HHC_Check_PWD_To_Start_Plan": "Ma<PERSON>kkan kata sandi pembayaran untuk mengaktifkan Belanja & Simpan", "HHC_Choose_Payment": "Pilih kartu", "HHC_Deposit_Plan": "<PERSON><PERSON><PERSON>", "HHC_Did_Modify": "<PERSON><PERSON><PERSON>", "HHC_Did_Open": "<PERSON><PERSON><PERSON>", "HHC_Did_Pause": "Ditangg<PERSON><PERSON>", "HHC_Name": "Belanja & Simpan", "HHC_Plan_Check_Amount": "Jumlah tidak valid. Periksa lalu coba lagi.", "HHC_Plan_Set_Bank_Card_Tip": "Untuk menggunakan Belanja & Simpan, pilih kartu deposit terlebih dahulu.", "LQT_Fixed_Deposit": "<PERSON><PERSON><PERSON><PERSON>", "LQT_Fixed_Deposit_Check_PWD_To_Add_Plan": "Masukkan kata sandi pembayaran untuk mengaktifkan deposit berkala", "LQT_Fixed_Deposit_Check_PWD_To_Delete_Plan": "<PERSON><PERSON><PERSON><PERSON> kata sandi pembayaran untuk menghapus jadwal.", "LQT_Fixed_Deposit_Check_PWD_To_Pause_Plan": "Ma<PERSON>kkan kata sandi pembayaran untuk menangguhkan jadwal", "LQT_Fixed_Deposit_Check_PWD_To_Start_Plan": "<PERSON><PERSON>kkan kata sandi pembayaran untuk mengaktifkan jadwal.", "LQT_Fixed_Deposit_Did_Delete": "<PERSON><PERSON><PERSON>", "LQT_Fixed_Deposit_Did_Modify": "<PERSON><PERSON><PERSON><PERSON>", "LQT_Fixed_Deposit_Did_Open": "Diaktifkan", "LQT_Fixed_Deposit_Did_Pause": "Ditangg<PERSON><PERSON>", "LQT_Fixed_Deposit_No_Plan": "Tidak ada jadwal deposit", "LQT_Fixed_Deposit_Plan": "<PERSON><PERSON><PERSON>", "LQT_Fixed_Deposit_Plan_Set_Bank_Card_Tip": "Untuk menetapkan deposit berkala, pilih kartu untuk ditarik dananya terlebih dahulu.", "LQT_Fixed_Deposit_Plan_Set_Time_Tip": "Untuk menetapkan deposit berkala, pilih waktu deposit terlebih dahulu.", "LQT_Fixed_Deposit_Plan_Should_Input": "Untuk menetapkan deposit berkala, masukkan jumlah deposit terlebih dahulu.", "ModifyPwdUseCase_ModifyPwd_Desc": "Ma<PERSON>kkan kata sandi pembayaran untuk memverifikasi identitas", "ModifyPwdUseCase_ModifyPwd_GiveUp_Title": "Batalkan perubahan sandi pembayaran <PERSON>?", "ModifyPwdUseCase_ModifyPwd_Success": "<PERSON>a sandi di<PERSON>h", "ModifyPwdUseCase_ModifyPwd_Title": "Ubah Kata Sandi", "Monday": "<PERSON><PERSON>", "Monthly": "<PERSON><PERSON><PERSON> bulan", "OfflinePay_CreateOfflinePay_ConfirmBtn_Title": "Aktifkan <PERSON>", "OfflinePay_CreateOfflinePay_Euro_Tips": "Pembayaran Cepat tidak diaktifkan. <PERSON><PERSON><PERSON> diak<PERSON>, tunjukkan kode ke kasir untuk membayar dengan cepat. (<PERSON>ya mendukung transaksi CNY)", "OfflinePay_CreateOfflinePay_Tips": "Pembayaran Cepat tidak diaktifkan. <PERSON><PERSON> diak<PERSON>, <PERSON><PERSON> dapat menu<PERSON>n kode untuk membayar vendor secara cepat.", "OfflinePay_ReCreateOfflinePay_ConfirmBtn_Title": "Aktifkan", "OfflinePay_ReCreateOfflinePay_Euro_Tips": "Pembayaran Cepat tidak diaktifkan. Aktifkan Pembayaran Cepat untuk membayar barang kepada pedagang dengan cepat dan mudah - cukup tunjukkan kodenya dan pergi. Hanya mendukung transaksi CNY.", "OfflinePay_ReCreateOfflinePay_Tips": "Pembayaran Cepat tidak diaktifkan. Aktifkan Pembayaran Cepat untuk membayar barang kepada pedagang dengan cepat dan mudah - cukup tunjukkan kodenya dan pergi.", "Saturday": "Sabtu", "Sunday": "<PERSON><PERSON>", "Thursday": "<PERSON><PERSON>", "Tuesday": "<PERSON><PERSON><PERSON>", "WCPay_BankCardBindTo0_0_5D_Detail": "$0,05 akan dipotong untuk verifikasi akun", "WCPay_BankCardBindTo0_0_5D_Detail_back": "$0,05 akan dipotong untuk verifikasi akun dan dikembalikan setelah verifikasi.", "WCPay_BankCardBindTo1B_Detail": "¥0,01 akan dipotong untuk verifikasi akun dan dikembalikan setelah verifikasi.", "WCPay_BankCardBindTo1B_NotReturnDetail": "¥0,01 akan dipotong untuk verifikasi akun agar dapat digunakan.", "WCPay_CountryCode_Title": "Negara/Wilayah", "WCPay_FaceID_Auth_Tip": "V<PERSON><PERSON><PERSON><PERSON><PERSON> wajah Anda untuk membayar", "WCPay_GiveUpReset_Title": "Hentikan pengaturan ulang kata sandi pembayaran?", "WCPay_NeedChangeCard_Error_Btn": "Ubah metode pembayaran", "WCPay_TouchID_Auth_Tip": "Verifikasi sidik jari yang ada untuk membayar", "WCPay_TouchID_Confirm_Alert_Cancel": "<PERSON><PERSON>", "WCPay_TouchID_Confirm_Alert_Content": "Sidik jari diverif<PERSON>. Konfirmasi pembayaran?", "WCPay_TouchID_Confirm_Alert_OK": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "WCPay_TouchID_Confirm_Alert_Title": "Bayar", "WeChatPay_Name": "___<BRAND_Pay>___", "Wednesday": "<PERSON><PERSON>", "Weekly": "<PERSON><PERSON><PERSON> minggu", "address_item_key": "<PERSON><PERSON><PERSON>", "address_item_place_holder": "<PERSON><PERSON><PERSON><PERSON>", "agree": "<PERSON><PERSON><PERSON>", "agree_user_protocal": "<PERSON><PERSON><PERSON><PERSON>", "agreement_alert": "<PERSON><PERSON><PERSON> lihat <PERSON>, <PERSON><PERSON> \"<PERSON><PERSON><PERSON><PERSON>\"", "alertChargeFee": "<PERSON><PERSON><PERSON>", "area_item_key": "<PERSON><PERSON><PERSON>", "ask_verify_fingerprint": "Verifikasi sidik jari", "assign_pay_dialog_content": "Informasi identitas yang dimasukkan berbeda dari yang dihubungkan ke akun ___<BRAND>___ saat ini. Gunakan akun ___<BRAND>___ yang terhubung dengan kartu kredit, lalu verifikasi kembali.", "balance": "<PERSON><PERSON>", "bank_card_item_key": "Kartu kredit", "bank_select_item_key": "Bank", "bind_new_card": "Tambah kartu kredit", "bind_new_card_section_footer": "<PERSON>tuk alasan kea<PERSON>n, kartu yang terhubung saat ini akan diputuskan hubungannya.", "bind_new_card_section_header": "Hubungkan kartu baru untuk mendapatkan kembali akun", "bind_new_card_to_pay_tip": "Ma<PERSON>kkan kata sandi pembayaran untuk memverifikasi identitas", "bind_new_card_to_reset_mobile_desc": "Hubungkan nomor ponsel dengan menghubungkan kartu baru.", "binded_card_list_page_title": "<PERSON><PERSON><PERSON> seluler untuk verifikasi SMS", "birth_date_item_key": "<PERSON><PERSON>", "can_not_bind_more_card": "<PERSON><PERSON><PERSON> maksimum kartu yang tertaut tercapai.", "can_not_get_sms_with_question_mard_word": "Kode verifikasi tidak diterima?", "can_not_get_sms_word": "Kode verifikasi tidak diterima", "cancel_time": "Waktu pembatalan: {}", "cannot_receive_sms_code_content": "Kode SMS telah dikirimkan ke nomor ponsel Anda yang didaftarkan dengan bank Anda. Pastikan Anda saat ini sedang menggunakan nomor ponsel ini dan kode SMS belum diblokir oleh aplikasi keamanan apa pun. Jika Anda tidak lagi menggunakan nomor ini, hubungi bank Anda. Untuk bantuan lebih lanjut, hubungi ___<OfficialEntity_Service>___ di +86-0755-95017.", "cannot_receive_sms_code_title": "Tidak Dapat Mengirim Kode Verifikasi", "card_holder_dialog_content": "1. Untuk memastikan keamanan dana, ID ___<BRAND>___ hanya dapat menautkan kartu bank dengan nama yang sama.\n\n2. Untuk menautkan kartu dengan nama yang berb<PERSON>, <PERSON><PERSON> harus memperbarui informasi nama asli Anda.\n\n3. <PERSON><PERSON><PERSON> mengubah informasi nama asli, informasi pemegang kartu lama akan dihapus dan Anda hanya akan bisa menambahkan kartu dengan nama pemegang kartu yang baru.", "card_holder_dialog_title": "Nama pada kartu", "card_holder_item_key": "Pemegang kartu", "card_holder_section_header": "Masukkan informasi yang sebelumnya Anda berikan ke bank. Hanya kartu kredit yang disimpan dengan nama ini yang dapat ditambahkan di masa mendatang.", "card_info_section_header": "Masukkan informasi kartu", "card_num_item_key": "<PERSON><PERSON>", "card_number_input_tips_title": "Tidak diperlukan biaya atau perbankan online", "card_select_item_key": "<PERSON><PERSON> kartu", "card_type_section_header": "<PERSON><PERSON><PERSON> jenis kartu", "change_realname_word": "<PERSON><PERSON>", "change_to_face_id": "<PERSON><PERSON><PERSON><PERSON>", "change_to_pwd": "<PERSON><PERSON><PERSON>", "change_to_touch_id": "<PERSON><PERSON><PERSON>", "check_pay_pwd_page_desc": "Ma<PERSON>kkan kata sandi pembayaran untuk mengonfirmasi pembayaran", "check_pay_pwd_page_title": "Verifikasi Identitas", "check_sms_desc": "Kode verifikasi telah dikirim ke nomor ponsel yang didaftarkan di bank Anda.\n\n1. Konfirmasikan bahwa ini adalah nomor ponsel Anda saat ini yang terdaftar di bank.\n\n2. Periksa apakah SMS telah diblokir oleh aplikasi keamanan di ponsel Anda.\n\n3. Jika Anda tidak dapat mengakses nomor ini sekarang, silakan hubungi bank Anda.\n\n4. Untuk ban<PERSON>an tambahan, hubungi Layanan Pelanggan di 95017.", "check_sms_page_desc": "Menghubungkan kartu kredit memerlukan verifikasi SMS. Kode verifikasi dikirim ke ponsel {}. <PERSON><PERSON><PERSON> petun<PERSON>.", "check_sms_page_favor": "<PERSON><PERSON> akan memba<PERSON> {}{:.2f} (Disimpan {}{:.2f})", "check_sms_page_title": "Verifikasi nomor ponsel", "common_back": "Kembali", "common_cancel_word": "<PERSON><PERSON>", "common_close": "<PERSON><PERSON><PERSON>", "common_done_word": "Se<PERSON><PERSON>", "common_drop_out_word": "<PERSON><PERSON><PERSON><PERSON>", "common_i_know_word": "<PERSON><PERSON>", "common_more": "<PERSON><PERSON><PERSON>", "common_next_word": "Selanjutnya", "common_question_word": "<PERSON>", "common_select": "<PERSON><PERSON><PERSON>", "common_tip_word": "Pengingat", "confirm_mobile_no": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirm_pay": "<PERSON><PERSON>", "confirm_to_receive": "Konfirmasi <PERSON>", "confrim_pay_and_open_deduct_word": "Bayar & Aktifkan", "confrim_pay_word": "Bayar", "coupon_change_should_change_payment": "<PERSON><PERSON><PERSON> pembayaran telah berubah. <PERSON><PERSON> pilih ulang.", "coupon_component_need_bank_pay_tips": "Potongan Harga untuk Metode Pembayaran yang Ditentukan", "cre_id_item_key": "Nomor ID", "cre_id_item_place_holder": "Masukkan nomor ID", "cre_type_item_key": "<PERSON><PERSON>", "cre_type_item_place_holder": "<PERSON><PERSON><PERSON> jeni<PERSON>", "cvv_dialog_content": "CVV adalah 3 atau 4 digit kode keamanan yang ada di bagian belakang atau di bagian depan kartu Anda.", "cvv_dialog_title": "Apa itu CVV?", "cvv_item_key": "CVV ", "cvv_item_place_holder": "3 atau 4 digit bias<PERSON>a di bagian belakang kartu", "default_delay_transfer_confirm_desc": "Transfer akan tiba dalam {} jam", "email_item_key": "<PERSON><PERSON><PERSON> email", "email_item_place_holder": "Masukkan email", "error_detail_title": "<PERSON><PERSON>", "face_hongbao": "<PERSON><PERSON>", "fast_bind_card_support_bank_title_text": "Saat ini mendukung bank-bank berikut ini", "fill_card_and_user_info_title": "Tuliskan informasi kartu kredit dan identitas", "fill_card_info_card_holder_assign_pay_header": "<PERSON><PERSON> pemegang kartu yang ditunjuk yang dapat membayar.", "fill_card_info_page_favor_desc": "<PERSON><PERSON><PERSON> jenis kartu kredit ini untuk menghemat {}{:.2f} tambahan.", "fill_card_info_page_realname_cre_not_support": "Tidak dapat menggunakan {} untuk menautkan kartu ini", "fill_card_info_page_title": "Masukkan info kartu", "fill_card_num_format_error": "Nomor kartu tidak valid", "fill_card_num_of_card_holder_section_header": "Masukkan nomor kartu kredit dari pemegang kartu", "fill_card_num_page_desc": "Hubungkan kartu kredit", "fill_card_num_page_favor_dialog_title": "<PERSON><PERSON><PERSON> dengan <PERSON> In<PERSON>", "fill_card_num_page_realname_desc": "Anda perlu menambahkan kartu bank Anda untuk menyelesaikan autentikasi nama asli.", "fill_card_num_page_sns_input_hint": "Hanya mendukung kartu debit", "fill_card_num_page_title": "Tambah kartu kredit", "fill_card_number_assign_pay": "Gunakan kartu bank {} untuk membayar", "fill_card_number_more_favor": "Gunakan bank yang ditunjuk untuk menikmati diskon", "fill_complete_name": "<PERSON><PERSON><PERSON><PERSON> nama lengkap", "fill_id_format_error": "Format nomor ID salah.", "fill_in_sms_key": "<PERSON><PERSON> veri<PERSON>", "fill_in_sms_word": "Masukkan kode verifikasi", "fill_phone_num_format_error": "Format nomor ponsel salah.", "finger_print_err_tips": "Coba lagi", "first_name_item_key": "<PERSON><PERSON> de<PERSON>", "first_name_item_place_holder": "<PERSON><PERSON><PERSON><PERSON> na<PERSON>", "float_paydesk_modal_no_select_favor": "Diskon yang Belum Dipakai", "foreign_mobile_header": "<PERSON><PERSON><PERSON><PERSON> ponsel baru", "forget_pay_pwd_title": "Lupa Sand<PERSON>", "get_sms_with_count_down_word": "Dapatkan kode verifikasi \n({})", "get_sms_word": "Dapatkan kode verifikasi", "give_up_on_new_card": "<PERSON><PERSON><PERSON>ti menghubungkan kartu?", "give_up_this_order_or_not": "<PERSON><PERSON>ikan transaksi ini?", "group_aa": "<PERSON><PERSON><PERSON>", "has_send_sms": "Terkirim", "has_send_sms_with_count": "Terkirim ({})", "hongbao_refund_way_header_title": "Paket Me<PERSON> yang tidak dibuka dalam 24 jam setelah dikirimkan akan dikembalikan dengan cara di bawah ini.", "hongbao_refund_way_title": "Ke<PERSON>likan Pak<PERSON> Me<PERSON> ke", "id_card_name": "Kartu ID", "install_cert_error": "<PERSON><PERSON> sertif<PERSON>t", "last_name_item_key": "<PERSON><PERSON>", "last_name_item_place_holder": "<PERSON><PERSON><PERSON><PERSON> nama belakang", "loading_title": "Memuat...", "lottery_network_error": "Maaf, mungkin lain kali!", "lqt": "Biaya Mini", "lqt_reset_mobile_desc": "Pilih kartu bank. Verifikasi Dana Mini Anda menggunakan nomor ponsel yang disimpan oleh kartu kredit tersebut.", "mobile_dialog_content": "Nomor ponsel yang terdaftar pada akun bank Anda adalah nomor yang Anda berikan kepada bank ketika membuka akun. Bila Anda tidak memberikan nomor ponsel ke bank, Anda lupa nomornya, atau nomor tersebut tidak dapat lagi diakses, hubungi bank Anda dan perbarui nomor tersebut.", "mobile_dialog_title": "Nomor ponsel", "mobile_item_key": "Nomor ponsel", "mobile_item_place_holder": "Masukkan nomor ponsel yang disimpan oleh bank", "name_item_key": "<PERSON><PERSON>", "name_item_place_holder": "<PERSON><PERSON><PERSON>n nama pada kartu", "nationality_item_key": "Negara/Wilayah", "nationality_place_holder": "Masukkan negara/wilayah", "new_mobile_item_key": "Nomor ponsel baru", "new_mobile_item_place_holder": "Masukkan nomor ponsel yang disimpan oleh bank", "new_user_card_num_input_safety_desc": "Hubungkan kartu milik pemilik akun ___<BRAND>___ ini", "new_user_card_num_input_safety_desc_v2": "___<BRAND>___ hesap sahibine ait bir kartı ba<PERSON>layın", "no": "Tidak", "offline_choose_payment": "<PERSON><PERSON><PERSON> Default", "offline_choose_payment_fail": "Jika metode pembayaran default <PERSON><PERSON> t<PERSON> be<PERSON>, metode lain akan diupayakan untuk menyelesaikan pembayaran.", "offline_click_view_code": "Ketuk untuk melihat nomor kode pembayaran", "offline_pay_modify_limit": "<PERSON>", "offline_pay_only_pay_title": "Bayar", "offline_pay_select_card_invalid": "Saat ini tidak dapat menggunakan metode pembayaran yang dipilih. Coba metode pembayaran lainnya.", "offline_pay_title": "<PERSON><PERSON>", "offline_pay_to_merchant": "Bayar Pedagang", "offline_prefer_payment": "Metode pembayaran default", "offline_show_code_warning": "<PERSON>ya gunakan nomor kode pembayaran untuk ditunjukkan kepada kasir saat membayar. Untuk keamanan <PERSON>, jangan bagikan nomor dengan siapa pun.", "offline_view_code_warning": "<PERSON>ya gunakan nomor kode pembayaran untuk ditunjukkan kepada kasir saat membayar. Untuk keamanan <PERSON>, jangan bagikan nomor dengan siapa pun.", "ok": "OK", "order_address_section_header": "<PERSON><PERSON><PERSON>", "pay_card_detail_contact_user_info": "Hubung<PERSON>: ", "pay_card_detail_tel_number": "95017", "pay_power_by_tenpay_word": "Disediakan o<PERSON>", "pay_success": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON>", "paydesk_coupon_page_title": "Halaman Diskon Pembayaran", "paydesk_float_page_title": "<PERSON><PERSON>", "paydesk_main_page_more_favor": "<PERSON><PERSON><PERSON> banyak potongan harga", "paydesk_main_page_title": "<PERSON><PERSON>", "paydesk_payment_page_title": "Daftar Kartu Pembayaran", "paydesk_sub_page_title": "<PERSON><PERSON><PERSON>", "payee_remark_title": "Keterangan Pembayar", "paying_alert_tips": "Pembayaran telah dikirimkan. <PERSON><PERSON><PERSON> pesan hasil pembayaran untuk memeriksa apakah Anda perlu mengirimkan kembali pembayaran.", "payment_method": "<PERSON><PERSON>", "phone_number_item_key": "Ponsel", "phone_number_item_place_holder": "<PERSON><PERSON><PERSON><PERSON> nomor ponsel", "profession_item_key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pure_bind_card_succ_tips": "<PERSON><PERSON><PERSON><PERSON>", "pwd_repeat_error_tip": "Kata sandi tidak sesuai", "rebind_bank_card_section_header": "Hubungkan kartu lagi untuk mendapatkan kembali akun", "receipt": "<PERSON><PERSON>", "receive_done": "Diterima", "receive_remark_title": "Keterangan Tanda Terima", "receive_time": "<PERSON><PERSON><PERSON> tanda terima: {}", "receiver_remark_title": "Keterangan Penerima", "refund_doing_tip": "Memproses pengembalian dana. <PERSON><PERSON><PERSON> dana tersebut akan dikembalikan ke kartu Anda dalam 1-3 hari kerja.", "refund_done": "<PERSON>", "refund_done_and_expired": "<PERSON><PERSON><PERSON><PERSON><PERSON> (kedaluwarsa)", "refund_time": "<PERSON><PERSON><PERSON> penge<PERSON>: {}", "refund_transfer_or_not": "Kembalikan transfer dari {}?", "refund_word": "<PERSON><PERSON><PERSON><PERSON>", "renewal_time_item_key": "<PERSON><PERSON><PERSON>", "resend_message_or_not": "<PERSON><PERSON> kembali pesan ini?", "resend_sms": "<PERSON><PERSON>", "resend_sms_with_count": "<PERSON><PERSON> ({})", "resend_success_tip": "<PERSON>esan di<PERSON> kembali", "resend_word": "<PERSON><PERSON>", "reset_ban_mobile_fill_card_info_credit_tip_header": "Masukkan informasi kartu kredit untuk pemeriksaan", "reset_ban_mobile_fill_card_num_tip_header": "Tambah kartu kredit baru dan gunakan nomor ponsel yang disimpan oleh bank Anda untuk memverifikasi Saldo pembayaran.", "reset_cvv_and_valid_date_tip": "Anda memperbarui informasi kartu yang terhubung dan melakukan pembayaran pada saat yang sama. Bila Anda tidak yakin telah melakukannya, hubungi layanan pelanggan bank Anda: ", "reset_cvv_title": "Ubah CVV", "reset_lqt_mobile_fill_card_num_tip_header": "Tambah kartu baru dan gunakan nomor ponsel yang terdaftar pada kartu untuk menyelesaikan verifikasi SMS untuk Dana Mini.", "reset_mobile_bank_card_number": "Kartu", "reset_mobile_card_desc_format": "{}{} ({}) nomor ponsel yang terdaftar", "reset_mobile_card_desc_with_update_format": "{}{} ({}) nomor ponsel yang terdaftar. ", "reset_mobile_new_mobile_info_btn": "Detail", "reset_mobile_new_mobile_number": "<PERSON><PERSON>", "reset_mobile_phone_page_title": "Edit nomor ponsel", "reset_phone_tip": "Anda dapat membayar setelah identitas diverifikasi. Untuk mengonfirmasi nomor telepon yang disimpan oleh bank Anda, hubungi ", "reset_pwd_fill_rebind_card_info_page_title": "Masukkan informasi kartu kredit", "reward": "<PERSON><PERSON>", "safety_dialog_content": "Pengukuran keamanan: <PERSON><PERSON><PERSON><PERSON> akun, peman<PERSON>uan real-time, pembekuan darurat. \n\nVerifikasi dua langkah: Kata sandi pembayaran diperlukan untuk setiap pembayaran. Verifikasi SMS diperlukan untuk pembayaran berjumlah besar. \n\nPerlindungan privasi: Enkripsi data yang kuat digunakan untuk melindungi data pengguna. \n\nAsuransi pembayaran: Pembayaran diasuransikan oleh PICC.", "safety_dialog_title": "<PERSON><PERSON><PERSON><PERSON> kea<PERSON>n", "scan_card_num_title": "Pindai Kartu", "select_payment": "<PERSON><PERSON><PERSON> metode pembayaran", "select_payment_card": "<PERSON><PERSON><PERSON>", "send_verify_code_btn_wording": "<PERSON><PERSON>", "send_verify_code_switch_btn_wording": "Ubah metode verifikasi", "send_verify_code_tips_format": "Kode verifikasi SMS akan dikirim ke: \n{}", "set_pay_pwd_confirm_page_title": "<PERSON><PERSON><PERSON>n lagi untuk mengonfirmasi", "set_pay_pwd_page_desc": "Atur kata sandi ___<BRAND_Pay>___ untuk memverifikasi pembayaran Anda", "set_pay_pwd_page_title": "Tetapkan Sandi <PERSON>em<PERSON>", "set_pwd_success": "Pengatura<PERSON> diperbarui", "succ_page_open_biometric_cancel_btn_title": "<PERSON><PERSON>", "succ_page_open_biometric_dialog_content": "<PERSON>a dapat mengaktifkan pembayaran wajah atau sidik jari untuk melakukan pembayaran dengan lebih cepat.", "succ_page_open_biometric_faceid_btn_title": "<PERSON><PERSON><PERSON><PERSON>", "succ_page_open_biometric_touchid_btn_title": "<PERSON><PERSON>bay<PERSON>", "succ_page_open_face_id_dialog_content": "Aktifkan Pembayaran Wajah untuk menggunakan pengenal wajah untuk menyelesaikan pembayaran dengan cepat dan aman.", "succ_page_open_face_id_right_btn_title": "Aktifkan <PERSON>bayaran Waja<PERSON>", "succ_page_open_touch_id_dialog_content": "Aktifkan Pembayaran Sentuh untuk menggunakan pengenal sidik jari untuk menyelesaikan pembayaran dengan cepat dan aman.", "succ_page_open_touch_id_left_btn_title": "<PERSON><PERSON><PERSON>", "succ_page_open_touch_id_right_btn_title": "Aktifkan Pembayaran Sentuh", "to_be_confirm_receive": "Tanda terima belum dikonfirmasi", "transfer": "Transfer", "transfer_account": "<PERSON><PERSON><PERSON> transfer", "transfer_amount_input_invalid_hint": "<PERSON><PERSON><PERSON> yang dimasukkan salah", "transfer_bank": "Transfer ke Kartu Bank", "transfer_explain": "Tambah catatan transfer", "transfer_modify_explain": "Ubah", "transfer_second_left_button": "<PERSON><PERSON>", "transfer_second_right_button": "Lanjutkan", "transfer_second_title": "Pengingat transfer", "transfer_time": "Waktu transfer: {}", "transfer_ui_title": "Transfer ke Teman", "understand_safety": "<PERSON><PERSON><PERSON><PERSON> kea<PERSON>n", "update_word": "<PERSON><PERSON><PERSON>", "user_card_type_select_placeholder_v2": "Banka kartı ve kart türünü seçin", "user_info_section_header": "Masukkan informasi pribadi", "user_protocal": "\"<PERSON><PERSON><PERSON><PERSON>\"", "valid_date_item_key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "verify_cre_tip": "Masukkan 4 digit terakhir {} {} untuk memverifikasi identitas", "verify_fingerprint_fail": "Verifikasi sidik jari gagal", "verify_id_ui_true_name_tips": "{} (<PERSON><PERSON><PERSON><PERSON> nama leng<PERSON>p)", "wechat_bank_agreement": "Perjanjian Bank", "wechat_mobile_phone_word": "Ponsel yang terhubung ke ___<BRAND>___", "wechat_user_agreement": "<PERSON><PERSON><PERSON><PERSON> ___<BRAND_Pay>___", "wxp_common_cancel": "<PERSON><PERSON>", "wxp_common_confirm": "OK", "wxp_common_i_know": "<PERSON><PERSON>", "wxp_common_remind": "Pengingat", "wxp_network_error": "Sistem sibuk. Coba lagi nanti.", "wxp_payment_network_error": "Transaksi terkirim. <PERSON><PERSON> akan menerima pemberitahuan status pembayaran dari Akun <PERSON>i ___<BRAND_Pay>___. Konfirmasikan status pembayaran sebelum mengirimkan ulang pembayaran bila perlu.", "wxp_system_error": "Sistem sibuk. Coba lagi nanti.", "wxp_wcpay_system_error": "Sistem sibuk. Coba lagi nanti.", "yes": "Ya", "zip_item_key": "<PERSON><PERSON> pos", "zip_item_place_holder": "Masukkan kode pos", "common_confirm_word": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ModifyPwdUseCase_ModifyPwd_GiveUp_Yes": "<PERSON><PERSON>", "ModifyPwdUseCase_ModifyPwd_GiveUp_No": "Lanjutkan", "disagree": "Tidak Setuju", "card_user_agreement": "<PERSON><PERSON><PERSON><PERSON>", "card_bank_agreement": "Perjanjian Bank", "Card_UserAgreement_Title": "Anda harus menyetujui perjanjian di bawah ini untuk menambahkan kartu bank.", "pay_settings_delay_transfer_page_title": "Waktu Transfer", "pay_settings_delay_transfer_page_desc": "<PERSON><PERSON><PERSON>, dana akan didepostitokan ke Saldo pengguna lain pada waktu berikutnya. Transfer tidak dapat ditarik kembali set<PERSON><PERSON> di<PERSON>, jadi periksa informasi penerima dengan seksama sebelum mentransfer.", "pay_settings_delay_transfer_no_delay": "Instan", "pay_settings_delay_transfer_two_hour": "Dalam 2 jam", "pay_settings_delay_transfer_one_day": "Dalam 24 jam", "pay_settings_biometric_pay_enabled": "Diaktifkan", "pay_settings_biometric_pay_disabled": "Dinonaktifkan", "pay_settings_biometric_pay_multi_support_title": "<PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON>", "pay_settings_biometric_pay_faceid_enabled": "Pembayaran wajah diak<PERSON>fkan", "pay_settings_biometric_pay_touchid_enabled": "Pembayaran sidik jari di<PERSON>kan", "pay_settings_biometric_pay_multi_support_desc": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> dapat menggunakan verifikasi wajah atau sidik jari untuk membuat pembayaran menjadi lebih cepat.", "f2f_pay_extrabuy_detail_modal_original_price": "(Harga asli ¥{:.2f})", "common_button": "Tombol", "Accessibility_Type_SwitchView_Selected": "{}, tombol alih, aktifkan", "Accessibility_Type_SwitchView_UnSelected": "{}, tombol alih, nonaktifkan", "YunShanFu_Loading_Wording": "Me<PERSON>uka QuickPass...", "YunShanFu_Uninstalled_Error": "Anda belum menginstal QuickPass. Instal dan coba lagi, atau lanjutkan pembayaran dengan ___<BRAND_Pay>___.", "OfflinePay_Setting_CloseOfflinePay_AlertMessage_ShowFeaturePassword": "Ke<PERSON>ka kunci keamanan di<PERSON>, metode membuka kunci pilihan Anda diperlukan untuk mengakses Pembayaran Cepat yang membantu mengamankan pembayaran Anda. Nonaktifkan?", "OfflinePay_Setting_CloseOfflinePay_AlertMessage": "Nonaktifkan Pembayaran Cepat?", "OfflinePay_Setting_CloseOfflinePay_BtnTitle": "Nonaktifkan", "OfflinePay_Setting_CloseOfflinePay_Cancel": "Lanjutkan", "OfflinePay_Setting_CloseOfflinePay_OpenWalletLock": "Tetap<PERSON>", "Wallet_Mix_Paid_UnKnown_Error": "Permintaan transaksi dikirimkan. <PERSON>a akan menerima pemberitahuan status dari Akun <PERSON>i ___<BRAND_Pay>___ Hong Kong. Jangan lakukan pembayaran lagi sampai status pembayaran telah dikonfirmasi.", "bank_card_info": "Catatan", "HHC_Did_Add": "Ditambahkan", "VoiceOver_OfflinePay_barCode": "<PERSON><PERSON>, dapat di<PERSON>n ke kasir. Ketuk dua kali untuk menampilkan kode pembayaran layar penuh", "VoiceOver_OfflinePay_barCode_short": "<PERSON><PERSON>", "VoiceOver_OfflinePay_Qrcode": "Pembayaran Kode QR", "VoiceOver_OfflinePay_barcode_clickHint": "Ketuk dua kali untuk kembali", "VoiceOver_OfflinePay_Qrcode_clickHint": "Ketuk dua kali untuk menampilkan dalam layar penuh", "common_link": "Tautan", "Accessibility_Collapse_Header_Collapsed": "{}, diciut<PERSON>", "Accessibility_Collapse_Header_Showed": "{}, dibentangkan", "Pay_Android_Fingerprint_Prompt_Title": "Verifikasi sidik jari", "Pay_Android_Fingerprint_Prompt_SubTitle": "untuk menyelesaikan pembayaran.", "Pay_Android_Fingerprint_Prompt_Button": "<PERSON><PERSON><PERSON>", "Accessibility_Collapse_Header_Collapsed({}": "Menampilkan Lebih Sedikit", "Accessibility_Collapse_Header_Showed({}": "Menampilkan Semu<PERSON>", "Accessibility_State_Disabled": "Gelapkan", "Accessibility_String_PwdInputView": "Password box, 6 digits in total, {} digits entered", "Accessibility_Type_Button": "{}, button", "Accessibility_Type_CheckBox_Selected": "Check box, selected, {}, button", "Accessibility_Type_CheckBox_UnSelected": "Check box, unselected, {}, button", "Accessibility_Type_Selected": "Selected, {}, button", "Accessibility_Type_UnSelected": "Unselected, {}, button", "common_check_box_check": "Check box, selected", "common_check_box_uncheck": "Check box, unselected", "OfflinePay_ChangePayment_UnConnectedNetwork_Tips": "Jaringan tidak tersedia. Tidak dapat memilih metode pembayaran.", "OfflinePay_EnablePage_UnConnectedNetwork_Tips": "<PERSON><PERSON>an tidak tersedia. Coba lagi nanti.", "Fetch_Balance_To_Bank": "Ditarik ke", "Fetch_Balance_Amount": "<PERSON><PERSON><PERSON> yang ditarik kembali", "Fetch_Balance_Amount_Tips": "Saldo: ¥{}.", "Fetch_Balance_Amount_Exceed": "<PERSON><PERSON><PERSON> yang dimasukkan melebihi <PERSON>do yang tersedia", "Fetch_Balance_Fetch_All": "<PERSON><PERSON>", "HoneyPay_CheckPwd_Unbind_Title": "Putuskan Hubungan Kartu Kerabat", "HoneyPay_Modify_CreditLimit_Title": "<PERSON> batas bulanan", "HoneyPay_Modify_CreditLimit_Desc": "<PERSON><PERSON> b<PERSON>", "HoneyPay_Modify_CreditLimit_Max_Alert": "<PERSON><PERSON><PERSON> tidak dapat melebihi ¥{:.2f}", "balance_entry_balnce_title": "<PERSON><PERSON>", "balance_entry_balnce_detail": "Transaksi", "balance_entry_powered_by_tenpay": "Disediakan o<PERSON>", "balance_recharge_page_title": "<PERSON><PERSON>", "balance_recharge_card_info_title": "Met<PERSON> Is<PERSON>", "balance_recharge_payment_new_card": "Tambah Kartu Baru", "HoneyPay_Add_Card": "Hadiahkan Kartu Kerabat", "HoneyPay_Select_Contact_Title": "<PERSON><PERSON><PERSON>", "HoneyPay_Modify_Comment": "<PERSON>", "HoneyPay_MoneyInput_Hint": "<PERSON><PERSON><PERSON><PERSON> jum<PERSON>", "HoneyPay_CreateCard_Btn": "<PERSON><PERSON>", "HoneyPay_Max_Amount_Notice": "<PERSON><PERSON><PERSON> tidak dapat melebihi ¥{:.2f}", "HoneyPay_Modify_Credit": "<PERSON><PERSON> b<PERSON>", "HoneyPay_Main_Title": "Kartu Kerabat", "hbrefund_info_tips": "Keterangan", "hbrefund_set_button": "<PERSON><PERSON><PERSON><PERSON>", "hbrefund_time_title": "<PERSON><PERSON><PERSON> Penge<PERSON>lian Paket Me<PERSON>", "hbrefund_forbid_way": "<PERSON><PERSON> pengembalian dana ini tidak lagi didukung.", "hbrefund_had_set": "<PERSON><PERSON><PERSON><PERSON> diatur", "hbrefund_origin_desc": "Paket Merah yang tidak dibuka dalam 24 jam setelah dikirimkan akan dikembalikan ke metode pembayaran semula.", "hbrefund_set_tips": "<PERSON><PERSON><PERSON>, uang yang tidak diterima akan dikembalikan ke metode pembayaran semula. Ini tidak dapat dialihkan ke \"Pengembalian Dana ke Saldo\". Lanjutkan?", "TeenagerPayDetailUIPage_NotSet": "Tidak ada", "TeenagerPayDetailUIPage_LimitedAmount": "¥{:.{}f}", "TeenagerPaySetLimitModal_AmountTitle": "<PERSON><PERSON><PERSON>", "TeenagerPaySetLimitModal_MaxAmount": "Diizinkan hingga 7 digit", "TeenagerPayGetDetailUseCase_LimitOn": "Batas diatur", "TeenagerPayGetDetailUseCase_LimitOff": "Tidak ada batas", "TeenagerPayUseCase_Set_Ok": "<PERSON><PERSON><PERSON><PERSON> diatur", "TeenagerPayUseCase_Close_Ok": "<PERSON><PERSON> jumlah din<PERSON>", "TeenagerPayUseCase_Limit_Max": "Batas pembayaran per transaksi tidak boleh lebih besar dari batas pembayaran harian.", "TeenagerPayUseCase_Limit_Min": "Batas pembayaran harian tidak boleh kurang dari batas pembayaran per transaksi.", "Dcep_Loading_Wording": "Memuat...", "Dcep_Uninstalled_Error": "Anda belum menginstal E-CNY. Instal dan coba lagi, atau lanjutkan pembayaran dengan ___<BRAND_Pay>___.", "TeenagerPayUseCase_Input_Accesibility": "Bilah teks", "bankcard_detail": "{} <PERSON><PERSON> ujung {}", "bankcard_qmf_detail": "{} Pengirim Kartu Kerabat, {}", "FaceCheck_Agreement_title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaceCheck_Success_title": "Diverifika<PERSON>", "FaceCheck_Result_Retry": "Coba lagi", "TabBar_NewBadge": "<PERSON><PERSON>", "common_delete_alert_title": "Konfirmasi pen<PERSON>?", "common_delete": "Hapus", "transfer_to_bank_name_input_placeholder": "<PERSON><PERSON>", "transfer_to_bank_card_input_placeholder": "No. Kartu Bank Penerima", "transfer_to_bank_bank_select_placeholder": "Pilih bank", "transfer_to_bank_arrival_time_select_title": "<PERSON><PERSON><PERSON>", "transfer_to_bank_arrival_time_modal_title": "<PERSON><PERSON><PERSON>", "transfer_to_bank_arrival_time_modal_desc": "<PERSON><PERSON><PERSON> Anda meminta transfer, biaya akan didepositkan pada akun penerima saat itu juga.", "transfer_to_bank_history_page_title": "<PERSON><PERSON><PERSON>", "transfer_to_bank_history_page_empty_prompt": "Tidak ada penerima sebelumnya", "transfer_to_bank_history_me_section_title": "<PERSON><PERSON>", "transfer_to_bank_history_others_section_title": "<PERSON><PERSON><PERSON>", "transfer_to_bank_history_modify_remark_action": "Catatan", "transfer_to_bank_history_set_remark_title": "Tambahkan Catatan", "transfer_to_bank_history_delete_action": "Hapus", "transfer_to_bank_bank_unavailable_alert": "Bank sedang dalam pemeliharaan. Transfer saat ini tidak tersedia.", "transfer_to_bank_money_input_title": "<PERSON><PERSON><PERSON>", "transfer_to_bank_info_receiver_format": "Penerima: {}", "transfer_to_bank_info_charge_fee": "<PERSON><PERSON><PERSON>", "transfer_to_bank_info_charge_fee_rate_format": "(tarif: {:.2f})", "transfer_to_bank_info_total_amount": "Total", "transfer_to_bank_info_transfer_explain": "Keterangan", "transfer_to_bank_info_transfer_explain_edit_hint_format": "Terlihat oleh pembayar dan penerima pembayaran. Maks {} karakter.", "transfer_to_bank_info_add_transfer_explain": "Tambahkan Catatan", "transfer_to_bank_info_detail_title": "Detail", "transfer_to_bank_info_detail_current_state": "Status", "transfer_to_bank_info_detail_paid_success": "<PERSON><PERSON>", "transfer_to_bank_info_detail_withdrawn_success": "Penarikan se<PERSON>ai", "HoneyPay_PrepareCardUI_Title": "Atur Kartu Kerabat", "none": "Tidak ada", "mobile_item_key_bank": "Nomor ponsel yang terdaftar di bank", "mobile_item_place_holder_short": "<PERSON><PERSON><PERSON><PERSON> nomor ponsel", "FillCard_Number_Default_Mobile_Modify_Tips_New": "Nomor ponsel yang ditautkan terakhir kali telah terisi secara otomatis. <PERSON>a dapat mengu<PERSON>ya sesuai kebutuhan.", "HoneyPay_MoneyInput_Hint_New": "<PERSON><PERSON><PERSON><PERSON> jum<PERSON>", "AddPayCard_No_Card_Bind_Card_Title": "Tautan tanpa memasukkan nomor kartu", "AddPayCard_Manual_Bind_Card_Title": "Tautkan dengan memasukkan nomor kartu", "FillCard_Number_Reg_Hint_V3": "Masukkan nomor kartu bank {}.", "FastBindCardSelectBankUIV2_Title": "Menautkan tanpa memasukkan nomor kartu", "FastBindCardSelectBankUIV2_Search_Hint": "Cari bank {}", "qrcode_collection_settings": "<PERSON><PERSON><PERSON>", "qrcode_collection_amount": "<PERSON><PERSON><PERSON>", "qrcode_collection_remark": "Petunjuk untuk Menerima Uang", "OfflinePay_Banner_Use_Tips": "<PERSON><PERSON>", "OfflinePay_Banner_Expand_Tips": "Bentangkan", "OfflinePay_Banner_Collapse_Tips": "Sembunyikan", "OfflinePay_Close_WalletLock_HalfDialog_Title": "Nonaktifkan Kode Pembayaran", "OfflinePay_Close_WalletLock_HalfDialog_Content": "<PERSON><PERSON> keamanan ketika menggunakan kode pembayaran, <PERSON><PERSON> da<PERSON>t mengatur kunci keamanan. <PERSON><PERSON><PERSON>, veri<PERSON><PERSON><PERSON> keamanan akan diperlukan ketika Anda menggunakan kode pembayaran.", "FillCardNumberV2_CountryCode_Hint": "Masukkan kode negara/wilayah", "FillCardNumberV2_CountryCodeView_Hint": "<PERSON><PERSON><PERSON>", "paydesk_main_page_not_use_favor": "<PERSON><PERSON> gunakan <PERSON>.", "paysecurity_digital_cert_not_install": "Tidak Diaktifkan", "WCPay_Digital_Cert_Desc_Not_Install": "Sertifikat digital tidak diaktifkan", "WCPay_Digital_Cert_Desc_Already_Install": "Sertifikat digital diaktifkan", "WCPay_Digital_Cert_Manage_Content_Desc": "Mengaktifkan sertifikat digital di perangkat Anda:", "WCPay_Digital_Cert_Manage_Content_Desc_1": "• Menjadikan pembayaran dari perangkat <PERSON>a lebih aman", "WCPay_Digital_Cert_Manage_Content_Desc_2": "• Meningkatkan batas harian untuk pembayaran menggunakan Saldo", "WCPay_Digital_Cert_Install_Button_Title": "Aktifkan", "WCPay_Digital_Cert_Delete_Button_Title": "Nonaktifkan", "WCPay_Digital_Cert_Install_List_desc": "Perangkat yang telah mengaktifkan sertifikat", "WCPay_Digital_Cert_Delete_Confirm_Content": "Anda yakin ingin menonaktifkan sertifikat digital untuk___<BRAND_ID>___saat ini di perangkat?", "WCPay_Digital_Delete_Confirm_Btn_Title": "Nonaktifkan", "WCPay_Digital_Cert_Install_Action_Title": "Verifikasi identitas", "WCPay_Digital_Cert_Install_Action_Desc": "Diperlukan verifikasi identitas sebelum menginstal sertifikat", "WCPay_Digital_Cert_Install_Input_Title_default": "Kartu ID", "WCPay_Digital_Cert_Install_Input_Desc_default": "Masukkan nomor ID", "WCPay_Digital_Cert_Install_Input_Desc": "Masukkan nomor ID dari {}", "WCPay_Digital_Cert_Verify_Button_Title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "WCPay_Digital_Cert_Install_Sccuess": "Diverifika<PERSON>", "WCPay_Digital_Cert_Delete_Succ_Toast": "Dinonaktifkan", "LQT_Purchase_Page_Title": "Transfer Masuk", "LQT_Purchase_Card_Info_Title": "Metode Transfer", "LQT_MonetInputOutOfRange_Tips": "Saldo tidak cukup. Isi ulang dan coba lagi.", "LQT_Limit_Cashier_Modal_Balance_Desc": "<PERSON><PERSON><PERSON>", "LQT_Limit_Cashier_Modal_LQT_Desc": "Biaya Mini", "LQT_Limit_Cashier_Modal_Transfer_In_Tips": "Transfer Masuk", "LQT_Limit_Cashier_Modal_Confirm_Button_Title": "<PERSON><PERSON>", "LQT_SaveAmountLargeThanBankAvaible_Tips": "<PERSON><PERSON><PERSON> yang <PERSON>a masukkan melebihi batas pembayaran bank", "LQT_Redeem_Card_Info_Title": "<PERSON><PERSON> ke", "LQT_Redeem_Page_Title": "<PERSON><PERSON>", "LQT_Redeem_Confirm_View_Desc": "<PERSON><PERSON>", "LQT_Redeem_Balance_Amount": "Saldo Biaya Mini ¥{:.2f}", "LQT_Redeem_Balance_Insufficient": "Saldo Biaya Mini Tidak Mencukupi", "LQT_Redeem_Balance_Fetch_All": "<PERSON><PERSON>", "LQT_Loading_Card_Data": "Memperoleh daftar kartu bank", "LQT_Loading_LQT_Amount": "Memperoleh saldo B<PERSON>ya Mini", "LQT_Loading_LQ_Amount": "<PERSON><PERSON><PERSON><PERSON> jumlah da<PERSON>", "LQT_PerRedeem_Invalid_Default_Tips": "<PERSON><PERSON><PERSON> trans<PERSON>i tunggal melebihi batas", "LQT_Redeem_Fetch_Amount_Large_Than_Bank_Avaible": "<PERSON><PERSON><PERSON> maksimum per transaksi adalah ¥{:.2f}. <PERSON><PERSON> dapat membaginya menjadi beberapa transaksi.", "LQT_Redeem_Fetch_Amount_Large_Than_Max_Amount_Tips": "<PERSON><PERSON><PERSON><PERSON> lebih lan<PERSON>t", "HoneyPay_Record_Receive_Title": "Kartu Kerabat untuk saya", "HoneyPay_Record_Donate_Title": "Kartu Kerabat dari saya", "LQTDetail_balance_Accessibility": "Saldo ¥{:.2f}", "WCPay_LQT_OnClickPurchase_Fail_Network_Error": "Ke<PERSON><PERSON> jaringa<PERSON>. Tidak dapat memperoleh daftar kartu bank Anda. Coba lagi nanti.", "WCPay_LQT_OnClickRedeem_Fail_Network_Error": "Ke<PERSON><PERSON> jaringa<PERSON>. Tidak dapat memperoleh daftar kartu bank Anda. Coba lagi nanti.", "WCPay_LQT_PurchaseFund_Fail_Network_Error": "Tidak dapat membeli. Coba lagi nanti.", "WCPay_LQT_QryPurchaseResult_Fail_Network_Error": "Tidak dapat memeriksa hasil pembelian", "WCPay_LQT_PreRedeemFund_Fail_Network_Error": "Tidak dapat melakukan pesanan penebusan. Coba lagi nanti.", "WCPay_LQT_RedeemFund_Fail_Network_Error": "Tidak dapat menebus dana. Coba lagi nanti.", "WCPay_LQT_MoneyVC_Save_TextFieldTips": "Jumlah Transfer Masuk", "WCPay_LQT_MoneyVC_Fetch_TextFieldTips": "<PERSON><PERSON><PERSON>", "LQT_Purchase_Keyboard_Confirm_Title": "<PERSON><PERSON><PERSON><PERSON>", "LQT_Redeem_Keyboard_Confirm_Title": "<PERSON><PERSON>", "Wallet_Lock_Default_Title": "<PERSON><PERSON><PERSON>", "Wallet_Lock_FaceLock": "Pembuka <PERSON>", "Wallet_Lock_TouchLock": "Pembuka Kunci Sid<PERSON>", "Wallet_Lock_BioLock": "Pembuka Kunci <PERSON>/<PERSON><PERSON><PERSON>", "Wallet_Lock_PatternLock": "Pembuka Kunci Kata Sandi Pola", "Wallet_Lock_PatternLock_Modify_Verify_Title": "<PERSON><PERSON><PERSON>n kata sandi pola yang lama", "Wallet_Lock_PatternLock_Modify": "Ubah Kata Sandi Pola", "Wallet_Lock_PatternLock_Modify_SubTltle": "Tetapkan kata sandi pola baru", "Wallet_Lock_Close_Tips": "<PERSON><PERSON><PERSON> kunci keamanan din<PERSON>, tidak ada metode membuka kunci yang diperlukan untuk mengakses \"<PERSON>a\" > \"<PERSON>anan\".", "Wallet_Lock_TouchLock_Verify_Title": "Verifikasi Touch ID untuk melanjutkan", "Wallet_Lock_FaceLock_Verify_Title": "Verifikasi Face ID untuk melanjutkan", "Wallet_Lock_PatternLock_Verify_Title": "<PERSON><PERSON><PERSON><PERSON> kata sandi pola", "Wallet_Lock_Verify_byPwd": "Verifikasi Kat<PERSON> Sand<PERSON>", "Wallet_Lock_Verify_Btn_FaceID": "<PERSON><PERSON><PERSON><PERSON><PERSON> wajah", "Wallet_Lock_Verify_Btn_TouchID": "Verifikasi sidik jari", "Wallet_Lock_PatternLock_Setup_Title": "Tetapkan kata sandi pola", "Wallet_Lock_PatternLock_Reset_Title": "Lupa Kata Sandi Pola?", "Wallet_Lock_PatternLock_Confirm_Title": "Ma<PERSON>kkan lagi untuk konfirmasi", "Wallet_Lock_PatternLock_Confirm_Error_Tips": "Sandi tidak konsisten. Tetapkan lagi.", "Wallet_Lock_PatternLock_Verify_Error_Tips": "Kata sandi salah. Anda memiliki {} kesempatan untuk memasukkan lagi.", "Wallet_Lock_PatternLock_Verify_Limit_Tips": "<PERSON><PERSON><PERSON><PERSON> sering mencoba. Coba lagi setelah {} menit.", "Wallet_Lock_PatternLock_Modify_AfterVerify_Title": "Tetapkan kata sandi pola baru", "Wallet_Lock_PatternLock_InvalidPattern_Tips": "Diperlukan setidaknya 4 poin. Tetapkan lagi.", "Wallet_Lock_PatternLock_Setup_Succ_Tips": "Kata sandi pola ditetapkan", "Wallet_Lock_PatternLock_Modify_Succ_Tips": "Kata sandi pola diubah", "Wallet_Lock_PatternLock_Setup_Cancel_Tips": "Jangan aktifkan kunci pola?", "Wallet_Lock_PatternLock_Setup_Cancel_LeftBtn": "Aktifkan", "Wallet_Lock_PatternLock_Setup_Cancel_RightBtn": "<PERSON><PERSON>", "Wallet_Lock_Not_Support_TouchID_Tip": "Touch ID tidak tersedia untuk perangkat ini. Atur ulang kunci keamanan.", "Wallet_Lock_Not_Support_FaceID_Tip": "Face ID tidak tersedia untuk perangkat ini. Atur ulang kunci keamanan.", "Wallet_Lock_Close_Wallet_Lock_Tip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Wallet_Lock_Setup_Pattern_Lock_Tip": "Tetapkan Kata Sandi Pola", "Wallet_Lock_TouchID_NotEnrolled_Tip": "Touch ID tidak diaktifkan. Aktifkan Touch ID pada pengaturan sistem atau atur ulang kunci keamanan Anda.", "Wallet_Lock_FaceID_NotEnrolled_Tip": "Face ID tidak diaktifkan. Aktifkan Face ID pada pengaturan sistem atau atur ulang kunci keamanan Anda.", "Wallet_Lock_FingerPrint_NotEnrolled_Tip": "Tidak ada sidik jari di sistem. Simpan sidik jari Anda dan tetapkan kunci keamanan lagi.", "Wallet_Lock_PatternLock_VerifyBlock_Tip": "Terlalu sering mencoba. Verifikasi identitas Anda di \"Atur Ulang Kata Sandi Pola\" atau coba lagi dalam 10 menit.", "Wallet_Lock_PatternLock_VerifyBlock_Reset_Tip": "Atur Ulang Kata Sandi Pola", "Wallet_Lock_New_FingerPrint_Authen_Tips": "Sidik jari baru dimasukkan. Masukkan kata sandi pembayaran untuk memverifikasi identitas.", "Wallet_Lock_New_TouchID_Authen_Tips": "Informasi sidik jari di perangkat ini telah diubah. masukkan sandi pembayaran untuk memverifikasi identitas Anda.", "Wallet_Lock_New_FaceID_Authen_Tips": "Informasi wajah di perangkat ini telah diubah. masukkan sandi pembayaran untuk memverifikasi identitas Anda.", "Wallet_Lock_Forget_Pwd": "Password dimenticata", "Wallet_Lock_Retry_Pwd": "<PERSON><PERSON><PERSON><PERSON>", "LQT_Detail_Operation_More_Product_Title": "<PERSON><PERSON><PERSON>", "pay_settings_biometric_pay_touchid_title": "<PERSON><PERSON>bay<PERSON>", "pay_settings_biometric_pay_faceid_title": "<PERSON><PERSON><PERSON><PERSON>", "pay_settings_biometric_pay_multi_title": "<PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON>", "pay_settings_biometric_pay_touchid_desc": "<PERSON><PERSON>, <PERSON><PERSON> dapat menggunakan verifikasi sidik jari untuk membuat pembayaran menjadi lebih cepat.", "pay_settings_biometric_pay_faceid_desc": "<PERSON><PERSON>, <PERSON><PERSON> dapat menggunakan verifikasi wajah untuk membuat pembayaran menjadi lebih cepat.", "pay_settings_biometric_pay_multi_desc": "Aktifkan pembayaran wajah atau sidik jari untuk membuat pembayaran menjadi lebih cepat.", "pay_settings_biometric_pay_enable_faceid": "Aktifkan <PERSON>bayaran Waja<PERSON>", "pay_settings_biometric_pay_enable_touchid": "Aktifkan Pembayaran Sidik <PERSON>", "common_reddot_accessibility": "Anda memiliki pesan baru", "common_help": "Bantu", "bind_new_card_input_name": "Masukkan nama yang diregistrasi dengan bank", "paydesk_title_accessibility_selected": "<PERSON><PERSON><PERSON><PERSON>", "RedDot_New": "NEW", "renewal_or_sign_time_item_key": "Penerbitan/Perpanjangan Izin", "WCPay_Option_Item": "Opsional", "VoiceOver_OfflinePay_Unselected": "Tidak dipilih", "FillCard_Number_Reg_Hint_Self": "Masukkan nomor kartu Anda", "common_continue": "lanjutkan", "WCPay_Risk_Dialog_Title": "Potensi risiko telah terdeteksi. Harap konfirmasi apakah Anda ingin melanjutkan. <PERSON><PERSON> <PERSON>, kami perlu memverifikasi identitas Anda.", "WCPay_Risk_Not_Support_Dialog_Title": "Potensi risiko telah terdeteksi. Harap selesaikan risiko sebelum melanjutkan.", "WCPay_Risk_Failed_Dialog_Title": "Verifikasi identitas gagal, operasi harus dihentikan Tutup", "bind_card_agreement_protocal_and_next": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON>", "wallet": "Dompet", "awaiting_real_name_verification": "Menunggu verifikasi identitas", "five_stars": "*****", "Card_UserAgreement_Read_And_Agreement_Title": "<PERSON><PERSON><PERSON><PERSON> per<PERSON> untuk menambahkan", "FaceCheck_Common_Error": "Sistem sibuk. Coba lagi.", "FaceCheck_MP_Request_Use": " <PERSON><PERSON> a<PERSON>", "FaceCheck_MP_Confirm_Tip": "___<BRAND>___ menggunakan pengenalan wajah untuk memverifikasi identitas Anda. Hal ini hanya boleh ditetapkan oleh pemilik akun.", "FaceCheck_MP_Front_Feedback": "Pusat <PERSON>", "FaceCheck_Recoging": "<PERSON><PERSON><PERSON>...", "waiting_for_real_name_authentication": "Menunggu verifikasi identitas", "collect_sub_title": "<PERSON><PERSON><PERSON>", "collect_main_add_desc_title_simple_change": "Ubah", "collect_main_add_desc_title": "Tambahkan Catatan", "remittance_amount_lowest_limit": "Jumlah transfer terendah adalah ￥0,01.", "collect_main_fixed": "Tetap<PERSON>", "collect_main_first_enter_tips_title": "<PERSON><PERSON><PERSON>", "collect_main_first_enter_tips_new": "<PERSON>ang yang diterima akan didepositokan di ___<BRAND_Balance>___ <PERSON><PERSON> (\"<PERSON><PERSON>\" > \"<PERSON>ana<PERSON>\" > \"<PERSON><PERSON>\"), yang mana uang tersebut dapat digunakan ataupun ditarik.", "collect_main_close_ring_tone": "Nonaktifkan Bunyi Pemberitahuan Tanda Terima", "collect_main_close_ring_tone_tips": "Dinonaktifkan", "collect_main_open_ring_tone": "Aktifkan Bunyi Pemberitahuan Tanda Terima", "collect_main_open_ring_tone_tips": "Diaktifkan. Periksa apakah volume media Anda aktif.", "collect_main_qrcode_usage_other": "<PERSON><PERSON><PERSON>", "collect_main_qrcode_usage_other_placeholder": "Tambah info (hingga 16 karakter)", "collect_main_payer_desc_default_placeholder": "Tambahkan catatan untuk penerima.", "collect_qrcode_save_failed": "Gagal menyimpang", "collect_material_guide_save_text_toast": "Disimpan ke Album", "collect_mch_module_title": "Kode QR Bisnis", "collect_personal_module_title": "Kode <PERSON>", "collect_setting_title": "<PERSON><PERSON><PERSON><PERSON>", "collect_main_fixed_cancel": "<PERSON><PERSON>", "collect_main_more_function": "<PERSON><PERSON><PERSON><PERSON>", "collect_main_save_qrcode": "Simpan Ko<PERSON> Pembay<PERSON>", "collect_main_receive_title": "Total", "collect_main_paying": "Membayar...", "collect_main_pay_suc": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON>", "collect_main_pay_cancel": "Batalkan Pembayaran", "collect_main_loading_title": "Memuat kode QR...", "collect_main_ring_not_support": "Tidak didukung pada sistem ini", "WCPay_Transfer_To_Format": "Transfer ke {}", "WCPay_Transfer_Weixin_Alias_Prefix": "___<BRAND_ID>___:", "WCPay_Transfer_InputAmount_Tips": "<PERSON><PERSON><PERSON><PERSON> jumlah te<PERSON><PERSON>h da<PERSON>u", "WCPay_Transfer_Cashier_Desc_Format": "Transfer ke {}", "WCPay_Transfer_Succ_Tips": "<PERSON><PERSON><PERSON> penerimaan uang dari {}", "WCPay_Service": "<PERSON><PERSON><PERSON>", "recognize_and_pay": "<PERSON><PERSON> dan bayar", "bizf2f_input_ui_page_to_person": "Bayar ke Perorangan", "bizf2f_input_ui_page_add_remark": "Tambahkan Catatan", "bizf2f_input_ui_page_amount_title": "<PERSON><PERSON><PERSON>", "WCPay_Verify_Password_Get_SMS_Code": "Terima kode verifikasi", "WCPay_VerifyCode_Header_Description": "Transaksi memerlukan verifikasi SMS.", "bizf2f_input_ui_page_pay_action": "Bayar", "bizf2f_input_ui_page_change_remark": "Ubah", "bizf2f_input_ui_page_pay_title": "Bayar", "bizf2f_favor_title": "<PERSON><PERSON><PERSON>", "bizf2f_favor_total_fee": "Total Jumlah", "bizf2f_favor_calculating": "Menghitung...", "bizf2f_favor_select_favor": "<PERSON><PERSON><PERSON>", "UN_BIND_CARD_TITLE": "Putuskan Hubungkan Kartu Bank", "WCPay_system_version_limitation_tip": "Untuk fitur lebih lan<PERSON>, lihat pada HarmonyOS 4.2 atau versi lebih lama, atau perangkat lain.", "reconfirm_payment_amount_title": "Kon<PERSON><PERSON><PERSON> ulang jumlah pem<PERSON>", "reconfirm_payment_amount_des": "<PERSON><PERSON> k<PERSON> as<PERSON>, pastikan jumlahnya guna mengh<PERSON> k<PERSON>han.", "reconfirm_amount_page_tip": "<PERSON><PERSON> perat<PERSON>n, pembayaran yang melebihi batas kode QR statis harus dilakukan dengan memindai kode QR dinamis di bawah ini. Ketuk tombol untuk memverifikasi dan menyelesaikan pembayaran.", "Hongbao_SendUI_NavigationBar_Title": "<PERSON><PERSON>", "Hongbao_SendUI_Send_Button_Titlle": "Siapkan Paket Merah", "Hongbao_SendUI_Count_Title": "Kuantitas ", "Hongbao_SendUI_Amount_Title_Group": "Total", "Hongbao_SendUI_Amount_Title_Single": "<PERSON><PERSON><PERSON>", "Hongbao_SendUI_RandomLuckyMode_Title": "<PERSON><PERSON><PERSON>", "Hongbao_SendUI_Count_Tips": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "Hongbao_SendUI_Amount_Tips": "¥0,00", "Hongbao_SendUI_Default_Wishing": "<PERSON><PERSON> hangat", "Hongbao_Per_Hongbao_Max_Amount_Format": "Hingga {}CNY untuk masing-masing Paket Me<PERSON>", "HongBao_SendTips": "<PERSON><PERSON> yang <PERSON>", "HongBao_OpenTips": "<PERSON><PERSON>", "HongBao_AmoutTips": "CNY", "HongBao_MainTitle": "<PERSON><PERSON>", "HongBao_Cashier_Desc": "___<BRAND_hongbao>___", "Hongbao_SendUI_RandomMode_Title": "<PERSON><PERSON><PERSON>", "Hongbao_SendUI_NormalMode_Title": "<PERSON><PERSON><PERSON> ya<PERSON>", "Hongbao_SendUI_ExclusiveMode_Title": "Eksklusif", "Hongbao_SendUI_Select_Count_Suffix": "", "Hongbao_SendUI_Group_Member_Count_Format": "Grup ini memiliki {} anggota", "Hongbao_SendUI_Amount_Title_Group_Normal": "<PERSON><PERSON><PERSON>", "Hongbao_SendUI_Perperson_Maxvalue_Error_Tips": "Hingga {}CNY untuk masing-masing Paket Me<PERSON>", "Hongbao_SendUI_Perperson_Minvalue_Error_Tips": "Setidaknya {:.2f} untuk setiap Paket Merah", "Hongbao_SendUI_Count_Larger_Than_Group_Mem_Count_Error_Tips": "<PERSON><PERSON><PERSON> tidak boleh melebihi jumlah anggota grup.", "Hongbao_SendUI_Total_Num_Error_Tips": "Kuantitas maksimal adala<PERSON> {}.", "Hongbao_SendUI_Select_Count_Data_Invalid_Error_Tips": "\"Kuantitas\" belum dimasukkan", "Hongbao_SendUI_Amount_Data_Invalid_Error_Tips": "\"Total\" belum dimasukkan", "Hongbao_SendUI_Select_Count_Zero_Error_Tips": "<PERSON><PERSON><PERSON>.", "Hongbao_SendUI_Amount_Larger_Than_Max_Amount_Error_Tips": "Jumlah total tidak boleh melebihi {}CNY.", "Hongbao_ReceiveModal_Detail_Link": "<PERSON><PERSON> r<PERSON>", "Hongbao_DetailUI_Load_More_Text": "Klik untuk memuat lebih banyak", "TransferPhone_Entry_Title": "Pilih Metode Transfer", "TransferPhone_To_Bank_Title": "Transfer ke Kartu Bank", "TransferPhone_To_Bank_Desc": "Masukkan kartu bank penerima untuk mentransfer ke akun bank mereka.", "TransferPhone_To_Phone_Title": "Transfer ke Nomor Ponsel", "TransferPhone_To_Phone_Desc": "<PERSON><PERSON><PERSON>n nomor ponsel penerima untuk mentransfer ke ___<BRAND_Balance>___ mereka.", "TransferPhone_To_PaySetting": "Pengaturan Transfer dengan Nomor Ponsel", "WCPay_ThirdParty_Tips_Title": "<PERSON><PERSON><PERSON>", "WCPay_Service_Manage": "<PERSON><PERSON><PERSON>", "identify_and_pay": "<PERSON><PERSON> dan bayar", "Hongbao_SendUI_Exclusive_Mode_Send_To_Who": "<PERSON><PERSON> ke", "Hongbao_SendUI_Exclusive_Mode_Send_To_Who_Error_Tips": "\"<PERSON><PERSON> ke\" tidak dipilih", "Hongbao_SendUI_Amount_Data_Invalid_Error_Tips_2": "\"Jumlah\" belum dimasukkan", "MerchantPay_Input_Remark_Hint_Format": "Dapat dilihat oleh penerima. Maks {} karakter.", "MerchantPay_Input_Remark_Title": "Tambahkan Catatan", "MerchantPay_Transfer_To_Format": "Bayar ke {}", "Greeting_Hongbao_Random_Change_Amount": "<PERSON><PERSON>", "Greeting_Hongbao_Who_Receive_Hongbao_Format": "<PERSON><PERSON><PERSON> o<PERSON> {}", "set_amount": "Tetap<PERSON>"}