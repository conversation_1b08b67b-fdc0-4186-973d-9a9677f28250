{"BindCard_Gender_Female": "<PERSON><PERSON><PERSON>.", "BindCard_Gender_Male": "Муж.", "Choose_Deposit_Time": "Задать время", "Choose_Payment": "Выбрать способ зачисления", "Continue_Pay": "Продолжить оплату", "Day": "{}", "Each_Day_In_Month_Deposit": "{} каждого месяца", "Each_WeekDay_Deposit": "Кажд. {}", "ExposureInfo_Waiting_Wording": "Подождите...", "Fetch_Balance": "Вывести средства баланса", "Fetch_Balance_Bank_Proccessing": "Банк выполняет обработку", "Fetch_Balance_Open_Order": "Запрос на вывод средств", "Fetch_Balance_Success": "Средства поступили на счет", "FillCard_Info_ErrorTips_Format": "{} (всего ошибок: {})", "FillCard_Number_Default_Mobile_Modify_Tips": "Если моб. номер неверный, нажмите для редактирования.", "FillCard_Number_Reg_Hint": "Номер вашей банковской карты", "FillCard_Number_Unreg_Hint": "Номер банковской карты владельца учетной записи ___<BRAND>___", "Friday": "Пятница", "Give_Up": "Отказаться", "HHC_Check_PWD_To_Add_Plan": "Введите платежный пароль, чтобы запустить опцию Тратить и экономить", "HHC_Check_PWD_To_Edit_Plan": "Введите платежный пароль, чтобы изменить опцию Тратить и экономить", "HHC_Check_PWD_To_Pause_Plan": "Введите платежный пароль, чтобы приостановить опцию Тратить и экономить", "HHC_Check_PWD_To_Start_Plan": "Введите платежный пароль, чтобы включить опцию Тратить и экономить", "HHC_Choose_Payment": "Выберите карту", "HHC_Deposit_Plan": "Расписание зачислений", "HHC_Did_Modify": "Изменено", "HHC_Did_Open": "Начато", "HHC_Did_Pause": "Приостановлено", "HHC_Name": "Тратить и экономить", "HHC_Plan_Check_Amount": "Неверная сумма. Проверьте и попробуйте снова.", "HHC_Plan_Set_Bank_Card_Tip": "Чтобы использовать опцию Тратить и экономить, сначала выберите депозитную карту.", "LQT_Fixed_Deposit": "Периодические платежи", "LQT_Fixed_Deposit_Check_PWD_To_Add_Plan": "Введите платежный пароль, чтобы включить периодические платежи", "LQT_Fixed_Deposit_Check_PWD_To_Delete_Plan": "Введите платежный пароль, чтобы удалить график.", "LQT_Fixed_Deposit_Check_PWD_To_Pause_Plan": "Введите платежный пароль, чтобы приостановить платежи по графику", "LQT_Fixed_Deposit_Check_PWD_To_Start_Plan": "Введите платежный пароль, чтобы включить платежи по графику.", "LQT_Fixed_Deposit_Did_Delete": "Удалено", "LQT_Fixed_Deposit_Did_Modify": "Обновлено", "LQT_Fixed_Deposit_Did_Open": "Включено", "LQT_Fixed_Deposit_Did_Pause": "Приостановлено", "LQT_Fixed_Deposit_No_Plan": "Нет графика платежей", "LQT_Fixed_Deposit_Plan": "Гра<PERSON>и<PERSON> платежей", "LQT_Fixed_Deposit_Plan_Set_Bank_Card_Tip": "Чтобы настроить периодические платежи, сперва необходимо выбрать карту, с которой будут списываться средства.", "LQT_Fixed_Deposit_Plan_Set_Time_Tip": "Чтобы настроить периодические платежи, сперва необходимо выбрать время платежа.", "LQT_Fixed_Deposit_Plan_Should_Input": "Чтобы настроить периодические платежи, сперва введите сумму платежа.", "ModifyPwdUseCase_ModifyPwd_Desc": "Введите платежный пароль, чтобы подтвердить вашу личность", "ModifyPwdUseCase_ModifyPwd_GiveUp_Title": "Отменить изменение пароля?", "ModifyPwdUseCase_ModifyPwd_Success": "Пароль изменен", "ModifyPwdUseCase_ModifyPwd_Title": "Изменить пароль", "Monday": "Понедельник", "Monthly": "Ежемесячно", "OfflinePay_CreateOfflinePay_ConfirmBtn_Title": "Включить сейчас", "OfflinePay_CreateOfflinePay_Euro_Tips": "Функция быстрой оплаты не включена. Включите эту функцию и покажите код для быстрой оплаты кассиру. (Транзакции поддерживаются только в юанях)", "OfflinePay_CreateOfflinePay_Tips": "Функция быстрой оплаты не включена. Включите эту функцию и покажите кассиру код для быстрой оплаты.", "OfflinePay_ReCreateOfflinePay_ConfirmBtn_Title": "<PERSON><PERSON><PERSON>.", "OfflinePay_ReCreateOfflinePay_Euro_Tips": "Функция \"Быстрая оплата\" отключена. Включите ее для быстрой и удобной оплаты продавцам - просто покажите код для оплаты. Поддерживаются только транзакции в юанях.", "OfflinePay_ReCreateOfflinePay_Tips": "Функция \"Быстрая оплата\" отключена. Включите ее для быстрой и удобной оплаты продавцам - просто покажите код для оплаты.", "Saturday": "Суббота", "Sunday": "Воскресенье", "Thursday": "Четверг", "Tuesday": "Вторник", "WCPay_BankCardBindTo0_0_5D_Detail": "Для подтверждения учетной записи будет списано $0,05", "WCPay_BankCardBindTo0_0_5D_Detail_back": "Для подтверждения учетной записи будет списано $0,05. После подтверждения средства будут возвращены.", "WCPay_BankCardBindTo1B_Detail": "Для подтверждения учетной записи будет списано ¥0,01. После подтверждения средства будут возвращены.", "WCPay_BankCardBindTo1B_NotReturnDetail": "Для подтверждения учетной записи будет списано ¥0,01.", "WCPay_CountryCode_Title": "Страна/регион", "WCPay_FaceID_Auth_Tip": "Идентифицировать лицо для оплаты", "WCPay_GiveUpReset_Title": "Отменить сброс платежного пароля?", "WCPay_NeedChangeCard_Error_Btn": "Изменить способ оплаты", "WCPay_TouchID_Auth_Tip": "Проверить существующий отпечаток пальца для оплаты", "WCPay_TouchID_Confirm_Alert_Cancel": "Отмена", "WCPay_TouchID_Confirm_Alert_Content": "Отпечаток пальца проверен. Подтвердить платеж?", "WCPay_TouchID_Confirm_Alert_OK": "Подтвердить", "WCPay_TouchID_Confirm_Alert_Title": "Оплатить", "WeChatPay_Name": "___<BRAND_Pay>___", "Wednesday": "Среда", "Weekly": "Еженедельно", "address_item_key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "address_item_place_holder": "Введите адрес", "agree": "Согласиться", "agree_user_protocal": "Принять пользовательское соглашение", "agreement_alert": "Сначала изучите и примите \"Пользовательское соглашение\".", "alertChargeFee": "Плата за услугу", "area_item_key": "Рай<PERSON>н", "ask_verify_fingerprint": "Проверить отпечаток пальца", "assign_pay_dialog_content": "Введенные идентификационные данные отличаются от привязанных к текущей учетной записи ___<BRAND>___. Используйте учетную запись ___<BRAND>___, привязанную к банковской карте, и повторите проверку.", "balance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bank_card_item_key": "Банковская карта", "bank_select_item_key": "<PERSON><PERSON><PERSON><PERSON>", "bind_new_card": "Добавьте банковскую карту", "bind_new_card_section_footer": "Из соображений безопасности привязка текущих карт будет отменена.", "bind_new_card_section_header": "Привяжите новую карту для восстановления учетной записи", "bind_new_card_to_pay_tip": "Введите платежный пароль, чтобы подтвердить вашу личность", "bind_new_card_to_reset_mobile_desc": "Привяжите мобильный номер к новой карте.", "binded_card_list_page_title": "Выберите мобильный номер для SMS-проверки", "birth_date_item_key": "Дата рождения", "can_not_bind_more_card": "Привязано максимальное количество карт.", "can_not_get_sms_with_question_mard_word": "Проверочный код не получен?", "can_not_get_sms_word": "Проверочный код не получен", "cancel_time": "Время отмены: {}", "cannot_receive_sms_code_content": "Ваш код проверки был отправлен на номер мобильного телефона, зарегистрированный в банке. Удостоверьтесь, что вы используете этот моб. номер на данный момент, и сообщения не заблокированы приложениями безопасности. Если вы больше не используете этот номер телефона, обратитесь в банк. Для получения подробной справки позвоните в службу поддержки клиентов ___<BRAND_Copyright_Suffix>___ по тел. +86-0755-95017.", "cannot_receive_sms_code_title": "Не удалось получить код подтверждения", "card_holder_dialog_content": "1. Для безопасности фондов к ___<BRAND_ID>___ можно привязать только банковские карты с одинаковым именем.\n\n2. Чтобы привязать банковскую карту с другим именем, необходимо обновить информацию о вашем настоящем имени.\n\n3. После изменения информации о настоящем имени, информация о предыдущем владельце карты будет удалена, и вы сможете добавлять карты только с именем нового владельца.", "card_holder_dialog_title": "Имя на карте", "card_holder_item_key": "Влад<PERSON><PERSON><PERSON><PERSON> карты", "card_holder_section_header": "Введите данные, ранее предоставленные банку. В будущем возможно добавление карт только на это имя.", "card_info_section_header": "Введите данные карты", "card_num_item_key": "Но<PERSON>ер карты", "card_number_input_tips_title": "Оплата или дистанционное банковское обслуживание не требуются", "card_select_item_key": "<PERSON>и<PERSON> карты", "card_type_section_header": "Выберите тип карты", "change_realname_word": "Изменить настоящее имя", "change_to_face_id": "Исп. лицо", "change_to_pwd": "Использовать пароль", "change_to_touch_id": "Использовать отпечаток пальца", "check_pay_pwd_page_desc": "Введите платежный пароль для подтверждения личности", "check_pay_pwd_page_title": "Удостоверение личности", "check_sms_desc": "Проверочный код был отправлен на мобильный номер, зарегистрированный в банке.\n\n1. Подтвердите, что это текущий номер мобильного телефона, который был зарегистрирован в банке.\n\n2. Проверьте, не было ли SMS-сообщение заблокировано приложением безопасности на мобильном устройстве.\n\n3. Если к этому номеру нет доступа прямо сейчас, обратитесь в банк.\n\n4. За дополнительной консультацией обращайтесь в службу поддержки клиентов по телефону 95017.", "check_sms_page_desc": "Для привязки банковской карты требуется SMS-проверка. Проверочный код отправлен на номер {}. Следуйте инструкциям.", "check_sms_page_favor": "Вы заплатите {}{:.2f} (Сэкономлено {}{:.2f})", "check_sms_page_title": "Проверьте моб. номер", "common_back": "Назад", "common_cancel_word": "Отмена", "common_close": "Закрыть", "common_done_word": "Готово", "common_drop_out_word": "Выход", "common_i_know_word": "Понятно", "common_more": "Еще", "common_next_word": "Далее", "common_question_word": "Вопросы", "common_select": "Выбрать", "common_tip_word": "Напоминание", "confirm_mobile_no": "Подтвердить моб. номер", "confirm_pay": "Оплатить сейчас", "confirm_to_receive": "Подтвердить получение", "confrim_pay_and_open_deduct_word": "Оплатить и включить", "confrim_pay_word": "Оплатить", "coupon_change_should_change_payment": "Сумма оплаты изменилась. Выберите повторно.", "coupon_component_need_bank_pay_tips": "Скидка для указанного метода оплаты", "cre_id_item_key": "Номер удостоверения личности", "cre_id_item_place_holder": "Введите номер уд-ия личности", "cre_type_item_key": "Тип удостоверения", "cre_type_item_place_holder": "Выбрать тип ID", "cvv_dialog_content": "CVV-код — это 3- или 4-значный защитный код на тыльной или лицевой стороне карты.", "cvv_dialog_title": "Что такое CVV-код?", "cvv_item_key": "CVV-код ", "cvv_item_place_holder": "3 или 4 цифры, обычно на тыльной стороне карты", "default_delay_transfer_confirm_desc": "Перевод поступит через {} ч", "email_item_key": "Адрес эл. почты", "email_item_place_holder": "Введите адрес эл. почты", "error_detail_title": "Посмотреть решение", "face_hongbao": "Пакеты рядом", "fast_bind_card_support_bank_title_text": "На данный момент поддерживаются следующие банки", "fill_card_and_user_info_title": "Заполните банковские и персональные данные", "fill_card_info_card_holder_assign_pay_header": "Оплату должен произвести указанный владелец карты.", "fill_card_info_page_favor_desc": "Используйте этот вид банковской карты, чтобы сэкономить дополнительно {}{:.2f}.", "fill_card_info_page_realname_cre_not_support": "Невозможно использовать {} для привязки этой карты", "fill_card_info_page_title": "Введите данные карты", "fill_card_num_format_error": "Неверный номер карты", "fill_card_num_of_card_holder_section_header": "Введите номер банковской карты владельца", "fill_card_num_page_desc": "Привязать банковскую карту", "fill_card_num_page_favor_dialog_title": "Сэкономить с этой картой", "fill_card_num_page_realname_desc": "Для выполнения проверки подлинности в реальном времени необходимо добавить банковскую карту.", "fill_card_num_page_sns_input_hint": "Поддерживаются только дебетовые карты", "fill_card_num_page_title": "Добавьте банковскую карту", "fill_card_number_assign_pay": "Использовать банковскую карту {} для оплаты", "fill_card_number_more_favor": "Используйте заданную банковскую карту для получения скидок", "fill_complete_name": "Введите ФИО", "fill_id_format_error": "Неверный формат номера удостоверения личности.", "fill_in_sms_key": "Код проверки", "fill_in_sms_word": "Введите код проверки", "fill_phone_num_format_error": "Неверный формат мобильного номера.", "finger_print_err_tips": "Повторить попытку", "first_name_item_key": "Имя", "first_name_item_place_holder": "Введите имя", "float_paydesk_modal_no_select_favor": "Неиспользуемые скидки", "foreign_mobile_header": "Введите новый моб. номер", "forget_pay_pwd_title": "Забыли платежный пароль", "get_sms_with_count_down_word": "Получить проверочный код \n({})", "get_sms_word": "Получить проверочный код", "give_up_on_new_card": "Остановить привязку карты?", "give_up_this_order_or_not": "Отказаться от этой транзакции?", "group_aa": "Разделить счет", "has_send_sms": "Отправлено", "has_send_sms_with_count": "Отправлено ({})", "hongbao_refund_way_header_title": "Красные пакеты, которые не были открыты в течение 24 часов после отправления, будут возвращены способом, указанным ниже.", "hongbao_refund_way_title": "Вернуть Кр. пакет", "id_card_name": "Идентификационная карта", "install_cert_error": "Не удалось установить сертификат", "last_name_item_key": "Фамилия", "last_name_item_place_holder": "Введите фамилию", "loading_title": "Загрузка...", "lottery_network_error": "Извините, желаем удачи в следующий раз!", "lqt": "Мин<PERSON>-фонд", "lqt_reset_mobile_desc": "Выберите банковскую карту. Проверьте мини-фонд, используя мобильный номер, привязанный к банковской карте.", "mobile_dialog_content": "Этот мобильный номер, зарегистрированный в банке при регистрации счета, вы указывали при подаче заявки на банковский счет. Если вы не предоставляли номер телефона, забыли его или больше не имеете к нему доступа, обратитесь в банк и обновите моб. номер.", "mobile_dialog_title": "Моб. номера", "mobile_item_key": "Моб. номер", "mobile_item_place_holder": "Введите мобильный номер, зарегистрированный в банке", "name_item_key": "Имя", "name_item_place_holder": "Введите имя на карте", "nationality_item_key": "Страна/регион", "nationality_place_holder": "Введите страну/регион", "new_mobile_item_key": "Новый моб. номер", "new_mobile_item_place_holder": "Введите мобильный номер, зарегистрированный в банке", "new_user_card_num_input_safety_desc": "Привяжите карту, принадлежа<PERSON><PERSON>ю владельцу учетной записи ___<BRAND>___", "new_user_card_num_input_safety_desc_v2": "Привяжите карту, принадлежа<PERSON><PERSON>ю владельцу учетной записи ___<BRAND>___", "no": "Нет", "offline_choose_payment": "Выберите способ платежа по умолчанию", "offline_choose_payment_fail": "Если не удалось использовать способ платежа по умолчанию, система попытается использовать другие способы выполнения платежа.", "offline_click_view_code": "Нажмите для просмотра кода платежа", "offline_pay_modify_limit": "Изменить сумму", "offline_pay_only_pay_title": "Оплатить", "offline_pay_select_card_invalid": "В данный момент невозможно использовать этот способ платежа. Попробуйте использовать другой способ платежа.", "offline_pay_title": "Деньги", "offline_pay_to_merchant": "Заплатить продавцам", "offline_prefer_payment": "Способ платежа по умолчанию", "offline_show_code_warning": "Показывайте код платежа кассиру только при выполнении платежа. В целях безопасности не сообщайте этот код другим лицам.", "offline_view_code_warning": "Показывайте код платежа кассиру только при выполнении платежа. В целях безопасности не сообщайте этот код другим лицам.", "ok": "ОК", "order_address_section_header": "Адрес для выставления счетов", "pay_card_detail_contact_user_info": "Обратиться в службу поддержки клиентов: ", "pay_card_detail_tel_number": "95017", "pay_power_by_tenpay_word": "Предоставлено Tenpay", "pay_success": "Платеж выполнен успешно", "paydesk_coupon_page_title": "Страница платежных скидок", "paydesk_float_page_title": "Пароль для опл.", "paydesk_main_page_more_favor": "Больше скидок", "paydesk_main_page_title": "Страница платежей", "paydesk_payment_page_title": "Список платежных карт", "paydesk_sub_page_title": "Подстраница платежей", "payee_remark_title": "Примечания плательщика", "paying_alert_tips": "Платеж уже отправлен. Дождитесь сообщения о результате платежа и проверьте, нужно ли отправлять его повторно.", "payment_method": "Метод оплаты", "phone_number_item_key": "Телефон", "phone_number_item_place_holder": "Введите номер телефона", "profession_item_key": "Профессия", "pure_bind_card_succ_tips": "Привязка успешно завершена", "pwd_repeat_error_tip": "Пароли не совпадают", "rebind_bank_card_section_header": "Привяжите карту еще раз для восстановления учетной записи", "receipt": "Получить деньги", "receive_done": "Получено", "receive_remark_title": "Примечания к получению платежа", "receive_time": "Время получения: {}", "receiver_remark_title": "Примечания получателя", "refund_doing_tip": "Обработка возмещения. Сумма будет возвращена на карту в течение 1-3 рабочих дней.", "refund_done": "Возмещено", "refund_done_and_expired": "Возвращено (истек срок)", "refund_time": "Время возврата: {}", "refund_transfer_or_not": "Вернуть перевод от {}?", "refund_word": "Возврат", "renewal_time_item_key": "Количество изменений", "resend_message_or_not": "Повторить отправку этого сообщения?", "resend_sms": "Отправить повторно", "resend_sms_with_count": "Отправить повторно ({})", "resend_success_tip": "Сообщение отправлено повторно", "resend_word": "Отправить повторно", "reset_ban_mobile_fill_card_info_credit_tip_header": "Введите данные банковской карты для проверки", "reset_ban_mobile_fill_card_num_tip_header": "Добавьте новую банковскую карту и используйте мобильный номер, зарегистрированный в банке, для проверки балансовых платежей.", "reset_cvv_and_valid_date_tip": "Вы одновременно обновляете данные привязанной карты и совершаете платеж. При наличии сомнений позвоните в службу поддержки клиентов банка: ", "reset_cvv_title": "Изменить CVV-код", "reset_lqt_mobile_fill_card_num_tip_header": "Добавьте новую карту и используйте привязанный к ней мобильный номер для проведения SMS-проверок мини-фонда.", "reset_mobile_bank_card_number": "Карта", "reset_mobile_card_desc_format": "{}{} ({}) зарегистрированный моб. номер", "reset_mobile_card_desc_with_update_format": "{}{} ({}) зарегистрированный моб. номер. ", "reset_mobile_new_mobile_info_btn": "Сведения", "reset_mobile_new_mobile_number": "Новый номер телефона", "reset_mobile_phone_page_title": "Изменить моб. номер", "reset_phone_tip": "Вы можете произвести оплату после удостоверения личности. Чтобы подтвердить номер телефона, зарегистрированный в банке, позвоните ", "reset_pwd_fill_rebind_card_info_page_title": "Введите данные банковской карты", "reward": "Код награды", "safety_dialog_content": "Меры безопасности: защита учетной записи, мониторинг в реальном времени, экстренная заморозка. \n\nДвухэтапная аутентификация: для каждого платежа требуется ваш пароль. Для крупных платежей необходима SMS-проверка. \n\nЗащита конфиденциальности: для защиты данных пользователей используется стойкое шифрование данных. \n\nСтрахование платежей: платежи страхуются компанией PICC.", "safety_dialog_title": "Меры защиты", "scan_card_num_title": "Сканировать карту", "select_payment": "Выберите метод оплаты", "select_payment_card": "Выберите способ оплаты", "send_verify_code_btn_wording": "Отправить", "send_verify_code_switch_btn_wording": "Изменить метод проверки", "send_verify_code_tips_format": "SMS-код проверки будет отправлен на номер: \n{}", "set_pay_pwd_confirm_page_title": "Введите еще раз и подтвердите", "set_pay_pwd_page_desc": "Задайте пароль ___<BRAND_Pay>___ для подтверждения платежей", "set_pay_pwd_page_title": "Установить платежный пароль", "set_pwd_success": "Параметры обновлены", "succ_page_open_biometric_cancel_btn_title": "Не сейчас", "succ_page_open_biometric_dialog_content": "Для ускорения платежей можно включить оплату с помощью идентификации лица или отпечатков пальцев.", "succ_page_open_biometric_faceid_btn_title": "Оплата с идентификацией лица", "succ_page_open_biometric_touchid_btn_title": "Оплата по отпечаткам пальцев", "succ_page_open_face_id_dialog_content": "Включите оплату с идентификацией лица, чтобы использовать распознавание лица для быстрого и безопасного проведения платежей.", "succ_page_open_face_id_right_btn_title": "Включить оплату с идентификацией лица", "succ_page_open_touch_id_dialog_content": "Включите сенсорную оплату, чтобы использовать распознавание отпечатков пальцев для быстрого и безопасного проведения платежей.", "succ_page_open_touch_id_left_btn_title": "Возможно, позже", "succ_page_open_touch_id_right_btn_title": "Включить сенсорную оплату", "to_be_confirm_receive": "Получение не подтверждено", "transfer": "Перевод средств", "transfer_account": "Сумма перевода", "transfer_amount_input_invalid_hint": "Введена неверная сумма", "transfer_bank": "Перевод на банковскую карту", "transfer_explain": "Добавьте примечание к переводу", "transfer_modify_explain": "Изменить", "transfer_second_left_button": "Отмена", "transfer_second_right_button": "Продолжить", "transfer_second_title": "Напоминание о переводе", "transfer_time": "Время перевода средств: {}", "transfer_ui_title": "Перевод средств друзьям", "understand_safety": "Меры защиты", "update_word": "Обновить", "user_card_type_select_placeholder_v2": "Выберите банковскую карту и тип карты", "user_info_section_header": "Введите персональные данные", "user_protocal": "\"Пользовательское соглашение\"", "valid_date_item_key": "Срок действия", "verify_cre_tip": "Введите последние 4 цифры {} {} для удостоверения личности", "verify_fingerprint_fail": "Сбой при проверке по отпечаткам пальцев", "verify_id_ui_true_name_tips": "{} (Введите ФИО)", "wechat_bank_agreement": "Банковское соглашение", "wechat_mobile_phone_word": "Моб. номер, привязанный к ___<BRAND>___", "wechat_user_agreement": "Пользовательское соглашение ___<BRAND_Pay>___", "wxp_common_cancel": "Отмена", "wxp_common_confirm": "ОК", "wxp_common_i_know": "Понятно", "wxp_common_remind": "Напоминание", "wxp_network_error": "Система занята. Попробуйте позже.", "wxp_payment_network_error": "Транзакция отправлена. Вы получите уведомление о статусе платежа от офиц. уч. записи ___<BRAND_Pay>___. В случае необходимости подтвердите статус платежа, прежде чем повторять платеж.", "wxp_system_error": "Система занята. Попробуйте позже.", "wxp_wcpay_system_error": "Система занята. Попробуйте позже.", "yes": "Да", "zip_item_key": "Почтовый индекс", "zip_item_place_holder": "Введите почтовый индекс", "common_confirm_word": "Подтвердить", "ModifyPwdUseCase_ModifyPwd_GiveUp_Yes": "Отменить изменения", "ModifyPwdUseCase_ModifyPwd_GiveUp_No": "Продолжить", "disagree": "Не соглашаюсь", "card_user_agreement": "Соглашение об обслуживании пользователя", "card_bank_agreement": "Банковское соглашение", "Card_UserAgreement_Title": "Для добавления банковских карт необходимо согласиться с нижеприведенным соглашением.", "pay_settings_delay_transfer_page_title": "Время перевода средств", "pay_settings_delay_transfer_page_desc": "После того как перевод будет принят, средства будут к указанному времени переведены на баланс этого пользователя. После отправки перевод средств нельзя будет отменить, поэтому необходимо внимательно проверить данные получателя перед отправкой.", "pay_settings_delay_transfer_no_delay": "Немедленно", "pay_settings_delay_transfer_two_hour": "Через 2 часа", "pay_settings_delay_transfer_one_day": "Через 24 часа", "pay_settings_biometric_pay_enabled": "Включено", "pay_settings_biometric_pay_disabled": "Отключено", "pay_settings_biometric_pay_multi_support_title": "Оплата по лицу/отпечаткам пальцев", "pay_settings_biometric_pay_faceid_enabled": "Оплата с идентификацией лица включена", "pay_settings_biometric_pay_touchid_enabled": "Оплата по отпечаткам пальцев включена", "pay_settings_biometric_pay_multi_support_desc": "После включения этой функции вы сможете использовать проверку по лицу или отпечаткам пальцев для ускорения платежей.", "f2f_pay_extrabuy_detail_modal_original_price": "(Исходная цена ¥{:.2f})", "common_button": "Кнопка", "Accessibility_Type_SwitchView_Selected": "{}, кнопка переключения, вкл.", "Accessibility_Type_SwitchView_UnSelected": "{}, кнопка переключения, выкл.", "YunShanFu_Loading_Wording": "Открываем QuickPass...", "YunShanFu_Uninstalled_Error": "У вас не установлена программа QuickPass. Установите ее и повторите попытку или совершите платеж с помощью ___<BRAND_Pay>___.", "OfflinePay_Setting_CloseOfflinePay_AlertMessage_ShowFeaturePassword": "После включения защитной блокировки, для доступа к сервису быстрой оплаты будет запрашиваться выбранный метод разблокировки. Отключить?", "OfflinePay_Setting_CloseOfflinePay_AlertMessage": "Отключить быструю оплату?", "OfflinePay_Setting_CloseOfflinePay_BtnTitle": "От<PERSON><PERSON>.", "OfflinePay_Setting_CloseOfflinePay_Cancel": "Продолжить", "OfflinePay_Setting_CloseOfflinePay_OpenWalletLock": "Настроить защитную блокировку", "Wallet_Mix_Paid_UnKnown_Error": "Запрос на транзакцию отправлен. Вы получите уведомление от офиц. уч. записи ___<BRAND_Pay>___ Hong Kong. Не выполняйте платеж повторно, пока статус этого платежа не будет подтвержден.", "bank_card_info": "Примечание", "HHC_Did_Add": "Добавлено", "VoiceOver_OfflinePay_barCode": "Штрихкод оплаты, можно показать кассиру. Нажмите два раза, чтобы показать код платежа в полноэкранном режиме", "VoiceOver_OfflinePay_barCode_short": "Код платежа", "VoiceOver_OfflinePay_Qrcode": "QR-код платежа", "VoiceOver_OfflinePay_barcode_clickHint": "Нажмите дважды, чтобы вернуться", "VoiceOver_OfflinePay_Qrcode_clickHint": "Нажмите два раза, чтобы показать в полноэкранном режиме", "common_link": "Ссылка", "Accessibility_Collapse_Header_Collapsed": "{}, свернуто", "Accessibility_Collapse_Header_Showed": "{}, развернуто", "Pay_Android_Fingerprint_Prompt_Title": "Подтвердите отпечаток пальца", "Pay_Android_Fingerprint_Prompt_SubTitle": "для выполнения платежа.", "Pay_Android_Fingerprint_Prompt_Button": "Используйте пароль", "Accessibility_Collapse_Header_Collapsed({}": "Показать меньше", "Accessibility_Collapse_Header_Showed({}": "Показать все", "Accessibility_State_Disabled": "Сделать темнее", "Accessibility_String_PwdInputView": "Password box, 6 digits in total, {} digits entered", "Accessibility_Type_Button": "{}, button", "Accessibility_Type_CheckBox_Selected": "Check box, selected, {}, button", "Accessibility_Type_CheckBox_UnSelected": "Check box, unselected, {}, button", "Accessibility_Type_Selected": "Selected, {}, button", "Accessibility_Type_UnSelected": "Unselected, {}, button", "common_check_box_check": "Check box, selected", "common_check_box_uncheck": "Check box, unselected", "OfflinePay_ChangePayment_UnConnectedNetwork_Tips": "Сеть недоступна. Невозможно выбрать способ оплаты.", "OfflinePay_EnablePage_UnConnectedNetwork_Tips": "Сеть недоступна. Повторите попытку позже.", "Fetch_Balance_To_Bank": "Средства выведены в", "Fetch_Balance_Amount": "Сумма выводимых средств", "Fetch_Balance_Amount_Tips": "Баланс: ¥{}.", "Fetch_Balance_Amount_Exceed": "Введенная сумма превышает доступный баланс", "Fetch_Balance_Fetch_All": "Вывести все", "HoneyPay_CheckPwd_Unbind_Title": "Отменить привязку карты родственника", "HoneyPay_Modify_CreditLimit_Title": "Ред. месячный лимит", "HoneyPay_Modify_CreditLimit_Desc": "Месячный лимит", "HoneyPay_Modify_CreditLimit_Max_Alert": "Сумма не может превышать ¥{:.2f}", "balance_entry_balnce_title": "<PERSON><PERSON><PERSON> баланс", "balance_entry_balnce_detail": "Транзакции", "balance_entry_powered_by_tenpay": "Предоставлено Tenpay", "balance_recharge_page_title": "Пополнение счета", "balance_recharge_card_info_title": "Способ пополнения счета", "balance_recharge_payment_new_card": "Добавить новую карту", "HoneyPay_Add_Card": "Подарить карту родственника", "HoneyPay_Select_Contact_Title": "Выберите контакты", "HoneyPay_Modify_Comment": "Ред. комментарий", "HoneyPay_MoneyInput_Hint": "Введите сумму", "HoneyPay_CreateCard_Btn": "Отправить", "HoneyPay_Max_Amount_Notice": "Сумма не может превышать ¥{:.2f}", "HoneyPay_Modify_Credit": "Месячный лимит", "HoneyPay_Main_Title": "Карта родственника", "hbrefund_info_tips": "Примечания", "hbrefund_set_button": "Параметры", "hbrefund_time_title": "Время возврата красного пакета", "hbrefund_forbid_way": "Этот способ возврата больше не поддерживается.", "hbrefund_had_set": "Успешно задано", "hbrefund_origin_desc": "Красные пакеты, которые не были открыты в течение 24 часов после отправки, будут возвращены в соответствии с первоначальным способом оплаты.", "hbrefund_set_tips": "После отправки непринятые средства будут возвращены в соответствии с первоначальным способом оплаты. Невозможно переключить на \"Вернуть на баланс\". Продолжить?", "TeenagerPayDetailUIPage_NotSet": "Нет", "TeenagerPayDetailUIPage_LimitedAmount": "¥{:.{}f}", "TeenagerPaySetLimitModal_AmountTitle": "Сумма", "TeenagerPaySetLimitModal_MaxAmount": "До 7 знаков", "TeenagerPayGetDetailUseCase_LimitOn": "<PERSON>и<PERSON><PERSON><PERSON> задан", "TeenagerPayGetDetailUseCase_LimitOff": "Нет лимита", "TeenagerPayUseCase_Set_Ok": "Задано", "TeenagerPayUseCase_Close_Ok": "Лимит суммы отключен", "TeenagerPayUseCase_Limit_Max": "Лимит транзакции не должен превышать ежедневный лимит.", "TeenagerPayUseCase_Limit_Min": "Ежедневный лимит не должен быть меньше лимита транзакции.", "Dcep_Loading_Wording": "Загрузка...", "Dcep_Uninstalled_Error": "У вас не установлена программа E-CNY. Установите ее и повторите попытку или совершите платеж с помощью ___<BRAND_Pay>___.", "TeenagerPayUseCase_Input_Accesibility": "Текстовая срока", "bankcard_detail": "{} Рег. номера {}", "bankcard_qmf_detail": "{} Отправитель карты родственника, {}", "FaceCheck_Agreement_title": "Подтвердить лицо", "FaceCheck_Success_title": "Подтверждено", "FaceCheck_Result_Retry": "Повторить", "TabBar_NewBadge": "Новое", "common_delete_alert_title": "Подтвердить удаление?", "common_delete": "Удалить", "transfer_to_bank_name_input_placeholder": "Имя получателя", "transfer_to_bank_card_input_placeholder": "Номер банковской карты получателя", "transfer_to_bank_bank_select_placeholder": "Выберите банк", "transfer_to_bank_arrival_time_select_title": "Время", "transfer_to_bank_arrival_time_modal_title": "Выберите время перевода средств", "transfer_to_bank_arrival_time_modal_desc": "После того как вы запросите перевод, средства будут в указанное время внесены на счет получателя.", "transfer_to_bank_history_page_title": "Выберите получателя", "transfer_to_bank_history_page_empty_prompt": "Нет предыдущих получателей", "transfer_to_bank_history_me_section_title": "Я", "transfer_to_bank_history_others_section_title": "Предыдущие получатели", "transfer_to_bank_history_modify_remark_action": "Заметка", "transfer_to_bank_history_set_remark_title": "Добавить заметку", "transfer_to_bank_history_delete_action": "Удалить", "transfer_to_bank_bank_unavailable_alert": "В банке выполняется техническое обслуживание. Переводы средств в настоящее время недоступны.", "transfer_to_bank_money_input_title": "Сумма", "transfer_to_bank_info_receiver_format": "Получатель: {}", "transfer_to_bank_info_charge_fee": "Плата за услугу", "transfer_to_bank_info_charge_fee_rate_format": "(тариф: {:.2f} %)", "transfer_to_bank_info_total_amount": "Итого", "transfer_to_bank_info_transfer_explain": "Примечание", "transfer_to_bank_info_transfer_explain_edit_hint_format": "Видно плательщику и получателю платежа (макс. {} симв.).", "transfer_to_bank_info_add_transfer_explain": "Добавить заметку", "transfer_to_bank_info_detail_title": "Информация", "transfer_to_bank_info_detail_current_state": "Статус", "transfer_to_bank_info_detail_paid_success": "запускает перевод", "transfer_to_bank_info_detail_withdrawn_success": "Вывод средств завершен", "HoneyPay_PrepareCardUI_Title": "Задать карту родственника", "none": "Нет", "mobile_item_key_bank": "Мобильный номер, зарегистрированный в банке", "mobile_item_place_holder_short": "Введите моб. номер", "FillCard_Number_Default_Mobile_Modify_Tips_New": "Был автоматически подставлен последний привязанный моб. номер. При необходимости вы можете изменить этот номер.", "HoneyPay_MoneyInput_Hint_New": "Введите сумму", "AddPayCard_No_Card_Bind_Card_Title": "Привязать без ввода номера карты", "AddPayCard_Manual_Bind_Card_Title": "Привязать с использованием номера карты", "FillCard_Number_Reg_Hint_V3": "Введите номер банковской карты ({})", "FastBindCardSelectBankUIV2_Title": "Привязка без указания номера карты", "FastBindCardSelectBankUIV2_Search_Hint": "Поиск банка(-ов): {}", "qrcode_collection_settings": "Настройки получения", "qrcode_collection_amount": "Сумма", "qrcode_collection_remark": "Инструкции для получения денег", "OfflinePay_Banner_Use_Tips": "Код Платежа", "OfflinePay_Banner_Expand_Tips": "Развернуть", "OfflinePay_Banner_Collapse_Tips": "Скрыть", "OfflinePay_Close_WalletLock_HalfDialog_Title": "Отключить код платежа", "OfflinePay_Close_WalletLock_HalfDialog_Content": "В целях безопасности вы можете настроить защитную блокировку при использовании кода платежа. После этого при использовании кода платежа необходимо будет пройти проверку безопасности.", "FillCardNumberV2_CountryCode_Hint": "Введите код страны/региона", "FillCardNumberV2_CountryCodeView_Hint": "Выберите", "paydesk_main_page_not_use_favor": "Не использовать предложения.", "paysecurity_digital_cert_not_install": "Не установлен", "WCPay_Digital_Cert_Desc_Not_Install": "Цифровой сертификат не установлен", "WCPay_Digital_Cert_Desc_Already_Install": "Цифровой сертификат установлен", "WCPay_Digital_Cert_Manage_Content_Desc": "Установите цифровой сертификат на устройстве, чтобы:", "WCPay_Digital_Cert_Manage_Content_Desc_1": "• Сделать платежи со своего устройства еще безопаснее.", "WCPay_Digital_Cert_Manage_Content_Desc_2": "• Повысить дневной лимит для платежей с помощью баланса.", "WCPay_Digital_Cert_Install_Button_Title": "Установить", "WCPay_Digital_Cert_Delete_Button_Title": "Удалить", "WCPay_Digital_Cert_Install_List_desc": "Устройства, на которые установлен сертификат", "WCPay_Digital_Cert_Delete_Confirm_Content": "Удалить этот цифровой сертификат для текущего ___<BRAND_ID>___ на этом устройстве?", "WCPay_Digital_Delete_Confirm_Btn_Title": "Удалить", "WCPay_Digital_Cert_Install_Action_Title": "Подтверждение личности", "WCPay_Digital_Cert_Install_Action_Desc": "Перед установкой сертификата требуется подтвердить личность.", "WCPay_Digital_Cert_Install_Input_Title_default": "Удостоверение личности", "WCPay_Digital_Cert_Install_Input_Desc_default": "Введите номер удостоверения личности", "WCPay_Digital_Cert_Install_Input_Desc": "Введите номер удостоверения личности ({})", "WCPay_Digital_Cert_Verify_Button_Title": "Подтвердить", "WCPay_Digital_Cert_Install_Sccuess": "Подтверждено", "WCPay_Digital_Cert_Delete_Succ_Toast": "Удален", "LQT_Purchase_Page_Title": "Пополнение", "LQT_Purchase_Card_Info_Title": "Способ перевода", "LQT_MonetInputOutOfRange_Tips": "Недостаточный баланс. Пополните и повторите попытку.", "LQT_Limit_Cashier_Modal_Balance_Desc": "Сумма на балансе", "LQT_Limit_Cashier_Modal_LQT_Desc": "Мин<PERSON>-фонд", "LQT_Limit_Cashier_Modal_Transfer_In_Tips": "Пополнение", "LQT_Limit_Cashier_Modal_Confirm_Button_Title": "Понятно", "LQT_SaveAmountLargeThanBankAvaible_Tips": "Сумма, которую вы ввели, превышает лимит оплаты банка", "LQT_Redeem_Card_Info_Title": "Вывод средств", "LQT_Redeem_Page_Title": "Вывести средства", "LQT_Redeem_Confirm_View_Desc": "Вывести средства", "LQT_Redeem_Balance_Amount": "Баланс мини-фонда: ¥{:.2f}", "LQT_Redeem_Balance_Insufficient": "На балансе мини-фонда недостаточно средств", "LQT_Redeem_Balance_Fetch_All": "Вывести все", "LQT_Loading_Card_Data": "Получение списка банковских карт", "LQT_Loading_LQT_Amount": "Получение баланса мини-фонда", "LQT_Loading_LQ_Amount": "Получение суммы баланса", "LQT_PerRedeem_Invalid_Default_Tips": "Сумма одной транзакции превышает лимит", "LQT_Redeem_Fetch_Amount_Large_Than_Bank_Avaible": "Максимальная сумма транзакции: ¥{:.2f}. Вы можете разбить платеж на несколько транзакций.", "LQT_Redeem_Fetch_Amount_Large_Than_Max_Amount_Tips": "Узнать больше", "HoneyPay_Record_Receive_Title": "Мне с карты родственника", "HoneyPay_Record_Donate_Title": "От меня на карту родственника", "LQTDetail_balance_Accessibility": "Баланс: ¥{:.2f}", "WCPay_LQT_OnClickPurchase_Fail_Network_Error": "Ошибка сети. Не удалось получить список ваших банковских карт. Повторите попытку позже.", "WCPay_LQT_OnClickRedeem_Fail_Network_Error": "Ошибка сети. Не удалось получить список ваших банковских карт. Повторите попытку позже.", "WCPay_LQT_PurchaseFund_Fail_Network_Error": "Не удалось купить. Повторите попытку позже.", "WCPay_LQT_QryPurchaseResult_Fail_Network_Error": "Не удалось проверить результат покупки", "WCPay_LQT_PreRedeemFund_Fail_Network_Error": "Не удалось разместить заказ на получение средств. Повторите попытку позже.", "WCPay_LQT_RedeemFund_Fail_Network_Error": "Не удалось получить средства. Повторите попытку позже.", "WCPay_LQT_MoneyVC_Save_TextFieldTips": "Сумма пополнения", "WCPay_LQT_MoneyVC_Fetch_TextFieldTips": "Сумма", "LQT_Purchase_Keyboard_Confirm_Title": "Внести", "LQT_Redeem_Keyboard_Confirm_Title": "Вывести", "Wallet_Lock_Default_Title": "Защитная блокировка", "Wallet_Lock_FaceLock": "Разблокировка с идентификацией по лицу", "Wallet_Lock_TouchLock": "Разблокировка по отпечатку пальца", "Wallet_Lock_BioLock": "Разблокировка с идентификацией по лицу / отпечатку пальца", "Wallet_Lock_PatternLock": "Разблокировка с помощью жеста разблокировки", "Wallet_Lock_PatternLock_Modify_Verify_Title": "Введите старый жест разблокировки", "Wallet_Lock_PatternLock_Modify": "Сменить жест разблокировки", "Wallet_Lock_PatternLock_Modify_SubTltle": "Задать новый жест разблокировки", "Wallet_Lock_Close_Tips": "При отключении защитной блокировки для доступа к \"Я\" > \"Услуги\" разблокировка не требуется.", "Wallet_Lock_TouchLock_Verify_Title": "Проверьте Touch ID, чтобы продолжить", "Wallet_Lock_FaceLock_Verify_Title": "Проверьте Face ID, чтобы продолжить", "Wallet_Lock_PatternLock_Verify_Title": "Введите жест разблокировки", "Wallet_Lock_Verify_byPwd": "Подтверждение платежного пароля", "Wallet_Lock_Verify_Btn_FaceID": "Подтвердить лицо", "Wallet_Lock_Verify_Btn_TouchID": "Подтвердите отпечаток пальца", "Wallet_Lock_PatternLock_Setup_Title": "Задать жест разблокировки", "Wallet_Lock_PatternLock_Reset_Title": "Забыли жест разблокировки?", "Wallet_Lock_PatternLock_Confirm_Title": "Введите еще раз для подтверждения", "Wallet_Lock_PatternLock_Confirm_Error_Tips": "Жесты не совпадают. Введите снова.", "Wallet_Lock_PatternLock_Verify_Error_Tips": "Неверный жест. Осталось попыток: {}.", "Wallet_Lock_PatternLock_Verify_Limit_Tips": "Слишком много попыток. Попробуйте еще раз через {} мин.", "Wallet_Lock_PatternLock_Modify_AfterVerify_Title": "Задать новый жест разблокировки", "Wallet_Lock_PatternLock_InvalidPattern_Tips": "Необходимо не менее 4 точек. Задайте снова.", "Wallet_Lock_PatternLock_Setup_Succ_Tips": "Жест разблокировки задан", "Wallet_Lock_PatternLock_Modify_Succ_Tips": "Жест разблокировки изменен", "Wallet_Lock_PatternLock_Setup_Cancel_Tips": "Не включать блокировку жестом?", "Wallet_Lock_PatternLock_Setup_Cancel_LeftBtn": "Включить", "Wallet_Lock_PatternLock_Setup_Cancel_RightBtn": "Не сейчас", "Wallet_Lock_Not_Support_TouchID_Tip": "Функция Touch ID недоступна на этом устройстве. Сбросьте защитную блокировку.", "Wallet_Lock_Not_Support_FaceID_Tip": "Функция Face ID недоступна на этом устройстве. Сбросьте защитную блокировку.", "Wallet_Lock_Close_Wallet_Lock_Tip": "Отключить защитную блокировку", "Wallet_Lock_Setup_Pattern_Lock_Tip": "Задать жест разблокировки", "Wallet_Lock_TouchID_NotEnrolled_Tip": "Функция Touch ID не включена. Включите Touch ID в системных параметрах или сбросьте защитную блокировку.", "Wallet_Lock_FaceID_NotEnrolled_Tip": "Функция Face ID не включена. Включите Face ID в системных параметрах или сбросьте защитную блокировку.", "Wallet_Lock_FingerPrint_NotEnrolled_Tip": "В системе нет отпечатков пальцев. Сохраните отпечаток и снова задайте защитную блокировку.", "Wallet_Lock_PatternLock_VerifyBlock_Tip": "Слишком много попыток. Подтвердите вашу личность, нажав \"Сбросить жест разблокировки\", или попробуйте еще раз через 10 минут.", "Wallet_Lock_PatternLock_VerifyBlock_Reset_Tip": "Сбросить жест разблокировки", "Wallet_Lock_New_FingerPrint_Authen_Tips": "Введен новый отпечаток. Введите платежный пароль для подтверждения личности.", "Wallet_Lock_New_TouchID_Authen_Tips": "Данные отпечатков пальцев на этом устройстве изменились. Введите платежный пароль для подтверждения личности.", "Wallet_Lock_New_FaceID_Authen_Tips": "Данные лица на этом устройстве изменились. Введите платежный пароль для подтверждения личности.", "Wallet_Lock_Forget_Pwd": "Забыли пароль", "Wallet_Lock_Retry_Pwd": "Повторить", "LQT_Detail_Operation_More_Product_Title": "Еще товары", "pay_settings_biometric_pay_touchid_title": "Оплата по отпечатку пальца", "pay_settings_biometric_pay_faceid_title": "Оплата с идентификацией по лицу", "pay_settings_biometric_pay_multi_title": "Оплата с идентификацией по лицу / отпечатку пальца", "pay_settings_biometric_pay_touchid_desc": "После включения этой функции вы сможете использовать проверку по отпечатку пальца для ускорения платежей.", "pay_settings_biometric_pay_faceid_desc": "После включения этой функции вы сможете использовать проверку с идентификацией по лицу для ускорения платежей.", "pay_settings_biometric_pay_multi_desc": "Включите оплату с идентификацией по лицу или отпечатку пальца для ускорения платежей.", "pay_settings_biometric_pay_enable_faceid": "Включить оплату с идентификацией по лицу", "pay_settings_biometric_pay_enable_touchid": "Включить оплату по отпечатку пальца", "common_reddot_accessibility": "У вас есть новые сообщения", "common_help": "Справка", "bind_new_card_input_name": "Введите имя, зарегистрированное в банке", "paydesk_title_accessibility_selected": "Выбранное", "RedDot_New": "NEW", "renewal_or_sign_time_item_key": "Продления разрешений/выпусков", "WCPay_Option_Item": "Не обязательно", "VoiceOver_OfflinePay_Unselected": "Не выбрано", "FillCard_Number_Reg_Hint_Self": "Введите номер карты", "common_continue": "продолжить", "WCPay_Risk_Dialog_Title": "Обнаружен потенциальный риск. Подтвердите ваше желание продолжить. Для этого вам необходимо подтвердить вашу личность.", "WCPay_Risk_Not_Support_Dialog_Title": "Обнаружен потенциальный риск. Устраните риски, прежде чем продолжить.", "WCPay_Risk_Failed_Dialog_Title": "Не удалось подтвердить личность. Операция прекращена. Закрыть", "bind_card_agreement_protocal_and_next": "Согласиться и добавить", "wallet": "Кошелек", "awaiting_real_name_verification": "Ожидается подтверждение личности", "five_stars": "*****", "Card_UserAgreement_Read_And_Agreement_Title": "При добавлении необходимо согласие", "FaceCheck_Common_Error": "Система занята. Повторите попытку.", "FaceCheck_MP_Request_Use": " Запрос доступа", "FaceCheck_MP_Confirm_Tip": "___<BRAND>___ использует распознавание лица для удостоверения личности. Оно должно настраиваться только владельцем учетной записи.", "FaceCheck_MP_Front_Feedback": "Справочный центр", "FaceCheck_Recoging": "Распознавание...", "waiting_for_real_name_authentication": "Ожидается подтверждение личности", "collect_sub_title": "Сумма", "collect_main_add_desc_title_simple_change": "Изменить", "collect_main_add_desc_title": "Добавить примечание", "remittance_amount_lowest_limit": "Минимальная сумма перевода: 0,01 ¥.", "collect_main_fixed": "Задайте сумму", "collect_main_first_enter_tips_title": "Получение денег", "collect_main_first_enter_tips_new": "Полученные деньги будут зачислены на ваш счет ___<BRAND_Balance>___ («Я» > «Службы» > «Кошелек»), с которого их можно потратить или вывести.", "collect_main_close_ring_tone": "Откл. звуковые оповещения о получениях", "collect_main_close_ring_tone_tips": "Отключено", "collect_main_open_ring_tone": "Вкл. звуковые оповещения о получениях", "collect_main_open_ring_tone_tips": "Включено. Проверьте, что звук мультимедиа включен.", "collect_main_qrcode_usage_other": "Другое", "collect_main_qrcode_usage_other_placeholder": "Добавьте данные (до 16 символов)", "collect_main_payer_desc_default_placeholder": "Добавьте сообщение для получателя.", "collect_qrcode_save_failed": "Не удалось сохранить", "collect_material_guide_save_text_toast": "Сохранено в альбом", "collect_mch_module_title": "QR-код для бизнеса", "collect_personal_module_title": "QR-код физического лица", "collect_setting_title": "Настройки получения", "collect_main_fixed_cancel": "Очистить сумму", "collect_main_more_function": "Другие параметры", "collect_main_save_qrcode": "Сохранить код платежа", "collect_main_receive_title": "Всего", "collect_main_paying": "Оплата...", "collect_main_pay_suc": "Платеж выполнен", "collect_main_pay_cancel": "Отменить платеж", "collect_main_loading_title": "Загрузка QR-кода...", "collect_main_ring_not_support": "Не поддерживается в этой системе", "WCPay_Transfer_To_Format": "Перевести средства {}", "WCPay_Transfer_Weixin_Alias_Prefix": "___<BRAND_ID>___:", "WCPay_Transfer_InputAmount_Tips": "Сначала введите сумму", "WCPay_Transfer_Cashier_Desc_Format": "Перевести средства {}", "WCPay_Transfer_Succ_Tips": "Ожидание получения пользователем {}", "WCPay_Service": "Службы", "recognize_and_pay": "Распознать и оплатить", "bizf2f_input_ui_page_to_person": "Заплатить физическому лицу", "bizf2f_input_ui_page_add_remark": "Добавить примечание", "bizf2f_input_ui_page_amount_title": "Сумма оплаты", "WCPay_Verify_Password_Get_SMS_Code": "Получить код подтверждения", "WCPay_VerifyCode_Header_Description": "Для выполнения этой транзакции требуется SMS-верификация.", "bizf2f_input_ui_page_pay_action": "Оплата", "bizf2f_input_ui_page_change_remark": "Изменить", "bizf2f_input_ui_page_pay_title": "Оплата", "bizf2f_favor_title": "Предложения", "bizf2f_favor_total_fee": "Общая сумма", "bizf2f_favor_calculating": "Расчет...", "bizf2f_favor_select_favor": "Выберите скидку", "UN_BIND_CARD_TITLE": "Отменить привязку банковской карты", "WCPay_system_version_limitation_tip": "Чтобы получить больше функций, используйте HarmonyOS 4.2 или более раннюю версию, либо другие устройства.", "reconfirm_payment_amount_title": "Подтвердить сумму платежа еще раз", "reconfirm_payment_amount_des": "Подтвердите сумму, чтобы обеспечить безопасность ваших активов и избежать ошибок.", "reconfirm_amount_page_tip": "В связи с нормативными требованиями платежи, превышающие ограничения для статического QR-кода, должны выполняться путем сканирования динамического QR-кода ниже. Нажмите кнопку для подтверждения и завершения платежа.", "Hongbao_SendUI_NavigationBar_Title": "Отправить Красный пакет", "Hongbao_SendUI_Send_Button_Titlle": "Подготовить красный пакет", "Hongbao_SendUI_Count_Title": "Количество", "Hongbao_SendUI_Amount_Title_Group": "Всего", "Hongbao_SendUI_Amount_Title_Single": "Сумма", "Hongbao_SendUI_RandomLuckyMode_Title": "Случайная сумма", "Hongbao_SendUI_Count_Tips": "Ввести количество", "Hongbao_SendUI_Amount_Tips": "0,00 ¥", "Hongbao_SendUI_Default_Wishing": "С наилучшими пожеланиями", "Hongbao_Per_Hongbao_Max_Amount_Format": "До {} ¥ для каждого Красного пакета", "HongBao_SendTips": "Отправленные пакеты", "HongBao_OpenTips": "Открыть", "HongBao_AmoutTips": "Китайский юань", "HongBao_MainTitle": "Красный пакет", "HongBao_Cashier_Desc": "___<BRAND_hongbao>___", "Hongbao_SendUI_RandomMode_Title": "Случайная сумма", "Hongbao_SendUI_NormalMode_Title": "Идентичная сумма", "Hongbao_SendUI_ExclusiveMode_Title": "Эксклюзивно", "Hongbao_SendUI_Select_Count_Suffix": "", "Hongbao_SendUI_Group_Member_Count_Format": "Количество участников в этой группе: {}", "Hongbao_SendUI_Amount_Title_Group_Normal": "Сумма каждому", "Hongbao_SendUI_Perperson_Maxvalue_Error_Tips": "До {} ¥ для каждого Красного пакета", "Hongbao_SendUI_Perperson_Minvalue_Error_Tips": "Как минимум {:.2f} для каждого Красного пакета", "Hongbao_SendUI_Count_Larger_Than_Group_Mem_Count_Error_Tips": "Количество Красных пакетов не может превышать количество участников группы.", "Hongbao_SendUI_Total_Num_Error_Tips": "Максимальное количество: {}.", "Hongbao_SendUI_Select_Count_Data_Invalid_Error_Tips": "Количество не указано", "Hongbao_SendUI_Amount_Data_Invalid_Error_Tips": "Значение «Всего» не указано", "Hongbao_SendUI_Select_Count_Zero_Error_Tips": "Выберите количество.", "Hongbao_SendUI_Amount_Larger_Than_Max_Amount_Error_Tips": "Общая сумма не может превышать {} китайских юаней.", "Hongbao_ReceiveModal_Detail_Link": "Просмотреть сведения", "Hongbao_DetailUI_Load_More_Text": "Нажмите, чтобы загрузить еще", "TransferPhone_Entry_Title": "Выберите способ перевода средств", "TransferPhone_To_Bank_Title": "Перевод на банковскую карту", "TransferPhone_To_Bank_Desc": "Введите банковскую карту получателя платежа, чтобы выполнить перевод средств на его банковский счет.", "TransferPhone_To_Phone_Title": "Перевод средств на моб. номер", "TransferPhone_To_Phone_Desc": "Введите мобильный номер получателя платежа, чтобы выполнить перевод средств на ___<BRAND_Balance>___.", "TransferPhone_To_PaySetting": "Параметры перевода средств на моб.номер", "WCPay_ThirdParty_Tips_Title": "Отказ от обязательств", "WCPay_Service_Manage": "Управление службами", "identify_and_pay": "Распознать и оплатить", "Hongbao_SendUI_Exclusive_Mode_Send_To_Who": "Отправить", "Hongbao_SendUI_Exclusive_Mode_Send_To_Who_Error_Tips": "Получатель не указан", "Hongbao_SendUI_Amount_Data_Invalid_Error_Tips_2": "Сумма не указана", "MerchantPay_Input_Remark_Hint_Format": "Видно получателю. Макс. количество символов: {}.", "MerchantPay_Input_Remark_Title": "Добавить примечание", "MerchantPay_Transfer_To_Format": "Платеж для {}", "Greeting_Hongbao_Random_Change_Amount": "Изменить количество", "Greeting_Hongbao_Who_Receive_Hongbao_Format": "Открыто пользователем {}", "set_amount": "Задайте сумму"}