{"transfer": "转账", "balance": "零钱", "transfer_explain": "添加转账说明", "transfer_account": "转账金额", "transfer_modify_explain": "修改", "transfer_ui_title": "转账给朋友", "default_delay_transfer_confirm_desc": "好友确认收款延迟{}小时入账", "transfer_amount_input_invalid_hint": "输入金额错误", "wxp_common_confirm": "确定", "wxp_system_error": "系统繁忙，请稍后再试。", "wxp_wcpay_system_error": "系统繁忙，请稍后再试。", "wxp_network_error": "系统繁忙，请稍后再试。", "common_close": "关闭", "wxp_common_cancel": "取消", "wxp_common_remind": "提醒", "wxp_payment_network_error": "交易请求已提交，请留意___<BRAND_Pay>___公众号下发的消息通知。支付状态未明确前，请勿重复支付", "transfer_second_title": "转账提醒", "transfer_second_left_button": "取消", "transfer_second_right_button": "继续转账", "yes": "是", "no": "否", "none": "无", "common_reddot_accessibility": "有新消息", "paydesk_main_page_title": "收银台主页面", "paydesk_float_page_title": "请输入支付密码", "paydesk_coupon_page_title": "收银台优惠页面", "paydesk_sub_page_title": "收银台子页面", "paydesk_payment_page_title": "收银台卡列表", "paydesk_title_accessibility_selected": "已选择", "WCPay_TouchID_Auth_Tip": "请验证已有的指纹，用于支付", "WCPay_TouchID_Confirm_Alert_Title": "支付", "WCPay_TouchID_Confirm_Alert_Cancel": "取消", "WCPay_TouchID_Confirm_Alert_OK": "确认", "WCPay_TouchID_Confirm_Alert_Content": "指纹验证通过，是否确认支付？", "paydesk_main_page_more_favor": "更多优惠", "confrim_pay_and_open_deduct_word": "支付并开通", "confrim_pay_word": "支付", "binded_card_list_page_title": "请选择验短手机号", "lqt_reset_mobile_desc": "选择一张银行卡，用该卡的银行预留手机号作为零钱通验证手机号，验证短信通过后生效", "wechat_mobile_phone_word": "___<BRAND>___绑定手机号", "update_word": "更新", "bind_new_card_to_reset_mobile_desc": "通过绑定新卡绑定手机号", "pay_card_detail_contact_user_info": "联系客服解决：", "pay_card_detail_tel_number": "95017", "common_i_know_word": "我知道了", "common_next_word": "下一步", "common_done_word": "完成", "common_delete_alert_title": "确认删除?", "common_delete": "删除", "common_confirm_word": "确认", "common_back": "返回", "common_continue": "继续", "common_tip_word": "提醒", "common_cancel_word": "取消", "common_drop_out_word": "退出", "common_question_word": "常见问题", "common_check_box_uncheck": "勾选框,未勾选", "common_check_box_check": "勾选框,已勾选", "pay_power_by_tenpay_word": "本服务由财付通提供", "check_pay_pwd_page_title": "身份验证", "give_up_on_new_card": "是否放弃绑定银行卡？", "check_pay_pwd_page_desc": "请验证支付密码确认本人操作", "check_sms_page_title": "验证手机号", "check_sms_page_desc": "绑定银行卡需要短信确认，验证码已发送至手机：{}，请按提示操作。", "fill_in_sms_key": "验证码", "fill_in_sms_word": "填写验证码", "resend_sms": "重新发送", "has_send_sms": "已发送", "has_send_sms_with_count": "已发送({})", "resend_sms_with_count": "重新发送({})", "get_sms_word": "获取验证码", "get_sms_with_count_down_word": "获取验证码\n（{}）", "can_not_get_sms_with_question_mard_word": "收不到验证码？", "can_not_get_sms_word": "收不到验证码", "fill_card_info_page_title": "填写卡信息", "card_type_section_header": "请选择银行卡类型", "bank_select_item_key": "银行", "card_select_item_key": "卡类型", "card_info_section_header": "请填写银行卡信息", "valid_date_item_key": "有效期", "cvv_item_key": "安全码", "cvv_item_place_holder": "卡背面末三位", "card_holder_section_header": "请填写银行预留信息，后续只能添加该持卡人的银行卡", "name_item_key": "姓名", "name_item_place_holder": "请输入持卡人姓名", "cre_type_item_key": "证件类型", "cre_type_item_place_holder": "请选择证件类型", "cre_id_item_key": "证件号", "cre_id_item_place_holder": "请输入证件号", "user_info_section_header": "请填写个人信息", "profession_item_key": "职业", "area_item_key": "地区", "nationality_item_key": "国籍", "nationality_place_holder": "请输入国籍", "mobile_item_key": "手机号", "mobile_item_key_bank": "银行预留手机号", "mobile_item_place_holder": "请输入银行预留手机号", "mobile_item_place_holder_short": "输入手机号", "agree_user_protocal": "同意《用户协议》", "agree": "同意", "disagree": "不同意", "user_protocal": "《用户协议》", "card_user_agreement": "《用户服务协议》", "card_bank_agreement": "《银行协议》", "bind_card_agreement_protocal_and_next": "同意协议并添加", "fill_card_num_page_title": "添加银行卡", "fill_card_num_page_desc": "请绑定持卡人本人的银行卡", "fill_card_num_page_sns_input_hint": "仅支持储蓄卡", "card_holder_item_key": "持卡人", "new_user_card_num_input_safety_desc": "请绑定___<BRAND>___账户本人的卡，", "understand_safety": "了解安全保障", "card_num_item_key": "卡号", "fill_card_number_more_favor": "使用指定银行卡可享受优惠", "fill_card_number_assign_pay": "请使用持卡人为 {} 的银行卡支付", "card_holder_dialog_title": "持卡人说明", "card_holder_dialog_content": "1. 为了资金安全，一个___<BRAND_ID>___，只能添加同一身份下的银行卡。\n\n2. 如需添加其他持卡人的银行卡，需要更换实名。\n\n3. 更换实名后，原持卡人信息将被清空，后续仅能添加新持卡人的银行卡。", "change_realname_word": "更换实名", "assign_pay_dialog_content": "当前___<BRAND>___账号绑定的身份信息与您输入的信息不一致，请使用绑定了您本人银行卡的___<BRAND>___账户重新验证", "safety_dialog_title": "安全保障", "safety_dialog_content": "安全保障：帐号保护、实时监控、紧急冻结等多重金融安全保障。\n\n双重验证：每次支付均验证支付密码，大额支付还需短信验证。\n\n隐私保护：高强度数据加密，保护用户隐私信息。\n\n支付理赔：支付安全由中国人民财产保险股份有限公司承保。", "reset_cvv_title": "重置安全码", "reset_cvv_and_valid_date_tip": "你正在更新银行卡绑定信息，同时完成支付。如有疑问请致电银行客服：", "cvv_dialog_title": "安全码说明", "cvv_dialog_content": "安全码是打印在信用卡背面签名区的一组数字，一般是后3位或4位数字。", "reset_mobile_phone_page_title": "修改预留手机号", "reset_phone_tip": "验证通过后可完成支付，如需向银行确认预留手机号信息，请拨打", "bank_card_item_key": "银行卡", "new_mobile_item_key": "新手机号", "new_mobile_item_place_holder": "请输入银行预留手机号", "bank_card_info": "说明", "mobile_dialog_title": "手机号说明", "mobile_dialog_content": "银行预留手机号是你在办理该银行卡时所填写的手机号。没有预留、手机号忘记或者已停用，请联系银行客服处理。", "set_pay_pwd_page_title": "设置支付密码", "set_pay_pwd_confirm_page_title": "请再次填写以确认", "set_pay_pwd_page_desc": "请设置___<BRAND_Pay>___密码，用于支付验证。", "change_to_pwd": "使用密码", "change_to_touch_id": "使用指纹", "change_to_face_id": "使用面容", "select_payment": "请选择付款方式", "transfer_time": "转账时间：{}", "to_be_confirm_receive": "待确认收款", "receive_done": "已收款", "confirm_to_receive": "确认收钱", "receive_time": "收钱时间：{}", "refund_done": "已退款", "refund_doing_tip": "已发起退款，1-3个工作日退回到储蓄卡", "refund_time": "退还时间：{}", "refund_done_and_expired": "已退还（过期）", "cancel_time": "取消时间：{}", "refund_transfer_or_not": "是否退还{}的转账", "refund_word": "退还", "resend_word": "重发", "resend_success_tip": "重发消息成功", "resend_message_or_not": "是否重发此条消息", "forget_pay_pwd_title": "忘记支付密码", "rebind_bank_card_section_header": "重新绑定银行卡找回", "bind_new_card_section_header": "绑定新卡找回", "bind_new_card": "添加银行卡", "bind_new_card_section_footer": "为安全起见，完成后已绑卡将自动解绑", "bind_new_card_to_pay_tip": "请输入支付密码，以验证身份。", "bind_new_card_input_name": "输入银行预留姓名", "reset_pwd_fill_rebind_card_info_page_title": "填写银行卡信息", "fill_card_num_of_card_holder_section_header": "请输入持卡人本人的银行卡号", "fill_complete_name": "请输入完整姓名", "id_card_name": "身份证", "set_pwd_success": "设置成功", "pwd_repeat_error_tip": "两次密码输入不一致", "give_up_this_order_or_not": "是否放弃本次支付？", "fill_card_and_user_info_title": "填写银行卡及身份信息", "confirm_pay": "立即支付", "order_address_section_header": "账单寄送地址", "first_name_item_key": "名", "first_name_item_place_holder": "请输入名字", "last_name_item_key": "姓", "last_name_item_place_holder": "请输入姓", "address_item_key": "地址", "address_item_place_holder": "请输入地址", "phone_number_item_key": "电话", "phone_number_item_place_holder": "请输入电话", "zip_item_key": "邮编", "zip_item_place_holder": "请输入邮编", "email_item_key": "电子邮件", "email_item_place_holder": "请输入电子邮件", "reset_ban_mobile_fill_card_info_credit_tip_header": "请填写银行卡信息用于校验", "reset_ban_mobile_fill_card_num_tip_header": "添加一张新银行卡，并用该银行卡的预留手机号来作为零钱验证手机号", "reset_lqt_mobile_fill_card_num_tip_header": "添加一张新银行卡，并用该银行卡的预留手机号来作为零钱通验证手机号", "verify_cre_tip": "请填写{}的{}号后四位以验证身份", "confirm_mobile_no": "确认手机号", "send_verify_code_tips_format": "我们将发送验证短信到这个号码：\n{}", "send_verify_code_btn_wording": "发送", "send_verify_code_switch_btn_wording": "切换其他验证方式", "WCPay_CountryCode_Title": "国家/地区", "WCPay_NeedChangeCard_Error_Btn": "更换支付方式", "check_sms_page_favor": "将要支付{}{:.2f}（共优惠{}{:.2f}）", "foreign_mobile_header": "填写新手机号", "agreement_alert": "请先阅读并同意《用户协议》。", "ok": "好", "succ_page_open_touch_id_dialog_content": "开启指纹支付，支付时可通过验证指纹快速完成付款。", "succ_page_open_face_id_dialog_content": "开启面容支付，支付时可通过验证面容快速完成付款。", "succ_page_open_touch_id_left_btn_title": "稍后再说", "succ_page_open_touch_id_right_btn_title": "开启指纹支付", "succ_page_open_face_id_right_btn_title": "开启面容支付", "fill_card_num_page_favor_dialog_title": "本次可享受银行优惠", "fill_card_info_page_favor_desc": "使用此类型银行卡可再优惠{}{:.2f}", "fill_phone_num_format_error": "手机号格式不正确", "fill_id_format_error": "证件号格式不正确", "lottery_network_error": "本次未中奖", "fill_card_info_card_holder_assign_pay_header": "该订单需要指定身份支付", "fill_card_num_format_error": "卡号格式不正确", "coupon_component_need_bank_pay_tips": "指定支付方式优惠", "finger_print_err_tips": "再试一次", "paying_alert_tips": "订单正在支付中，请等待支付结果后再确定是否发起另一笔支付", "succ_page_open_biometric_dialog_content": "你可开启面容或指纹支付，开启后，支付时可快速完成付款", "succ_page_open_biometric_faceid_btn_title": "面容支付", "succ_page_open_biometric_touchid_btn_title": "指纹支付", "succ_page_open_biometric_cancel_btn_title": "暂不开启", "wechat_user_agreement": "___<BRAND_Pay>___用户服务协议", "wechat_bank_agreement": "银行协议", "ask_verify_fingerprint": "请验证指纹", "verify_fingerprint_fail": "指纹验证失败", "lqt": "零钱通", "payment_method": "支付方式", "receive_remark_title": "收款备注", "receiver_remark_title": "收款方备注", "alertChargeFee": "手续费", "payee_remark_title": "付款方备注", "float_paydesk_modal_no_select_favor": "未使用优惠", "paydesk_main_page_not_use_favor": "不使用优惠", "hongbao_refund_way_title": "红包退款方式", "hongbao_refund_way_header_title": "发出的红包若在24小时内未被领取，资金将自动退回到如下路径", "card_number_input_tips_title": "无需网银/免手续费", "coupon_change_should_change_payment": "支付金额发生变化，请重新选择", "pure_bind_card_succ_tips": "绑卡成功", "common_select": "选择", "offline_pay_title": "收付款", "offline_pay_only_pay_title": "付款", "wallet": "钱包", "awaiting_real_name_verification": "待实名认证", "five_stars": "*****", "offline_pay_modify_limit": "修改额度", "loading_title": "正在加载…", "install_cert_error": "安装证书失败", "common_more": "更多", "pay_success": "支付成功", "offline_pay_select_card_invalid": "所选支付方式暂不可用，请更换其他支付方式", "offline_pay_to_merchant": "向商家付款", "offline_click_view_code": "点击可查看付款码数字", "offline_view_code_warning": "付款码数字仅用于支付时向收银员展示，请勿泄露以防诈骗", "offline_prefer_payment": "优先使用此支付方式付款", "offline_choose_payment": "选择优先支付方式", "offline_choose_payment_fail": "优先使用所选支付方式付款，如付款失败将尝试使用其他支付方式完成付款", "offline_show_code_warning": "付款码数字仅用于支付时向收银员展示，请勿发送给他人，以防诈骗。", "OfflinePay_Setting_CloseOfflinePay_AlertMessage": "暂停使用付款功能？", "OfflinePay_Setting_CloseOfflinePay_AlertMessage_ShowFeaturePassword": "开启安全锁后，进入刷卡会进行验证，以增强安全。暂停使用吗？", "OfflinePay_Setting_CloseOfflinePay_OpenWalletLock": "设置安全锁", "OfflinePay_Setting_CloseOfflinePay_BtnTitle": "暂停使用", "OfflinePay_Setting_CloseOfflinePay_Cancel": "继续使用", "OfflinePay_CreateOfflinePay_Tips": "你未开启付款功能，开启后向商家出示付款码即可完成快速支付。", "OfflinePay_CreateOfflinePay_Euro_Tips": "你未开启付款功能，开启后向商家出示付款码即可完成快速支付，仅支持人民币支付。", "OfflinePay_CreateOfflinePay_ConfirmBtn_Title": "立即开启", "OfflinePay_ReCreateOfflinePay_Tips": "需要重新开启付款功能，开启后向商家出示付款码即可完成快速支付。", "OfflinePay_ReCreateOfflinePay_Euro_Tips": "需要重新开启付款功能，开启后向商家出示付款码即可完成快速支付，仅支持人民币支付。", "OfflinePay_ReCreateOfflinePay_ConfirmBtn_Title": "开启", "OfflinePay_ChangePayment_UnConnectedNetwork_Tips": "当前网络不可用，无法选择支付方式", "OfflinePay_EnablePage_UnConnectedNetwork_Tips": "当前网络不可用，请稍后再试", "OfflinePay_Banner_Use_Tips": "付款码", "OfflinePay_Banner_Expand_Tips": "展开", "OfflinePay_Banner_Collapse_Tips": "收起", "VoiceOver_OfflinePay_barCode": "付款条码，可向收银员出示，双击可全屏展示付款码数字", "VoiceOver_OfflinePay_barCode_short": "付款条码", "VoiceOver_OfflinePay_Qrcode": "付款二维码", "VoiceOver_OfflinePay_barcode_clickHint": "双击可返回", "VoiceOver_OfflinePay_Qrcode_clickHint": "双击可全屏展示", "VoiceOver_OfflinePay_Unselected": "未选中", "OfflinePay_Close_WalletLock_HalfDialog_Title": "暂停使用付款码", "OfflinePay_Close_WalletLock_HalfDialog_Content": "如因担心支付安全而暂停使用付款码扫码支付，可选择设置安全锁。设置后，使用时会进行安全验证。", "Wallet_Lock_Default_Title": "安全锁", "Wallet_Lock_FaceLock": "面容解锁", "Wallet_Lock_TouchLock": "指纹解锁", "Wallet_Lock_BioLock": "指纹/面容解锁", "Wallet_Lock_PatternLock": "手势密码解锁", "Wallet_Lock_PatternLock_Modify_Verify_Title": "请输入原手势密码", "Wallet_Lock_PatternLock_Modify": "修改手势密码", "Wallet_Lock_PatternLock_Modify_SubTltle": "请设置新的手势密码", "Wallet_Lock_Close_Tips": "关闭安全锁后，进入“我-服务”时将不再验证身份", "Wallet_Lock_TouchLock_Verify_Title": "验证指纹以继续", "Wallet_Lock_FaceLock_Verify_Title": "验证面容以继续", "Wallet_Lock_PatternLock_Verify_Title": "请输入手势密码", "Wallet_Lock_Verify_byPwd": "支付密码验证", "Wallet_Lock_Verify_Btn_FaceID": "验证面容", "Wallet_Lock_Verify_Btn_TouchID": "验证指纹", "Wallet_Lock_PatternLock_Setup_Title": "请设置手势密码", "Wallet_Lock_PatternLock_Reset_Title": "忘记了手势密码？", "Wallet_Lock_PatternLock_Confirm_Title": "再次输入以确认", "Wallet_Lock_PatternLock_Confirm_Error_Tips": "密码不一致，请重新设置。", "Wallet_Lock_PatternLock_Verify_Error_Tips": "密码错误，你还可以输入{}次。", "Wallet_Lock_PatternLock_Verify_Limit_Tips": "多次输入错误，请{}分钟后再试。", "Wallet_Lock_PatternLock_Modify_AfterVerify_Title": "请设置新的手势密码", "Wallet_Lock_PatternLock_InvalidPattern_Tips": "至少连接4个点，请重新绘制。", "Wallet_Lock_PatternLock_Setup_Succ_Tips": "已设置手势密码", "Wallet_Lock_PatternLock_Modify_Succ_Tips": "已修改手势密码", "Wallet_Lock_PatternLock_Setup_Cancel_Tips": "暂不开启手势密码？", "Wallet_Lock_PatternLock_Setup_Cancel_LeftBtn": "继续开启", "Wallet_Lock_PatternLock_Setup_Cancel_RightBtn": "暂不开启", "Wallet_Lock_Not_Support_TouchID_Tip": "指纹锁在该设备不可使用，请重新设置安全锁", "Wallet_Lock_Not_Support_FaceID_Tip": "面容锁在该设备不可使用，请重新设置安全锁", "Wallet_Lock_Close_Wallet_Lock_Tip": "关闭安全锁", "Wallet_Lock_Setup_Pattern_Lock_Tip": "设置手势密码", "Wallet_Lock_TouchID_NotEnrolled_Tip": "尚未开启触控 ID功能，请到系统设置里开启或重新设置安全锁", "Wallet_Lock_FaceID_NotEnrolled_Tip": "尚未开启面容 ID 功能，请到系统设置里开启或重新设置安全锁", "Wallet_Lock_FingerPrint_NotEnrolled_Tip": "尚未在系统中录入指纹，请在系统设置里录入或重新设置安全锁", "Wallet_Lock_PatternLock_VerifyBlock_Tip": "手势密码错误次数过多，你可以通过重置手势密码来验证身份或10分钟后再试。", "Wallet_Lock_PatternLock_VerifyBlock_Reset_Tip": "重置手势密码", "Wallet_Lock_New_FingerPrint_Authen_Tips": "当前输入的是新指纹，请输入支付密码，以验证身份。", "Wallet_Lock_New_TouchID_Authen_Tips": "设备的指纹信息发生了变化，请输入支付密码，以验证身份。", "Wallet_Lock_New_FaceID_Authen_Tips": "设备的面容信息发生了变化，请输入支付密码，以验证身份。", "Wallet_Lock_Forget_Pwd": "忘记密码", "Wallet_Lock_Retry_Pwd": "重试", "scan_card_num_title": "扫描银行卡", "error_detail_title": "查看解决方法", "receipt": "二维码收款", "reward": "赞赏码", "group_aa": "群收款", "face_hongbao": "面对面红包", "transfer_bank": "转账到银行卡", "cannot_receive_sms_code_title": "收不到验证码", "cannot_receive_sms_code_content": "验证码发送至你的银行预留手机号，请确认当前是否使用预留手机号，并检查是否被安全软件拦截。若号码已停用，请联系银行客服咨询。更多帮助，请拨打客服电话95017。", "select_payment_card": "请选择支付方式", "WCPay_FaceID_Auth_Tip": "请验证已有的面容，用于支付", "fill_card_num_page_realname_desc": "完成实名认证需要添加本人银行卡", "fill_card_info_page_realname_cre_not_support": "当前银行卡不支持{}绑定", "renewal_time_item_key": "换证次数", "renewal_or_sign_time_item_key": "签发/换证次数", "birth_date_item_key": "出生日期", "wxp_common_i_know": "我知道了", "can_not_bind_more_card": "绑定的银行卡达到上限", "verify_id_ui_true_name_tips": "{}（请输入完整姓名）", "WCPay_BankCardBindTo1B_NotReturnDetail": "将扣除0.01元以验证帐户有效", "WCPay_BankCardBindTo1B_Detail": "将扣除0.01元以验证账户，验证后退还", "WCPay_BankCardBindTo0_0_5D_Detail_back": "将扣除0.05美元以验证账户，验证后退还", "WCPay_BankCardBindTo0_0_5D_Detail": "将扣除0.05美元以验证账户", "WCPay_GiveUpReset_Title": "是否放弃重置支付密码？", "reset_mobile_card_desc_format": "{}{}({})预留手机号", "reset_mobile_card_desc_with_update_format": "{}{}({})预留手机号，", "reset_mobile_bank_card_number": "银行卡", "reset_mobile_new_mobile_number": "新手机号", "reset_mobile_new_mobile_info_btn": "详细信息", "new_user_card_num_input_safety_desc_v2": "请绑定___<BRAND>___账户本人的卡", "user_card_type_select_placeholder_v2": "选择银行和卡类型", "BindCard_Gender_Male": "男", "BindCard_Gender_Female": "女", "FillCard_Info_ErrorTips_Format": "{} 等{}项错误", "FillCard_Number_Unreg_Hint": "___<BRAND_ID>___本人银行卡号", "FillCard_Number_Reg_Hint": "持卡人本人银行卡号", "Card_UserAgreement_Title": "添加银行卡需同意以下协议", "Card_UserAgreement_Read_And_Agreement_Title": "添加时需同意", "Fetch_Balance": "零钱提现", "Fetch_Balance_Open_Order": "发起提现申请", "Fetch_Balance_Bank_Proccessing": "银行处理中", "Fetch_Balance_Success": "到账成功", "Fetch_Balance_To_Bank": "到账银行卡", "Fetch_Balance_Amount": "提现金额", "Fetch_Balance_Amount_Tips": "当前零钱余额{}元，", "Fetch_Balance_Amount_Exceed": "输入金额超过零钱余额", "Fetch_Balance_Fetch_All": "全部提现", "Continue_Pay": "继续支付", "Give_Up": "放弃", "fast_bind_card_support_bank_title_text": "当前支持以下银行", "FillCard_Number_Default_Mobile_Modify_Tips": "如果手机号有误，可轻触进行修改。", "FillCard_Number_Default_Mobile_Modify_Tips_New": "已为你自动填入上次绑卡的手机号，如有误可修改。", "WeChatPay_Name": "___<BRAND_Pay>___", "Choose_Payment": "选择扣款方式", "Weekly": "每周", "Monday": "周一", "Tuesday": "周二", "Wednesday": "周三", "Thursday": "周四", "Friday": "周五", "Saturday": "周六", "Sunday": "周日", "Day": "{}日", "Monthly": "每月", "Each_WeekDay_Deposit": "每{}存入", "Each_Day_In_Month_Deposit": "每月{}日存入", "LQT_Fixed_Deposit_Check_PWD_To_Add_Plan": "请输入支付密码，以开始定时存", "LQT_Fixed_Deposit_Check_PWD_To_Pause_Plan": "请输入支付密码，以暂停计划", "LQT_Fixed_Deposit_Check_PWD_To_Start_Plan": "请输入支付密码，以启用计划", "LQT_Fixed_Deposit_Check_PWD_To_Delete_Plan": "请输入支付密码，以删除计划", "LQT_Fixed_Deposit": "定时存", "LQT_Fixed_Deposit_Did_Modify": "已修改", "LQT_Fixed_Deposit_Did_Pause": "已暂停", "LQT_Fixed_Deposit_Did_Open": "已启用", "LQT_Fixed_Deposit_Did_Delete": "已删除", "LQT_Fixed_Deposit_No_Plan": "暂无存入计划", "LQT_Fixed_Deposit_Plan": "存入计划", "LQT_Fixed_Deposit_Plan_Set_Bank_Card_Tip": "设置定时存计划需要选择扣款银行卡。", "LQT_Fixed_Deposit_Plan_Set_Time_Tip": "设置定时存计划需要选择存入时间。", "LQT_Fixed_Deposit_Plan_Should_Input": "设置定时存计划需要填写金额。", "LQT_Purchase_Page_Title": "转入", "LQT_Purchase_Card_Info_Title": "转入方式", "LQT_MonetInputOutOfRange_Tips": "余额不足，请充值零钱后再试", "LQT_Limit_Cashier_Modal_Balance_Desc": "零钱余额", "LQT_Limit_Cashier_Modal_LQT_Desc": "零钱通", "LQT_Limit_Cashier_Modal_Transfer_In_Tips": "转入", "LQT_Limit_Cashier_Modal_Confirm_Button_Title": "我知道了", "LQT_SaveAmountLargeThanBankAvaible_Tips": "输入金额超过该银行支付限额", "LQT_Redeem_Card_Info_Title": "到账账户", "LQT_Redeem_Page_Title": "转出", "LQT_Redeem_Confirm_View_Desc": "转出", "LQT_Redeem_Balance_Amount": "零钱通余额¥{:.2f}", "LQT_Redeem_Balance_Insufficient": "零钱通余额不足", "LQT_Redeem_Balance_Fetch_All": "全部转出", "LQT_Loading_Card_Data": "正在获取银行卡列表", "LQT_Loading_LQT_Amount": "正在获取零钱通余额", "LQT_Loading_LQ_Amount": "正在获取零钱余额", "LQT_PerRedeem_Invalid_Default_Tips": "单笔最高转出金额超上限", "LQT_Redeem_Fetch_Amount_Large_Than_Bank_Avaible": "单笔限额¥{:.2f}，可分多笔转出", "LQT_Redeem_Fetch_Amount_Large_Than_Max_Amount_Tips": "了解详情", "LQT_Purchase_Keyboard_Confirm_Title": "转入", "LQT_Redeem_Keyboard_Confirm_Title": "转出", "LQT_Detail_Operation_More_Product_Title": "更多产品", "ModifyPwdUseCase_ModifyPwd_Title": "修改密码", "ModifyPwdUseCase_ModifyPwd_Desc": "请输入支付密码，以验证身份。", "ModifyPwdUseCase_ModifyPwd_GiveUp_Title": "是否放弃修改支付密码？", "ModifyPwdUseCase_ModifyPwd_GiveUp_Yes": "放弃修改", "ModifyPwdUseCase_ModifyPwd_GiveUp_No": "继续修改", "ModifyPwdUseCase_ModifyPwd_Success": "修改密码成功", "ExposureInfo_Waiting_Wording": "稍等一下...", "HHC_Name": "花花存", "HHC_Choose_Payment": "选择扣款银行卡", "HHC_Plan_Set_Bank_Card_Tip": "设置花花存计划需要选择扣款银行卡。", "HHC_Plan_Check_Amount": "输入金额有误，请检查后重新输入。", "HHC_Check_PWD_To_Add_Plan": "请输入支付密码，以开始花花存", "HHC_Check_PWD_To_Pause_Plan": "请输入支付密码，以暂停花花存", "HHC_Check_PWD_To_Start_Plan": "请输入支付密码，以启用花花存", "HHC_Check_PWD_To_Edit_Plan": "请输入支付密码，以修改花花存", "HHC_Did_Add": "已添加", "HHC_Did_Modify": "已修改", "HHC_Did_Pause": "已暂停", "HHC_Did_Open": "已开始", "HHC_Deposit_Plan": "存入计划", "HoneyPay_CheckPwd_Unbind_Title": "解绑亲属卡", "HoneyPay_Modify_CreditLimit_Title": "修改每月消费上限", "HoneyPay_Modify_CreditLimit_Desc": "每月消费上限", "HoneyPay_Modify_CreditLimit_Max_Alert": "金额不能超过¥{:.2f}", "f2f_pay_extrabuy_detail_modal_original_price": "（原价¥{:.2f}）", "pay_settings_delay_transfer_page_title": "转账到账时间", "pay_settings_delay_transfer_page_desc": "收款方接收后，资金将按如下时间存入对方零钱账户。转账发出后不支持撤回，转账前请仔细核对收款人信息。", "pay_settings_delay_transfer_no_delay": "实时到账", "pay_settings_delay_transfer_two_hour": "2小时到账", "pay_settings_delay_transfer_one_day": "24小时到账", "pay_settings_biometric_pay_enabled": "已开启", "pay_settings_biometric_pay_disabled": "已关闭", "pay_settings_biometric_pay_touchid_title": "手机指纹识别", "pay_settings_biometric_pay_faceid_title": "手机面容识别", "pay_settings_biometric_pay_multi_title": "手机面容/指纹识别", "pay_settings_biometric_pay_touchid_desc": "开启后，支付时可验证指纹，快速完成付款。", "pay_settings_biometric_pay_faceid_desc": "开启后，支付时可验证面容ID，快速完成付款。", "pay_settings_biometric_pay_multi_desc": "你可以选择开启面容或指纹识别。开启后，支付时可验证面容或指纹，快速完成付款。", "pay_settings_biometric_pay_enable_faceid": "开启手机面容识别", "pay_settings_biometric_pay_enable_touchid": "开启手机指纹识别", "Wallet_Mix_Paid_UnKnown_Error": "交易请求已提交，请留意___<BRAND_Pay_Oversea>___ Hong Kong公众号下发的消息通知。支付状态未明确前，请勿重复支付", "Accessibility_Type_CheckBox_Selected": "勾选框,已选中,{},按钮", "Accessibility_Type_CheckBox_UnSelected": "勾选框,未选中,{},按钮", "Accessibility_Type_Selected": "已选择,{},按钮", "Accessibility_Type_UnSelected": "未选择,{},按钮", "Accessibility_Type_SwitchView_Selected": "{},切换按钮,打开", "Accessibility_Type_SwitchView_UnSelected": "{},切换按钮,关闭", "Accessibility_String_PwdInputView": "密码框,共六位数字,已输入{}位", "Accessibility_State_Disabled": "变暗", "common_button": "按钮", "common_link": "链接", "balance_entry_balnce_title": "我的零钱", "balance_entry_balnce_detail": "零钱明细", "balance_entry_powered_by_tenpay": "本服务由财付通提供", "balance_recharge_page_title": "充值", "balance_recharge_card_info_title": "充值方式", "balance_recharge_payment_new_card": "使用新卡充值", "YunShanFu_Loading_Wording": "正在打开云闪付", "YunShanFu_Uninstalled_Error": "你未安装云闪付，建议安装云闪付后重试或使用___<BRAND_Pay>___继续支付", "Dcep_Loading_Wording": "加载中", "Dcep_Uninstalled_Error": "你未安装数字人民币，建议安装数字人民币后重试或使用___<BRAND_Pay>___继续支付", "Pay_Android_Fingerprint_Prompt_Title": "请验证指纹", "Pay_Android_Fingerprint_Prompt_SubTitle": "以完成支付", "Pay_Android_Fingerprint_Prompt_Button": "使用密码", "Accessibility_Collapse_Header_Collapsed": "{},已折叠", "Accessibility_Collapse_Header_Showed": "{},已展开", "HoneyPay_Add_Card": "赠送亲属卡", "HoneyPay_Select_Contact_Title": "选择联系人", "HoneyPay_Modify_Comment": "修改留言", "HoneyPay_MoneyInput_Hint": "输入金额", "HoneyPay_MoneyInput_Hint_New": "填写金额", "HoneyPay_CreateCard_Btn": "赠送", "HoneyPay_Max_Amount_Notice": "金额不能超过¥{:.2f}", "HoneyPay_Modify_Credit": "每月消费上限", "HoneyPay_Main_Title": "亲属卡", "HoneyPay_Record_Receive_Title": "我收到的亲属卡", "HoneyPay_Record_Donate_Title": "我赠送的亲属卡", "hbrefund_info_tips": "到账说明", "hbrefund_time_title": "红包退款到账时间", "hbrefund_forbid_way": "不再支持该退款方式", "hbrefund_had_set": "已设置", "hbrefund_origin_desc": "发出的红包若在24小时内未被好友领取，资金将退回原支付方式。", "hbrefund_set_tips": "设置后，未被领取的红包将退回原支付方式。后续将无法调整为「退回零钱」，确定设置为「退回原支付方式」？", "bankcard_detail": "{} 尾号 {}", "bankcard_qmf_detail": "{} 赠卡者, {}", "TeenagerPayDetailUIPage_NotSet": "无", "TeenagerPayDetailUIPage_LimitedAmount": "¥{:.{}f}", "TeenagerPaySetLimitModal_AmountTitle": "金额", "TeenagerPaySetLimitModal_MaxAmount": "最多输入7位数", "TeenagerPayGetDetailUseCase_LimitOn": "已限额", "TeenagerPayGetDetailUseCase_LimitOff": "未限额", "TeenagerPayUseCase_Set_Ok": "已设置", "TeenagerPayUseCase_Close_Ok": "已取消限额", "TeenagerPayUseCase_Limit_Max": "单次消费限额不可超过已设置的每日消费限额", "TeenagerPayUseCase_Limit_Min": "每日消费限额不可小于已设置的单次消费限额", "TeenagerPayUseCase_Input_Accesibility": "文本栏", "LQTDetail_balance_Accessibility": "账户余额¥{:.2f}", "FaceCheck_Agreement_title": "刷脸验证", "FaceCheck_Success_title": "已通过", "FaceCheck_Result_Retry": "再试一次", "FaceCheck_Common_Error": "系统繁忙，请重试", "FaceCheck_MP_Request_Use": " 申请使用", "FaceCheck_MP_Confirm_Tip": "___<BRAND>___人脸识别验证你的身份信息，请确保本人操作", "FaceCheck_MP_Front_Feedback": "帮助中心", "FaceCheck_Recoging": "识别中", "WCPay_LQT_OnClickPurchase_Fail_Network_Error": "网络异常，无法获取你的银行卡列表，请稍后重试。", "WCPay_LQT_OnClickRedeem_Fail_Network_Error": "网络异常，无法获取你的银行卡列表，请稍后重试。", "WCPay_LQT_PurchaseFund_Fail_Network_Error": "申购下单失败，请稍后再试", "WCPay_LQT_QryPurchaseResult_Fail_Network_Error": "申购结果查询失败", "WCPay_LQT_PreRedeemFund_Fail_Network_Error": "赎回下单失败，请稍后再试", "WCPay_LQT_RedeemFund_Fail_Network_Error": "资金赎回失败，请稍后再试", "WCPay_LQT_MoneyVC_Save_TextFieldTips": "转入金额", "WCPay_LQT_MoneyVC_Fetch_TextFieldTips": "转出金额", "TabBar_NewBadge": "New", "RedDot_New": "NEW", "AddPayCard_No_Card_Bind_Card_Title": "免输卡号添加", "AddPayCard_Manual_Bind_Card_Title": "输入卡号添加", "FillCard_Number_Reg_Hint_V3": "输入{}本人银行卡号", "FillCard_Number_Reg_Hint_Self": "输入本人银行卡号", "FastBindCardSelectBankUIV2_Title": "免输卡号添加", "FastBindCardSelectBankUIV2_Search_Hint": "在{}家银行中搜索", "qrcode_collection_settings": "收款设置", "qrcode_collection_amount": "收款金额", "qrcode_collection_remark": "收款说明", "paysecurity_digital_cert_not_install": "未启用", "transfer_to_bank_name_input_placeholder": "收款人姓名", "transfer_to_bank_card_input_placeholder": "收款人银行卡号", "transfer_to_bank_bank_select_placeholder": "选择银行", "transfer_to_bank_arrival_time_select_title": "时间", "transfer_to_bank_arrival_time_modal_title": "选择到账时间", "transfer_to_bank_arrival_time_modal_desc": "发起转账申请后，资金将按如下时间存入对方账户。", "transfer_to_bank_history_page_title": "选择收款人", "transfer_to_bank_history_page_empty_prompt": "没有历史收款人", "transfer_to_bank_history_me_section_title": "本人", "transfer_to_bank_history_others_section_title": "历史收款人", "transfer_to_bank_history_modify_remark_action": "备注", "transfer_to_bank_history_set_remark_title": "添加备注", "transfer_to_bank_history_delete_action": "删除", "transfer_to_bank_bank_unavailable_alert": "该银行维护中，暂不支持转账", "transfer_to_bank_money_input_title": "转账金额", "transfer_to_bank_info_receiver_format": "收款人：{}", "transfer_to_bank_info_charge_fee": "服务费", "transfer_to_bank_info_charge_fee_rate_format": "（费率{:.2f}）", "transfer_to_bank_info_total_amount": "支付总金额", "transfer_to_bank_info_transfer_explain": "转账说明", "transfer_to_bank_info_transfer_explain_edit_hint_format": "收付款双方可见，最多{}个字。", "transfer_to_bank_info_add_transfer_explain": "添加说明", "transfer_to_bank_info_detail_title": "转账详情", "transfer_to_bank_info_detail_current_state": "当前状态", "transfer_to_bank_info_detail_paid_success": "发起转账", "transfer_to_bank_info_detail_withdrawn_success": "到账成功", "waiting_for_real_name_authentication": "待实名认证", "HoneyPay_PrepareCardUI_Title": "设置亲属卡", "FillCardNumberV2_CountryCode_Hint": "请输入国际区号", "FillCardNumberV2_CountryCodeView_Hint": "请选择", "WCPay_Digital_Cert_Desc_Not_Install": "未启用数字证书", "WCPay_Digital_Cert_Desc_Already_Install": "已启用数字证书", "WCPay_Digital_Cert_Manage_Content_Desc": "当前设备启用数字证书将：", "WCPay_Digital_Cert_Manage_Content_Desc_1": "• 提高支付安全性。", "WCPay_Digital_Cert_Manage_Content_Desc_2": "• 提高每日零钱支付限额。", "WCPay_Digital_Cert_Install_Button_Title": "启用证书", "WCPay_Digital_Cert_Delete_Button_Title": "停用证书", "WCPay_Digital_Cert_Install_List_desc": "已启用数字证书的其他设备", "WCPay_Digital_Cert_Delete_Confirm_Content": "是否确认停用当前___<BRAND_ID>___在本机上的数字证书？", "WCPay_Digital_Delete_Confirm_Btn_Title": "停用", "WCPay_Digital_Cert_Install_Action_Title": "验证身份", "WCPay_Digital_Cert_Install_Action_Desc": "启用证书需验证当前账户的身份信息。", "WCPay_Digital_Cert_Install_Input_Title_default": "身份证", "WCPay_Digital_Cert_Install_Input_Desc_default": "请输入证件号码", "WCPay_Digital_Cert_Install_Input_Desc": "请输入{}的证件号码", "WCPay_Digital_Cert_Verify_Button_Title": "验证", "WCPay_Digital_Cert_Install_Sccuess": "已验证", "WCPay_Digital_Cert_Delete_Succ_Toast": "已停用", "WCPay_Risk_Dialog_Title": "系统检测到潜在风险，请确定是否继续操作。如继续，需进行身份验证。", "WCPay_Risk_Not_Support_Dialog_Title": "系统检测到潜在风险，请排查风险后继续操作。", "WCPay_Risk_Failed_Dialog_Title": "验证失败，需终止操作。", "WCPay_Option_Item": "选填", "collect_sub_title": "收款金额", "collect_main_add_desc_title_simple_change": "修改", "collect_main_add_desc_title": "添加收钱备注", "remittance_amount_lowest_limit": "转账最低金额不能小于0.01元", "collect_main_fixed": "设置金额", "collect_main_first_enter_tips_title": "收到的钱将存入哪里", "collect_main_first_enter_tips_new": "收到的钱将存入你的___<BRAND_Balance>___ （我-服务-钱包-零钱），可用于消费或提现。", "collect_main_close_ring_tone": "关闭收款到账语音提醒", "collect_main_close_ring_tone_tips": "已关闭", "collect_main_open_ring_tone": "开启收款到账语音提醒", "collect_main_open_ring_tone_tips": "已开启，请确认媒体音已打开", "collect_main_qrcode_usage_other": "其他", "collect_main_qrcode_usage_other_placeholder": "请补充信息，最多16个字", "collect_main_payer_desc_default_placeholder": "请填写备注给收款方", "collect_qrcode_save_failed": "保存失败", "collect_material_guide_save_text_toast": "已保存到系统相册", "collect_mch_module_title": "经营收款码", "collect_personal_module_title": "个人收款码", "collect_setting_title": "收款设置", "collect_main_fixed_cancel": "清除金额", "collect_main_more_function": "更多设置", "collect_main_save_qrcode": "保存收款码", "collect_main_receive_title": "收款总计", "collect_main_paying": "支付中...", "collect_main_pay_suc": "支付成功", "collect_main_pay_cancel": "取消支付", "collect_main_loading_title": "正在加载二维码", "collect_main_ring_not_support": "当前系统暂不支持此操作", "common_help": "帮助", "WCPay_Transfer_To_Format": "转账给 {}", "WCPay_Transfer_Weixin_Alias_Prefix": "___<BRAND_ID>___：", "WCPay_Transfer_InputAmount_Tips": "需先填写金额", "WCPay_Transfer_Cashier_Desc_Format": "向{}转账", "WCPay_Transfer_Succ_Tips": "待{}确认收款", "WCPay_Service": "服务", "recognize_and_pay": "识别并支付", "bizf2f_input_ui_page_to_person": "付款给个人", "bizf2f_input_ui_page_add_remark": "添加备注", "bizf2f_input_ui_page_amount_title": "付款金额", "WCPay_Verify_Password_Get_SMS_Code": "接收验证码", "WCPay_VerifyCode_Header_Description": "本次交易需要进行短信验证", "bizf2f_input_ui_page_pay_action": "付款", "bizf2f_input_ui_page_change_remark": "修改", "bizf2f_input_ui_page_pay_title": "付款", "bizf2f_favor_title": "优惠", "bizf2f_favor_total_fee": "付款总额", "bizf2f_favor_calculating": "计算中", "bizf2f_favor_select_favor": "选择优惠", "UN_BIND_CARD_TITLE": "解绑银行卡", "WCPay_system_version_limitation_tip": "其他功能请前往鸿蒙4.2及以下系统或其他设备查看", "reconfirm_payment_amount_title": "再次确认付款金额", "reconfirm_payment_amount_des": "为了保护你的资产安全，防止付错钱，请再次确认需要支付的金额", "reconfirm_amount_page_tip": "根据监管要求，静态码超额交易需通过识别下方动态码完成，点击页面按钮识别并完成支付", "Hongbao_SendUI_NavigationBar_Title": "发红包", "Hongbao_SendUI_Send_Button_Titlle": "塞钱进红包", "Hongbao_SendUI_Count_Title": "红包个数", "Hongbao_SendUI_Amount_Title_Group": "总金额", "Hongbao_SendUI_Amount_Title_Single": "金额", "Hongbao_SendUI_RandomLuckyMode_Title": "拼手气红包", "Hongbao_SendUI_Count_Tips": "填写红包个数", "Hongbao_SendUI_Amount_Tips": "¥0.00", "Hongbao_SendUI_Default_Wishing": "恭喜发财，大吉大利", "Hongbao_Per_Hongbao_Max_Amount_Format": "单个红包金额不可超过{}元", "HongBao_SendTips": "发出的红包", "HongBao_OpenTips": "拆红包", "HongBao_AmoutTips": "元", "HongBao_MainTitle": "红包", "HongBao_Cashier_Desc": "___<BRAND_hongbao>___", "Hongbao_SendUI_RandomMode_Title": "拼手气红包", "Hongbao_SendUI_NormalMode_Title": "普通红包", "Hongbao_SendUI_ExclusiveMode_Title": "专属红包", "Hongbao_SendUI_Select_Count_Suffix": "个", "Hongbao_SendUI_Group_Member_Count_Format": "本群共{}人", "Hongbao_SendUI_Amount_Title_Group_Normal": "单个金额", "Hongbao_SendUI_Perperson_Maxvalue_Error_Tips": "单个红包金额不可超过{}元", "Hongbao_SendUI_Perperson_Minvalue_Error_Tips": "单个红包金额不可低于{:.2f}元", "Hongbao_SendUI_Count_Larger_Than_Group_Mem_Count_Error_Tips": "红包个数不可超过当前群聊人数", "Hongbao_SendUI_Total_Num_Error_Tips": "一次最多发{}个红包", "Hongbao_SendUI_Select_Count_Data_Invalid_Error_Tips": "未填写「红包个数」", "Hongbao_SendUI_Amount_Data_Invalid_Error_Tips": "未填写「总金额」", "Hongbao_SendUI_Select_Count_Zero_Error_Tips": "请选择红包个数", "Hongbao_SendUI_Amount_Larger_Than_Max_Amount_Error_Tips": "单次支付总额不可超过{}元", "Hongbao_ReceiveModal_Detail_Link": "查看领取详情", "Hongbao_DetailUI_Load_More_Text": "点击加载更多", "TransferPhone_Entry_Title": "请选择转账方式", "TransferPhone_To_Bank_Title": "向银行卡转账", "TransferPhone_To_Bank_Desc": "输入银行卡号，可转账至对方银行账户。", "TransferPhone_To_Phone_Title": "向手机号转账", "TransferPhone_To_Phone_Desc": "输入手机号，可转账至对方___<BRAND_Balance>___。", "TransferPhone_To_PaySetting": "手机号收款设置", "WCPay_ThirdParty_Tips_Title": "免责声明", "WCPay_Service_Manage": "服务管理", "identify_and_pay": "识别并支付", "Hongbao_SendUI_Exclusive_Mode_Send_To_Who": "发给谁", "Hongbao_SendUI_Exclusive_Mode_Send_To_Who_Error_Tips": "未填写「发给谁」", "Hongbao_SendUI_Amount_Data_Invalid_Error_Tips_2": "未填写「金额」", "MerchantPay_Input_Remark_Hint_Format": "收款方可见，最多{}个字", "MerchantPay_Input_Remark_Title": "添加备注", "MerchantPay_Transfer_To_Format": "付款给 {}", "Greeting_Hongbao_Random_Change_Amount": "切换金额", "Greeting_Hongbao_Who_Receive_Hongbao_Format": "{}已领取红包", "set_amount": "设置金额", "Card_Record_Component_FQF_Left_Title": "资金渠道"}