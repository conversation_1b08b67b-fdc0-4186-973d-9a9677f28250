{"v": "5.7.1", "fr": 24, "ip": 0, "op": 37, "w": 504, "h": 504, "nm": "大福袋2_bmp", "ddd": 0, "assets": [{"id": "image_0", "w": 241, "h": 212, "u": "images/", "p": "img_0.png", "e": 0}, {"id": "image_1", "w": 30, "h": 30, "u": "images/", "p": "img_1.png", "e": 0}, {"id": "image_2", "w": 17, "h": 17, "u": "images/", "p": "img_2.png", "e": 0}, {"id": "image_3", "w": 35, "h": 35, "u": "images/", "p": "img_3.png", "e": 0}, {"id": "image_4", "w": 16, "h": 16, "u": "images/", "p": "img_4.png", "e": 0}, {"id": "image_5", "w": 19, "h": 20, "u": "images/", "p": "img_5.png", "e": 0}, {"id": "image_6", "w": 16, "h": 16, "u": "images/", "p": "img_6.png", "e": 0}, {"id": "image_7", "w": 9, "h": 9, "u": "images/", "p": "img_7.png", "e": 0}, {"id": "comp_0", "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "大福袋.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [200, 279, 0], "ix": 2}, "a": {"a": 0, "k": [120.5, 106, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 3, "s": [93, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 6, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 10, "s": [115, 86, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 14, "s": [93.25, 105.25, 100]}, {"t": 18, "s": [100, 100, 100]}], "ix": 6}}, "ao": 0, "ef": [{"ty": 34, "nm": "操控", "np": 6, "mn": "ADBE FreePin3", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "人偶引擎", "mn": "ADBE FreePin3 Puppet Engine", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}, {"ty": 0, "nm": "网格旋转调整", "mn": "ADBE FreePin3 Auto Rotate Pins", "ix": 2, "v": {"a": 0, "k": 20, "ix": 2}}, {"ty": 7, "nm": "在透明背景上", "mn": "ADBE FreePin3 On Transparent", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 24, "nm": "arap", "np": 3, "mn": "ADBE FreePin3 ARAP Group", "ix": 4, "en": 1, "ef": [{"ty": 6, "nm": "自动追踪形状", "mn": "ADBE FreePin3 Outlines", "ix": 1, "v": 0}, {"ty": 1, "nm": "网格", "np": 2, "mn": "ADBE FreePin3 Mesh Group", "ix": 2, "en": 1, "ef": [{"nm": "网格 1", "np": 8, "mn": "ADBE FreePin3 Mesh Atom", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "网格", "mn": "ADBE FreePin3 Mesh", "ix": 1, "v": 0}, {"ty": 0, "nm": "三角形", "mn": "ADBE FreePin3 Mesh Tri Count", "ix": 2, "v": {"a": 0, "k": 50, "ix": 2}}, {"ty": 0, "nm": "密度", "mn": "ADBE FreePin3 Mesh Tri Density", "ix": 3, "v": {"a": 0, "k": 10, "ix": 3}}, {"ty": 0, "nm": "扩展", "mn": "ADBE FreePin3 Mesh Expansion", "ix": 4, "v": {"a": 0, "k": 3, "ix": 4}}, {"nm": "变形", "np": 5, "mn": "ADBE FreePin3 PosPins", "ix": 5, "en": 1, "ef": [{"nm": "操控点 4", "np": 7, "mn": "ADBE FreePin3 PosPin Atom", "ix": 1, "en": 1, "ef": [{"ty": 3, "nm": "顶点位移", "mn": "ADBE FreePin3 PosPin Vtx Offset", "ix": 1, "v": {"a": 0, "k": [0, 0], "ix": 1}}, {"ty": 0, "nm": "顶点索引", "mn": "ADBE FreePin3 PosPin Vtx Index", "ix": 2, "v": {"a": 0, "k": 38, "ix": 2}}, {"ty": 7, "nm": "固定类型", "mn": "ADBE FreePin3 PosPin Type", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 3, "nm": "位置", "mn": "ADBE FreePin3 PosPin Position", "ix": 4, "v": {"a": 0, "k": [117.5, 11], "ix": 4}}, {"ty": 0, "nm": "缩放", "mn": "ADBE FreePin3 PosPin Scale", "ix": 5, "v": {"a": 0, "k": 100, "ix": 5}}, {"ty": 0, "nm": "旋转", "mn": "ADBE FreePin3 PosPin Rotation", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}, {"nm": "操控点 3", "np": 7, "mn": "ADBE FreePin3 PosPin Atom", "ix": 2, "en": 1, "ef": [{"ty": 3, "nm": "顶点位移", "mn": "ADBE FreePin3 PosPin Vtx Offset", "ix": 1, "v": {"a": 0, "k": [0, 0], "ix": 1}}, {"ty": 0, "nm": "顶点索引", "mn": "ADBE FreePin3 PosPin Vtx Index", "ix": 2, "v": {"a": 0, "k": 39, "ix": 2}}, {"ty": 7, "nm": "固定类型", "mn": "ADBE FreePin3 PosPin Type", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 3, "nm": "位置", "mn": "ADBE FreePin3 PosPin Position", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": 0, "y": 0}, "o": {"x": 0, "y": 0}, "t": 0, "s": [157.052, 49.751], "to": [3.575, -0.792], "ti": [-2.741, 0.625]}, {"i": {"x": 0, "y": 0}, "o": {"x": 0, "y": 0}, "t": 0, "s": [178.5, 45], "to": [2.741, -0.625], "ti": [0.833, -0.167]}, {"t": null, "s": [173.5, 46]}], "ix": 4}}, {"ty": 0, "nm": "缩放", "mn": "ADBE FreePin3 PosPin Scale", "ix": 5, "v": {"a": 0, "k": 100, "ix": 5}}, {"ty": 0, "nm": "旋转", "mn": "ADBE FreePin3 PosPin Rotation", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}, {"nm": "操控点 2", "np": 7, "mn": "ADBE FreePin3 PosPin Atom", "ix": 3, "en": 1, "ef": [{"ty": 3, "nm": "顶点位移", "mn": "ADBE FreePin3 PosPin Vtx Offset", "ix": 1, "v": {"a": 0, "k": [0, 0], "ix": 1}}, {"ty": 0, "nm": "顶点索引", "mn": "ADBE FreePin3 PosPin Vtx Index", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 7, "nm": "固定类型", "mn": "ADBE FreePin3 PosPin Type", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 3, "nm": "位置", "mn": "ADBE FreePin3 PosPin Position", "ix": 4, "v": {"a": 1, "k": [{"i": {"x": 0, "y": 0}, "o": {"x": 0, "y": 0}, "t": 0, "s": [76.658, 49.751], "to": [-2.86, -0.792], "ti": [1.86, 0.625]}, {"i": {"x": 0, "y": 0}, "o": {"x": 0, "y": 0}, "t": 0, "s": [59.5, 45], "to": [-1.86, -0.625], "ti": [-1, -0.167]}, {"t": null, "s": [65.5, 46]}], "ix": 4}}, {"ty": 0, "nm": "缩放", "mn": "ADBE FreePin3 PosPin Scale", "ix": 5, "v": {"a": 0, "k": 100, "ix": 5}}, {"ty": 0, "nm": "旋转", "mn": "ADBE FreePin3 PosPin Rotation", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}, {"nm": "操控点 1", "np": 7, "mn": "ADBE FreePin3 PosPin Atom", "ix": 4, "en": 1, "ef": [{"ty": 3, "nm": "顶点位移", "mn": "ADBE FreePin3 PosPin Vtx Offset", "ix": 1, "v": {"a": 0, "k": [0, 0], "ix": 1}}, {"ty": 0, "nm": "顶点索引", "mn": "ADBE FreePin3 PosPin Vtx Index", "ix": 2, "v": {"a": 0, "k": 41, "ix": 2}}, {"ty": 7, "nm": "固定类型", "mn": "ADBE FreePin3 PosPin Type", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 3, "nm": "位置", "mn": "ADBE FreePin3 PosPin Position", "ix": 4, "v": {"a": 0, "k": [116.5, 51], "ix": 4}}, {"ty": 0, "nm": "缩放", "mn": "ADBE FreePin3 PosPin Scale", "ix": 5, "v": {"a": 0, "k": 100, "ix": 5}}, {"ty": 0, "nm": "旋转", "mn": "ADBE FreePin3 PosPin Rotation", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}]}]}, {"nm": "重叠", "np": 1, "mn": "ADBE FreePin3 HghtPins", "ix": 6, "en": 1, "ef": []}, {"nm": "硬度", "np": 1, "mn": "ADBE FreePin3 StarchPins", "ix": 7, "en": 1, "ef": []}]}]}]}]}], "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "大紫.png", "cl": "png", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [100]}, {"t": 37, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.61, "y": 1}, "o": {"x": 0.167, "y": 0.25}, "t": 22, "s": [205, 192, 0], "to": [-35.5, -60.333, 0], "ti": [42.5, -60.667, 0]}, {"t": 37, "s": [58, 166, 0]}], "ix": 2}, "a": {"a": 0, "k": [15, 15, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "大绿.png", "cl": "png", "refId": "image_2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [100]}, {"t": 32, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.52, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [215, 203, 0], "to": [0, -27.167, 0], "ti": [0, 27.167, 0]}, {"t": 32, "s": [215, 40, 0]}], "ix": 2}, "a": {"a": 0, "k": [8.5, 8.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 20, "s": [100, 100, 100]}, {"t": 32, "s": [130, 130, 100]}], "ix": 6}}, "ao": 0, "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 2, "nm": "大星星.png", "cl": "png", "refId": "image_3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 11, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [100]}, {"t": 23, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 11, "s": [0]}, {"t": 23, "s": [128]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 11, "s": [200, 200, 0], "to": [0, -14.833, 0], "ti": [0, 23.833, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [200, 111, 0], "to": [0, -23.833, 0], "ti": [0, 9, 0]}, {"t": 23, "s": [200, 57, 0]}], "ix": 2}, "a": {"a": 0, "k": [17.5, 17.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 11, "s": [100, 100, 100]}, {"t": 23, "s": [134, 134, 100]}], "ix": 6}}, "ao": 0, "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 2, "nm": "蓝.png", "cl": "png", "refId": "image_4", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 7, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 16, "s": [100]}, {"t": 21, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7, "s": [204, 193, 0], "to": [0, -16.333, 0], "ti": [0, 16.333, 0]}, {"t": 21, "s": [204, 95, 0]}], "ix": 2}, "a": {"a": 0, "k": [8, 8, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 7, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 15, "s": [119, 119, 100]}, {"t": 21, "s": [87, 87, 100]}], "ix": 6}}, "ao": 0, "ip": -12, "op": 348, "st": -12, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 2, "nm": "红.png", "cl": "png", "refId": "image_5", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 32, "s": [100]}, {"t": 36, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [190, 188, 0], "to": [0, 0, 0], "ti": [-37.648, 19.547, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 29, "s": [214.228, 115.629, 0], "to": [40.345, -20.947, 0], "ti": [-21.826, -30.063, 0]}, {"t": 36, "s": [295, 125, 0]}], "ix": 2}, "a": {"a": 0, "k": [9.5, 10, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 22, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 29, "s": [120, 120, 100]}, {"t": 36, "s": [100, 100, 100]}], "ix": 6}}, "ao": 0, "ip": 1, "op": 361, "st": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 2, "nm": "红.png", "cl": "png", "refId": "image_5", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 18, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 28, "s": [100]}, {"t": 32, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [247, 195, 0], "to": [0, 0, 0], "ti": [-37.648, 19.547, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 25, "s": [271.228, 122.629, 0], "to": [40.345, -20.947, 0], "ti": [-21.826, -30.063, 0]}, {"t": 32, "s": [352, 132, 0]}], "ix": 2}, "a": {"a": 0, "k": [9.5, 10, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 18, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 25, "s": [120, 120, 100]}, {"t": 32, "s": [100, 100, 100]}], "ix": 6}}, "ao": 0, "ip": -3, "op": 357, "st": -3, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 2, "nm": "红.png", "cl": "png", "refId": "image_5", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 12, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [100]}, {"t": 27, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [190, 188, 0], "to": [-29.167, -89, 0], "ti": [49.167, -141, 0]}, {"t": 27, "s": [31, 161, 0]}], "ix": 2}, "a": {"a": 0, "k": [9.5, 10, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 12, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 19, "s": [200, 200, 100]}, {"t": 27, "s": [100, 100, 100]}], "ix": 6}}, "ao": 0, "ip": -6, "op": 354, "st": -6, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 2, "nm": "黄.png", "cl": "png", "refId": "image_6", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 11, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 21, "s": [100]}, {"t": 25, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 11, "s": [175, 207, 0], "to": [0, -9.002, 0], "ti": [0, 22.327, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17, "s": [175, 137.714, 0], "to": [0, -28.106, 0], "ti": [0, 11.332, 0]}, {"t": 25, "s": [175, 54, 0]}], "ix": 2}, "a": {"a": 0, "k": [8, 8, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 11, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 17, "s": [180, 180, 100]}, {"t": 25, "s": [80, 80, 100]}], "ix": 6}}, "ao": 0, "sy": [{"c": {"a": 0, "k": [1, 1, 1, 1], "ix": 5}, "o": {"a": 0, "k": 58, "ix": 2}, "s": {"a": 0, "k": 17, "ix": 10}, "r": {"a": 0, "k": 50, "ix": 11}, "ch": {"a": 0, "k": 0, "ix": 9}, "bm": {"a": 0, "k": 11, "ix": 1}, "no": {"a": 0, "k": 0, "ix": 3}, "j": {"a": 0, "k": 0, "ix": 12}, "ty": 3, "nm": "外发光"}], "ip": -9, "op": 351, "st": -9, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 2, "nm": "蓝.png 2", "cl": "png", "refId": "image_4", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 11, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [100]}, {"t": 33, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 11, "s": [206, 196, 0], "to": [-0.482, -25.054, 0], "ti": [-28.756, 7.625, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [256.882, 136.471, 0], "to": [26.171, -6.939, 0], "ti": [-0.482, -93.221, 0]}, {"t": 35, "s": [338, 234, 0]}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\ntry {\n    amp = $bm_div(effect('Elastic Controller \\u4f4d\\u7f6e')(1), 200);\n    freq = $bm_div(effect('Elastic Controller \\u4f4d\\u7f6e')(2), 30);\n    decay = $bm_div(effect('Elastic Controller \\u4f4d\\u7f6e')(3), 10);\n    $bm_rt = n = 0;\n    if (numKeys > 0) {\n        $bm_rt = n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    if (n == 0) {\n        $bm_rt = t = 0;\n    } else {\n        $bm_rt = t = $bm_sub(time, key(n).time);\n    }\n    if (n > 0) {\n        v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n        $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n    } else {\n        $bm_rt = value;\n    }\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [8, 8, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 11, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 20, "s": [130, 130, 100]}, {"t": 37, "s": [30, 30, 100]}], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic Controller 位置", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 60, "ix": 3}}]}], "ip": -1, "op": 359, "st": -1, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 2, "nm": "绿.png", "cl": "png", "refId": "image_7", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 6, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 17, "s": [100]}, {"t": 23, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.999, "y": 1}, "o": {"x": 0.001, "y": 0}, "t": 6, "s": [202, 203, 0], "to": [-5.333, -35, 0], "ti": [28.333, 35.5, 0]}, {"i": {"x": 0.999, "y": 1}, "o": {"x": 0.001, "y": 0}, "t": 15, "s": [215, 127, 0], "to": [-20.059, -25.133, 0], "ti": [-49.667, 6, 0]}, {"t": 23, "s": [236, 63, 0]}], "ix": 2}, "a": {"a": 0, "k": [4.5, 4.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 6, "s": [100, 100, 100]}, {"t": 23, "s": [54, 54, 100]}], "ix": 6}}, "ao": 0, "ip": -6, "op": 354, "st": -6, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 2, "nm": "绿.png", "cl": "png", "refId": "image_7", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 11, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [100]}, {"t": 28, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.999, "y": 1}, "o": {"x": 0.001, "y": 0}, "t": 11, "s": [189, 201, 0], "to": [-5.333, -35, 0], "ti": [28.333, 35.5, 0]}, {"i": {"x": 0.999, "y": 1}, "o": {"x": 0.001, "y": 0}, "t": 20, "s": [144, 139, 0], "to": [-20.059, -25.133, 0], "ti": [52.333, 3, 0]}, {"t": 28, "s": [63, 46, 0]}], "ix": 2}, "a": {"a": 0, "k": [4.5, 4.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 11, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 20, "s": [317.647, 317.647, 100]}, {"t": 28, "s": [97, 97, 100]}], "ix": 6}}, "ao": 0, "sy": [{"c": {"a": 0, "k": [1, 1, 1, 1], "ix": 5}, "o": {"a": 0, "k": 80, "ix": 2}, "s": {"a": 0, "k": 20, "ix": 10}, "r": {"a": 0, "k": 50, "ix": 11}, "ch": {"a": 0, "k": 0, "ix": 9}, "bm": {"a": 0, "k": 11, "ix": 1}, "no": {"a": 0, "k": 0, "ix": 3}, "j": {"a": 0, "k": 0, "ix": 12}, "ty": 3, "nm": "外发光"}], "ip": -6, "op": 354, "st": -6, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 2, "nm": "绿.png", "cl": "png", "refId": "image_7", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 8, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [100]}, {"t": 25, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [198, 195, 0], "to": [-5.333, -35, 0], "ti": [-16.667, 27.5, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17, "s": [214, 103, 0], "to": [16.667, -27.5, 0], "ti": [-14.667, 3, 0]}, {"t": 25, "s": [278, 63, 0]}], "ix": 2}, "a": {"a": 0, "k": [4.5, 4.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 8, "s": [100, 100, 100]}, {"t": 25, "s": [54, 54, 100]}], "ix": 6}}, "ao": 0, "ip": -6, "op": 354, "st": -6, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "大福袋", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 6, "s": [100]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 1, "s": [0]}, {"t": 3, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.999, "y": 1}, "o": {"x": 0.21, "y": 0}, "t": 1, "s": [251, 76, 0], "to": [0, 34.667, 0], "ti": [0, -34.667, 0]}, {"t": 5, "s": [251, 284, 0]}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\ntry {\n    amp = $bm_div(effect('Elastic Controller \\u4f4d\\u7f6e')(1), 200);\n    freq = $bm_div(effect('Elastic Controller \\u4f4d\\u7f6e')(2), 30);\n    decay = $bm_div(effect('Elastic Controller \\u4f4d\\u7f6e')(3), 10);\n    $bm_rt = n = 0;\n    if (numKeys > 0) {\n        $bm_rt = n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    if (n == 0) {\n        $bm_rt = t = 0;\n    } else {\n        $bm_rt = t = $bm_sub(time, key(n).time);\n    }\n    if (n > 0) {\n        v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n        $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n    } else {\n        $bm_rt = value;\n    }\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [200, 284, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 1, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 4, "s": [120, 120, 100]}, {"t": 8, "s": [100, 100, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\ntry {\n    amp = $bm_div(effect('Elastic Controller \\u7f29\\u653e')(1), 200);\n    freq = $bm_div(effect('Elastic Controller \\u7f29\\u653e')(2), 30);\n    decay = $bm_div(effect('Elastic Controller \\u7f29\\u653e')(3), 10);\n    $bm_rt = n = 0;\n    if (numKeys > 0) {\n        $bm_rt = n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    if (n == 0) {\n        $bm_rt = t = 0;\n    } else {\n        $bm_rt = t = $bm_sub(time, key(n).time);\n    }\n    if (n > 0) {\n        v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n        $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n    } else {\n        $bm_rt = value;\n    }\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic Controller 缩放", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 60, "ix": 3}}]}, {"ty": 5, "nm": "Elastic Controller 位置", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 60, "ix": 3}}]}], "w": 400, "h": 400, "ip": 0, "op": 360, "st": 0, "bm": 0}], "markers": []}