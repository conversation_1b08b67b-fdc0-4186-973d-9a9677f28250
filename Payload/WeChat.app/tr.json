{"BindCard_Gender_Female": "Kadın", "BindCard_Gender_Male": "<PERSON><PERSON><PERSON>", "Choose_Deposit_Time": "<PERSON><PERSON>", "Choose_Payment": "Para Yatırma Yöntemini Seçin", "Continue_Pay": "Ödemeye Devam Et", "Day": "{}", "Each_Day_In_Month_Deposit": "her ay {}", "Each_WeekDay_Deposit": "Her {}", "ExposureInfo_Waiting_Wording": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>in...", "Fetch_Balance": "Para Çekme Bakiyesi", "Fetch_Balance_Bank_Proccessing": "Banka işlemi yapıyor", "Fetch_Balance_Open_Order": "Para Çekme İsteği", "Fetch_Balance_Success": "Başarıyla ulaştı", "FillCard_Info_ErrorTips_Format": "{} ({} toplam hata)", "FillCard_Number_Default_Mobile_Modify_Tips": "Cep telefonu numarası yanlışsa düzenlemek için dokunun.", "FillCard_Number_Reg_Hint": "Banka kartı numaranız", "FillCard_Number_Unreg_Hint": "___<BRAND>___ hesap sahibinin banka kartı numarası", "Friday": "<PERSON><PERSON>", "Give_Up": "Vazgeç", "HHC_Check_PWD_To_Add_Plan": "Harca ve Tasarruf Et'i başlatmak için ödeme parolasını girin", "HHC_Check_PWD_To_Edit_Plan": "Harca ve Tasarruf Et'i değiştirmek için ödeme parolasını girin", "HHC_Check_PWD_To_Pause_Plan": "Harca ve Tasarruf Et'i askıya almak için ödeme parolasını girin", "HHC_Check_PWD_To_Start_Plan": "Harca ve Tasarruf Et'i etkinleştirmek için ödeme parolasını girin", "HHC_Choose_Payment": "<PERSON><PERSON>", "HHC_Deposit_Plan": "Para Yatırma <PERSON>", "HHC_Did_Modify": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HHC_Did_Open": "Başladı", "HHC_Did_Pause": "Askıya Alındı", "HHC_Name": "Harca ve Ta<PERSON>ruf <PERSON>", "HHC_Plan_Check_Amount": "Geçersiz miktar. Kontrol edin ve tekrar deneyin.", "HHC_Plan_Set_Bank_Card_Tip": "Harca ve Tasarruf Et'i kullanmak için önce mevduat kartını seçin.", "LQT_Fixed_Deposit": "Tekrarlanan Para Yatırma", "LQT_Fixed_Deposit_Check_PWD_To_Add_Plan": "Tekrarlanan para yatırmayı etkinleştirmek için ödeme parolasını girin", "LQT_Fixed_Deposit_Check_PWD_To_Delete_Plan": "Sil", "LQT_Fixed_Deposit_Check_PWD_To_Pause_Plan": "Zamanlamayı askıya almak için ödeme parolasını girin", "LQT_Fixed_Deposit_Check_PWD_To_Start_Plan": "<PERSON><PERSON><PERSON>", "LQT_Fixed_Deposit_Did_Delete": "<PERSON><PERSON><PERSON>", "LQT_Fixed_Deposit_Did_Modify": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LQT_Fixed_Deposit_Did_Open": "<PERSON><PERSON><PERSON>", "LQT_Fixed_Deposit_Did_Pause": "Askıya Alındı", "LQT_Fixed_Deposit_No_Plan": "Para yatırma zamanlaması yok", "LQT_Fixed_Deposit_Plan": "Para Yatırma <PERSON>", "LQT_Fixed_Deposit_Plan_Set_Bank_Card_Tip": "Tekrarlanan para yatırma işlemini ayarlamak için önce fonun dü<PERSON>ü<PERSON>ceği bir kart seçin.", "LQT_Fixed_Deposit_Plan_Set_Time_Tip": "Tekrarlanan para yatırma işlemini ayarlamak için önce bir para yatırma süresi seçin.", "LQT_Fixed_Deposit_Plan_Should_Input": "Tekrarlanan para yatırma işlemini ayarlamak için önce bir para yatırma tutarını seçin.", "ModifyPwdUseCase_ModifyPwd_Desc": "Kimliği doğrulamak için ödeme parolasını girin", "ModifyPwdUseCase_ModifyPwd_GiveUp_Title": "<PERSON><PERSON>me <PERSON>nızın değiştirilmesi iptal edilsin mi?", "ModifyPwdUseCase_ModifyPwd_Success": "<PERSON><PERSON><PERSON>", "ModifyPwdUseCase_ModifyPwd_Title": "<PERSON><PERSON><PERSON>", "Monday": "<PERSON><PERSON><PERSON>", "Monthly": "Aylık", "OfflinePay_CreateOfflinePay_ConfirmBtn_Title": "<PERSON><PERSON><PERSON>", "OfflinePay_CreateOfflinePay_Euro_Tips": "Hızlı Ödeme etkinleştirilmedi. Etkinleştirdikten sonra hızlı bir şekilde ödeme yapmak için kodu kasiyere gösterin. (Yalnızca CNY işlemlerini destekler)", "OfflinePay_CreateOfflinePay_Tips": "Hızlı Ödeme etkinleştirilmedi. Etkinleştirdikten sonra hızlı bir şekilde ödeme yapmak için kodu kasiyere gösterin.", "OfflinePay_ReCreateOfflinePay_ConfirmBtn_Title": "Etkinleştir", "OfflinePay_ReCreateOfflinePay_Euro_Tips": "Hızlı Ödeme etkinleştirilmedi. Ödeme yapan tüccarları hızlı ve kolay hale getirmek için etkinleştirin. Sadece kodu gösterin ve başlayın. Yalnızca CNY işlemleri desteklenmektedir.", "OfflinePay_ReCreateOfflinePay_Tips": "Hızlı Ödeme etkinleştirilmedi. Ödeme yapan tüccarları hızlı ve kolay hale getirmek için etkinleştirin. Sadece kodu gösterin ve başlayın.", "Saturday": "<PERSON><PERSON><PERSON><PERSON>", "Sunday": "Pazar", "Thursday": "Perşembe", "Tuesday": "Salı", "WCPay_BankCardBindTo0_0_5D_Detail": "Hesabın doğrulanması için 0.05$ tahsil edilecektir", "WCPay_BankCardBindTo0_0_5D_Detail_back": "Hesabın doğrulanması için 0.05$ tahsil edilecek ve doğrulama sonrası bu ücret iade edilecektir.", "WCPay_BankCardBindTo1B_Detail": "Hesabın doğrulanması için 0.01¥ tahsil edilecek ve doğrulama sonrası bu ücret iade edilecektir.", "WCPay_BankCardBindTo1B_NotReturnDetail": "Hesabın doğrulanıp kullanılmasını sağlamak için 0,01¥ tahsil edilecektir.", "WCPay_CountryCode_Title": "Ülke/Bölge", "WCPay_FaceID_Auth_Tip": "Ödeme yapmak için yüzünüzü doğrulayın", "WCPay_GiveUpReset_Title": "Ödeme şifresi sıfırlama işlemi durdurulsun mu?", "WCPay_NeedChangeCard_Error_Btn": "<PERSON>deme y<PERSON>", "WCPay_TouchID_Auth_Tip": "Ödeme yapmak için mevcut parmak izini doğrulayın", "WCPay_TouchID_Confirm_Alert_Cancel": "İptal", "WCPay_TouchID_Confirm_Alert_Content": "Parmak izi doğrulandı. Ödeme onaylansın mı?", "WCPay_TouchID_Confirm_Alert_OK": "<PERSON><PERSON><PERSON>", "WCPay_TouchID_Confirm_Alert_Title": "Ö<PERSON>", "WeChatPay_Name": "___<BRAND_Pay>___", "Wednesday": "Çarşamba", "Weekly": "Haftalık", "address_item_key": "<PERSON><PERSON>", "address_item_place_holder": "<PERSON><PERSON> girin", "agree": "Kabul et", "agree_user_protocal": "Kullanıcı Anlaşmasını Kabul Et", "agreement_alert": "Lütfen öncelikle \"Kullanıcı Anlaşması\"nı görüntüleyin ve kabul edin.", "alertChargeFee": "<PERSON>z<PERSON>", "area_item_key": "İlçe", "ask_verify_fingerprint": "Parmak izini doğrulayın", "assign_pay_dialog_content": "<PERSON><PERSON><PERSON> kimlik bilgileri, g<PERSON><PERSON><PERSON><PERSON> ___<BRAND>___ hesabına bağlı olandan farklı. Banka kartınıza bağlı olan ___<BRAND>___ hesabını kullanın ve tekrar doğrulayın.", "balance": "Bakiye", "bank_card_item_key": "Banka kartı", "bank_select_item_key": "Banka", "bind_new_card": "Banka kartı ekle", "bind_new_card_section_footer": "Güvenlik nedeniyle şu anda bağlı olan kartların bağlantısı kaldırılacaktır.", "bind_new_card_section_header": "Hesabı geri almak için yeni kart bağlayın", "bind_new_card_to_pay_tip": "Kimliği doğrulamak için ödeme parolasını girin", "bind_new_card_to_reset_mobile_desc": "Yeni bir kart bağlayarak cep telefonu numarası bağlayın", "binded_card_list_page_title": "SMS doğrulama için cep telefonu seçin", "birth_date_item_key": "Doğum günü", "can_not_bind_more_card": "Bağlanan kartlar maksimum miktara ulaştı.", "can_not_get_sms_with_question_mard_word": "Doğrulama kodu gelmedi mi?", "can_not_get_sms_word": "Doğ<PERSON>lama kodu alınmadı", "cancel_time": "İptal zamanı: {}", "cannot_receive_sms_code_content": "Bankanıza kayıtlı olan cep telefonu numaranıza SMS kodu gönderildi. Mevcut durumda bu cep telefonu numarasını kullandığınızdan ve SMS kodunun hiçbir güvenlik uygulaması tarafından engellenmediğinden emin olun. Bu numarayı artık kullanmıyorsanız, bankanıza başvurun. Daha fazla destek için, + 86-0755-95017 numaralı telefondan ___<OfficialEntity_Service>___'ni arayın.", "cannot_receive_sms_code_title": "Doğrulama Kodu Alınamıyor", "card_holder_dialog_content": "1. <PERSON>n<PERSON><PERSON><PERSON><PERSON>, bir ___<BRAND>___ Kimliği sadece aynı ada sahip banka kartlarını bağlayabilir.\n\n2. Başka bir ad üzerine kayıtlı bir kartı bağlamak için gerçek ad bilgilerinizi güncellemeniz gerekir.\n\n3. Gerçek ad bilgilerini değiştirdikten sonra eski kart sahibi bilgileri silinecek ve yalnızca yeni kart sahibi adına kart eklenecektir.", "card_holder_dialog_title": "Kart üzerindeki ad", "card_holder_item_key": "<PERSON><PERSON>", "card_holder_section_header": "Bankanıza önceden sağladığınız bilgileri girin. Gelecekte yalnızca bu adda bulunan banka kartları eklenebilir.", "card_info_section_header": "Kart bilgilerini girin", "card_num_item_key": "<PERSON><PERSON> numa<PERSON>ı", "card_number_input_tips_title": "Hiçbir ücret veya çevrimiçi bankacılık gerekmez", "card_select_item_key": "<PERSON><PERSON> tü<PERSON>", "card_type_section_header": "<PERSON><PERSON> tü<PERSON><PERSON><PERSON><PERSON> se<PERSON>", "change_realname_word": "Gerçek Adı değiştir", "change_to_face_id": "<PERSON><PERSON><PERSON>", "change_to_pwd": "<PERSON><PERSON><PERSON>", "change_to_touch_id": "Parmak İzi kullan", "check_pay_pwd_page_desc": "Kimliğinizi doğrulamak için ödeme parolanızı girin", "check_pay_pwd_page_title": "Kimlik Bilgileri Doğrulaması", "check_sms_desc": "Doğrulama kodu bankanızda kayıtlı olan cep telefonu numarasına gönderildi.\n\n1. Bunun bankanızda kayıtlı olan mevcut cep telefonu numarası olduğunu doğrulayın.\n\n2. Gönderilen SMS'in cep telefonunuzdaki bir güvenlik uygulaması tarafından engellenip engellenmediğini kontrol edin.\n\n3. Bu numaraya şu anda ulaşamıyorsanız lütfen bankanızla irtibata geçin.\n\n4. Daha fazla yardım için 95017 numaralı telefondan Müşteri Hizmetleriyle görüşün.", "check_sms_page_desc": "Banka kartını bağlamak için SMS doğrulaması gerekir. Doğrulama kodu {} numaralı telefona gönderildi. Talimatları izleyin.", "check_sms_page_favor": "Ödeyeceğiniz miktar: {}{:.2f} (Tasarruf edilen miktar: {}{:.2f})", "check_sms_page_title": "Cep telefonu numarasını doğrulayın", "common_back": "<PERSON><PERSON>", "common_cancel_word": "İptal", "common_close": "Ka<PERSON><PERSON>", "common_done_word": "<PERSON><PERSON>", "common_drop_out_word": "Çık", "common_i_know_word": "<PERSON><PERSON><PERSON><PERSON>", "common_more": "<PERSON><PERSON>", "common_next_word": "İleri", "common_question_word": "SSS", "common_select": "Seç", "common_tip_word": "Hatırlatıcı", "confirm_mobile_no": "Cep Telefonunu <PERSON>", "confirm_pay": "Ş<PERSON><PERSON>", "confirm_to_receive": "Alındığını Onayla", "confrim_pay_and_open_deduct_word": "Öde ve Etkinleştir", "confrim_pay_word": "Ö<PERSON>", "coupon_change_should_change_payment": "Ödeme tutarı değişti. Lütfen tekrar seçin.", "coupon_component_need_bank_pay_tips": "Belirtilen Ödeme Yöntemi için indirim", "cre_id_item_key": "Kimlik numarası", "cre_id_item_place_holder": "Kimlik numarasını girin", "cre_type_item_key": "Kimlik türü", "cre_type_item_place_holder": "Kimlik türü seçin", "cvv_dialog_content": "CVV kodu kartınızın arkasında veya önünde bulunan 3 veya 4 haneli güvenlik kodudur.", "cvv_dialog_title": "CVV nedir?", "cvv_item_key": "CVV ", "cvv_item_place_holder": "Genellikle kartın arka yüzünde bulunan 3 veya 4 hane", "default_delay_transfer_confirm_desc": "Transfer {} saat içinde gelecek", "email_item_key": "E-posta adresi", "email_item_place_holder": "E-posta girin", "error_detail_title": "Çözümü Görüntüle", "face_hongbao": "Yakındaki Paketler", "fast_bind_card_support_bank_title_text": "Şu anda aşağıdaki bankaları desteklemektedir", "fill_card_and_user_info_title": "Banka kartı ve kimlik bilgilerini girin", "fill_card_info_card_holder_assign_pay_header": "<PERSON><PERSON>me ya<PERSON> i<PERSON>in be<PERSON>ş bir kart sahibi olmalıdır.", "fill_card_info_page_favor_desc": "İlave {}{:.2f} tasarruf etmek için bu banka kartı türünü kullanın.", "fill_card_info_page_realname_cre_not_support": "Bu kartı bağlamak için {} kullanılamaz", "fill_card_info_page_title": "Kart bilgilerini girin", "fill_card_num_format_error": "Geçersiz bir kart numarası", "fill_card_num_of_card_holder_section_header": "Kart sahibinin banka kartı numarasını girin", "fill_card_num_page_desc": "Banka kartı bağla", "fill_card_num_page_favor_dialog_title": "Bu Kartla Daha Fazlasını Kaydet", "fill_card_num_page_realname_desc": "Gerçek ad doğrulama işlemini tamamlamak için banka kartı eklemelisiniz.", "fill_card_num_page_sns_input_hint": "Yalnızca banka kartları desteklenir", "fill_card_num_page_title": "Banka kartı ekle", "fill_card_number_assign_pay": "Ödeme yapmak için {} adlı kişiye ait banka kartını kullanın", "fill_card_number_more_favor": "İndirimlerden yararlanmak için belirlenen banka kartını kullanın", "fill_complete_name": "<PERSON> adı girin", "fill_id_format_error": "Kimlik numarası biçimi yanlış.", "fill_in_sms_key": "Doğrulama kodu", "fill_in_sms_word": "<PERSON><PERSON><PERSON><PERSON>a kodunu girin", "fill_phone_num_format_error": "Cep telefonu numarası biçimi yanlış.", "finger_print_err_tips": "<PERSON><PERSON><PERSON> dene", "first_name_item_key": "İlk ad", "first_name_item_place_holder": "Adınızı girin", "float_paydesk_modal_no_select_favor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "foreign_mobile_header": "Yeni cep telefonunu girin", "forget_pay_pwd_title": "Ödeme Parolasını Unuttum", "get_sms_with_count_down_word": "Doğ<PERSON>lama kodunu al \n({})", "get_sms_word": "Doğrulama kodunu al", "give_up_on_new_card": "Kart ba<PERSON><PERSON>a işlemi durdurulsun mu?", "give_up_this_order_or_not": "Bu işlemden vazgeçilsin mi?", "group_aa": "Hesabı Bölme", "has_send_sms": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "has_send_sms_with_count": "<PERSON><PERSON><PERSON><PERSON><PERSON> ({})", "hongbao_refund_way_header_title": "Gönderdikten sonra 24 saat içinde açılmayan Kırmızı Zarflar aşağıdaki yöntem ile iade edilecektir.", "hongbao_refund_way_title": "Kırmızı Zarf İadesi", "id_card_name": "Kimlik kartı", "install_cert_error": "Sertifika yükleme başarısız oldu", "last_name_item_key": "Soyadı", "last_name_item_place_holder": "Soyadını girin", "loading_title": "Yükleniyor...", "lottery_network_error": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bir daha ki sefere iyi şanslar dileriz!", "lqt": "Mini Para", "lqt_reset_mobile_desc": "Bir banka kartı seçin. Kartın bankasında bulunan cep telefonu numaranızı kullanarak Mini Paranızı doğrulayın.", "mobile_dialog_content": "Banka hesabınıza kayıtlı cep telefonu numarası, hesabı açarken bankaya vermiş olduğunuz numaradır. Eğer bankaya bir cep telefonu numarası vermediyseniz, numarayı unuttuysanız ya da numara artık kullanılmıyorsa, bankanıza tekrar başvurarak cep telefonu numaranızı güncelleyin.", "mobile_dialog_title": "Cep telefonu numaraları", "mobile_item_key": "Cep telefonu numa<PERSON>ı", "mobile_item_place_holder": "Bankada bulunan cep telefonu numarasını girin", "name_item_key": "Ad", "name_item_place_holder": "Kart üzerindeki adı girin", "nationality_item_key": "Ülke/Bölge", "nationality_place_holder": "<PERSON><PERSON><PERSON>/bölge girin", "new_mobile_item_key": "Yeni cep telefonu numa<PERSON>ı", "new_mobile_item_place_holder": "Bankada bulunan cep telefonu numarasını girin", "new_user_card_num_input_safety_desc": "___<BRAND>___ hesap sahibine ait bir kartı ba<PERSON>layın", "new_user_card_num_input_safety_desc_v2": "___<BRAND>___ hesap sahibine ait bir kartı ba<PERSON>layın", "no": "Hay<PERSON><PERSON>", "offline_choose_payment": "Varsayılan Ödeme Yöntemini Seçin", "offline_choose_payment_fail": "Varsayılan ödeme yöntemi başar<PERSON><PERSON><PERSON>z <PERSON>, ödemeyi tamamlamak için diğer yöntemleri deneyin.", "offline_click_view_code": "Ödeme kodu numarasını görüntülemek için dokunun", "offline_pay_modify_limit": "Tutarı Düzenle", "offline_pay_only_pay_title": "<PERSON><PERSON><PERSON> yap", "offline_pay_select_card_invalid": "Mevcut durumda seçilen ödeme yöntemi kullanılamıyor. Başka bir ödeme yöntemi deneyin.", "offline_pay_title": "Para", "offline_pay_to_merchant": "Satıcıya Öde", "offline_prefer_payment": "Varsayılan ödeme yöntemi", "offline_show_code_warning": "Ödeme yaparken bankaları görmek için yalnızca ödeme kodu numarasını kullanın. Güvenliğiniz için başkalarıyla paylaşmayın.", "offline_view_code_warning": "Ödeme yaparken bankaları görmek için yalnızca ödeme kodu numarasını kullanın. Güvenliğiniz için başkalarıyla paylaşmayın.", "ok": "<PERSON><PERSON>", "order_address_section_header": "<PERSON><PERSON> ad<PERSON>i", "pay_card_detail_contact_user_info": "Müşteri Hizmetleriyle İletişime Geç: ", "pay_card_detail_tel_number": "95017", "pay_power_by_tenpay_word": "Bu hizmet Tenpay tarafından sağlanmaktadır", "pay_success": "Ödeme başarılı", "paydesk_coupon_page_title": "Ödeme İndirimleri Sayfası", "paydesk_float_page_title": "<PERSON><PERSON><PERSON>", "paydesk_main_page_more_favor": "<PERSON><PERSON> fazla indirim", "paydesk_main_page_title": "Ödeme <PERSON>", "paydesk_payment_page_title": "Ödeme Ka<PERSON>ı <PERSON>esi", "paydesk_sub_page_title": "Ödeme Alt Sayfası", "payee_remark_title": "Ödeme Yapan A<PERSON>ı<PERSON>maları", "paying_alert_tips": "Ödeme zaten gönderildi. Tekrar göndermeniz gerekip gerekmediğini kontrol etmek için ödeme sonucu mesajını bekleyin.", "payment_method": "<PERSON><PERSON><PERSON>", "phone_number_item_key": "Telefon", "phone_number_item_place_holder": "Telefon numarasını girin", "profession_item_key": "Meslek", "pure_bind_card_succ_tips": "Başarıyla bağlandı", "pwd_repeat_error_tip": "<PERSON><PERSON><PERSON><PERSON>şmi<PERSON>r", "rebind_bank_card_section_header": "Hesabınızı geri almak için kartı tekrar bağlayın", "receipt": "Para Al", "receive_done": "Alındı", "receive_remark_title": "Alındı Açıklamaları", "receive_time": "<PERSON>ın<PERSON> zaman<PERSON>: {}", "receiver_remark_title": "Alacaklı Açıklamaları", "refund_doing_tip": "Geri ödeme işleniyor. <PERSON><PERSON>, 1-3 iş günü içinde kartınıza geri ödenecektir.", "refund_done": "<PERSON><PERSON>", "refund_done_and_expired": "<PERSON><PERSON> (süresi doldu)", "refund_time": "<PERSON><PERSON>il<PERSON> z<PERSON>: {}", "refund_transfer_or_not": "{} adlı kişiden gelen transfer geri gönderilsin mi?", "refund_word": "<PERSON><PERSON>", "renewal_time_item_key": "<PERSON><PERSON>", "resend_message_or_not": "Bu mesaj yeniden gönderilsin mi?", "resend_sms": "<PERSON><PERSON><PERSON>", "resend_sms_with_count": "<PERSON><PERSON><PERSON> ({})", "resend_success_tip": "<PERSON><PERSON> te<PERSON>", "resend_word": "<PERSON><PERSON><PERSON>", "reset_ban_mobile_fill_card_info_credit_tip_header": "Kontrol için banka kartı bilgilerini girin", "reset_ban_mobile_fill_card_num_tip_header": "Yeni bir banka kartı ekleyin ve Bakiye ödemelerini doğrulamak için bankanızda bulunan cep telefonu numarasını kullanın.", "reset_cvv_and_valid_date_tip": "Bağlı kart bilgilerini güncelliyor ve aynı zamanda bir ödeme yapıyorsunuz. Yaptığınız işlemden emin değilseniz bankanızın müşteri hizmetleriyle görüşün: ", "reset_cvv_title": "CVV'y<PERSON>", "reset_lqt_mobile_fill_card_num_tip_header": "Yeni bir kart ekleyin ve Mini Para için SMS doğrulamalarını tamamlamak üzere kartın kayıtlı cep telefonu numarasını kullanın.", "reset_mobile_bank_card_number": "Kart", "reset_mobile_card_desc_format": "{}{} ({}) kayıtlı cep telefonu numarası", "reset_mobile_card_desc_with_update_format": "{}{} ({}) kayıtlı cep telefonu numarası. ", "reset_mobile_new_mobile_info_btn": "Ayrıntılar", "reset_mobile_new_mobile_number": "Yeni Cep Telefonu", "reset_mobile_phone_page_title": "Cep telefonu numarasını düzenle", "reset_phone_tip": "Kimlik doğrulandıktan sonra ödeme yapabilirsiniz. Bankanızda bulunan telefon numarasını doğrulamak için şu numarayı arayın: ", "reset_pwd_fill_rebind_card_info_page_title": "Banka kartı bilgilerini girin", "reward": "<PERSON><PERSON><PERSON><PERSON>", "safety_dialog_content": "Güvenlik önlemleri: <PERSON><PERSON><PERSON> korum<PERSON>, ger<PERSON><PERSON> zamanlı izleme, acil durum dondurma işlemi. \n\nİki adımlı doğrulama: Her bir ödeme için ödeme parolanız gereklidir. Büyük ödemeler için SMS doğrulaması gereklidir. \n\nGizlilik koruması: Kullanıcı verilerini korumak için güçlü veri şifrelemesi kullanılır. \n\nÖdeme sigortası: Ödemeler PICC tarafından sigortalıdır.", "safety_dialog_title": "Güvenlik önlemleri", "scan_card_num_title": "Kartı Tara", "select_payment": "<PERSON><PERSON><PERSON>", "select_payment_card": "Ödeme Yöntemi Seç", "send_verify_code_btn_wording": "<PERSON><PERSON><PERSON>", "send_verify_code_switch_btn_wording": "Doğrulama yöntemini <PERSON>r", "send_verify_code_tips_format": "SMS doğrulama kodu şuraya gönderilecek: \n{}", "set_pay_pwd_confirm_page_title": "Doğrulamak için tekrar girin", "set_pay_pwd_page_desc": "Ödemelerinizi doğrulamak için bir ___<BRAND_Pay>___ parola ayarlayın", "set_pay_pwd_page_title": "Ödeme Parolası Belirle", "set_pwd_success": "<PERSON><PERSON><PERSON>", "succ_page_open_biometric_cancel_btn_title": "<PERSON><PERSON><PERSON>", "succ_page_open_biometric_dialog_content": "Ödemeleri daha hızlı yapmak için yüz veya parmak iziyle <PERSON>meyi etkinleştirebilirsiniz.", "succ_page_open_biometric_faceid_btn_title": "<PERSON><PERSON><PERSON>", "succ_page_open_biometric_touchid_btn_title": "Parmak <PERSON>zi<PERSON>", "succ_page_open_face_id_dialog_content": "Ödemeleri hızlı ve güvenli bir şekilde tamamlamak amacıyla yüz tanımayı kullanmak için Face Pay'i etkinleştirin.", "succ_page_open_face_id_right_btn_title": "Face Pay'i etkinleştir", "succ_page_open_touch_id_dialog_content": "Ödemeleri hızlı ve güvenli bir şekilde tamamlamak amacıyla parmak izi tanımayı kullanmak için Touch Pay'i etkinleştirin.", "succ_page_open_touch_id_left_btn_title": "<PERSON><PERSON>", "succ_page_open_touch_id_right_btn_title": "Touch Pay'i etkinleştir", "to_be_confirm_receive": "Alındı doğrulanmadı", "transfer": "Transfer et", "transfer_account": "Transfer tutarı", "transfer_amount_input_invalid_hint": "<PERSON><PERSON><PERSON> tutar yanlı<PERSON>", "transfer_bank": "Banka Kartına Transfer", "transfer_explain": "Transfer notu ekle", "transfer_modify_explain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transfer_second_left_button": "İptal", "transfer_second_right_button": "<PERSON><PERSON>", "transfer_second_title": "Transfer hatırlatıcısı", "transfer_time": "Transfer zamanı: {}", "transfer_ui_title": "Arkadaşa Transfer Et", "understand_safety": "Güvenlik önlemleri", "update_word": "<PERSON><PERSON><PERSON><PERSON>", "user_card_type_select_placeholder_v2": "Banka kartı ve kart türünü seçin", "user_info_section_header": "<PERSON><PERSON>isel bilgileri girin", "user_protocal": "\"Kullanıcı Anlaşması\"", "valid_date_item_key": "<PERSON> kullanma tarihi", "verify_cre_tip": "<PERSON><PERSON> doğrulaması için {} - {} son 4 hanesini girin", "verify_fingerprint_fail": "Parmak izi doğrulaması başarısız oldu", "verify_id_ui_true_name_tips": "{} (<PERSON> is<PERSON> girin)", "wechat_bank_agreement": "Banka Sözleşmesi", "wechat_mobile_phone_word": "Cep telefonu numarası ___<BRAND>___'e ba<PERSON><PERSON>ı", "wechat_user_agreement": "___<BRAND_Pay>___ Kullanıcı Sözleşmesi", "wxp_common_cancel": "İptal", "wxp_common_confirm": "<PERSON><PERSON>", "wxp_common_i_know": "<PERSON><PERSON><PERSON><PERSON>", "wxp_common_remind": "Hatırlatıcı", "wxp_network_error": "Sistem meşgul. <PERSON><PERSON> sonra tekrar deneyin.", "wxp_payment_network_error": "İşlem gönderildi. ___<BRAND_Pay>___ <PERSON><PERSON><PERSON>n ödeme durum bildirimi alacaksınız. Tekrar ödeme işlemi yapmadan önce ödeme durumunu doğrulayın.", "wxp_system_error": "Sistem meşgul. <PERSON><PERSON> sonra tekrar deneyin.", "wxp_wcpay_system_error": "Sistem meşgul. <PERSON><PERSON> sonra tekrar deneyin.", "yes": "<PERSON><PERSON>", "zip_item_key": "Posta kodu", "zip_item_place_holder": "<PERSON>a kodunu girin", "common_confirm_word": "<PERSON><PERSON><PERSON>", "ModifyPwdUseCase_ModifyPwd_GiveUp_Yes": "Değişiklikleri Kaldır", "ModifyPwdUseCase_ModifyPwd_GiveUp_No": "<PERSON><PERSON>", "disagree": "Kabul Etmiyorum", "card_user_agreement": "Kullanıcı Hizmet Sözleşmesi", "card_bank_agreement": "Banka Sözleşmesi", "Card_UserAgreement_Title": "Banka kartı eklemek için aşağıdaki sözleşmeyi kabul etmeniz gerekir.", "pay_settings_delay_transfer_page_title": "Transfer Zamanı", "pay_settings_delay_transfer_page_desc": "Kabul edildikten sonra, fonlar aşağıdaki saate kadar diğer kullanıcının Bakiyesine yatırılacaktır. Transfer yapıldıktan sonra geri alınamaz. Bu nedenle transferden önce alacaklının bilgilerini dikkatlice kontrol edin.", "pay_settings_delay_transfer_no_delay": "<PERSON><PERSON>", "pay_settings_delay_transfer_two_hour": "2 saat içinde", "pay_settings_delay_transfer_one_day": "24 saat içinde", "pay_settings_biometric_pay_enabled": "<PERSON><PERSON><PERSON>", "pay_settings_biometric_pay_disabled": "Devre Dışı", "pay_settings_biometric_pay_multi_support_title": "<PERSON><PERSON><PERSON>/Parmak İziyle <PERSON>", "pay_settings_biometric_pay_faceid_enabled": "<PERSON><PERSON><PERSON> ödeme etkin<PERSON>ştirildi", "pay_settings_biometric_pay_touchid_enabled": "Parmak iziyle ö<PERSON>me <PERSON>ştirildi", "pay_settings_biometric_pay_multi_support_desc": "Etkinleştirdikten sonra, ödemeleri daha hızlı yapmak için yüz veya parmak izi doğrulamasını kullanabilirsiniz.", "f2f_pay_extrabuy_detail_modal_original_price": "(Eski fiyat {:.2f} ¥)", "common_button": "Buton", "Accessibility_Type_SwitchView_Selected": "{}, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Accessibility_Type_SwitchView_UnSelected": "{}, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, devre dışı bırak", "YunShanFu_Loading_Wording": "QuickPass açılıyor...", "YunShanFu_Uninstalled_Error": "Henüz QuickPass yüklemediniz. Lütfen önce kurun ve tekrar deneyin veya ___<BRAND_Pay>___ ile ödemeye devam edin.", "OfflinePay_Setting_CloseOfflinePay_AlertMessage_ShowFeaturePassword": "Güvenlik kilidi <PERSON>, Quick Pay'e erişmek için seçili kilit açma yöntemi gerekir ve ödemelerinizin güvende olmasına yardımcı olur. <PERSON>re dışı bırakılsın mı?", "OfflinePay_Setting_CloseOfflinePay_AlertMessage": "Quick Pay devre dışı bırakılsın mı?", "OfflinePay_Setting_CloseOfflinePay_BtnTitle": "<PERSON><PERSON> dışı bırak", "OfflinePay_Setting_CloseOfflinePay_Cancel": "<PERSON><PERSON>", "OfflinePay_Setting_CloseOfflinePay_OpenWalletLock": "Güvenlik Kilidi Ayarla", "Wallet_Mix_Paid_UnKnown_Error": "İşlem isteği gö<PERSON>ildi. ___<BRAND_Pay>___ Hong Kong Resmi He<PERSON>ından bir durum bildirimi alacaksınız. Ödeme durumu onaylanana kadar tekrar ödeme işlemi yapmayın.", "bank_card_info": "Not", "HHC_Did_Add": "Eklendi", "VoiceOver_OfflinePay_barCode": "Ödeme Barkodu kasiyere gösterilebilir. Ödeme kodunu tam ekranda göstermek için iki kez dokunun", "VoiceOver_OfflinePay_barCode_short": "<PERSON><PERSON><PERSON>", "VoiceOver_OfflinePay_Qrcode": "Ödeme QR Kodu", "VoiceOver_OfflinePay_barcode_clickHint": "<PERSON><PERSON> dönmek için iki kez dokunun", "VoiceOver_OfflinePay_Qrcode_clickHint": "Tam ekranda göstermek için iki kez dokunun", "common_link": "Bağlantı", "Accessibility_Collapse_Header_Collapsed": "{}, kapatıldı", "Accessibility_Collapse_Header_Showed": "{}, genişletildi", "Pay_Android_Fingerprint_Prompt_Title": "Parmak izini doğrulayın", "Pay_Android_Fingerprint_Prompt_SubTitle": "ödemeyi tamamlamak için.", "Pay_Android_Fingerprint_Prompt_Button": "<PERSON><PERSON><PERSON>", "Accessibility_Collapse_Header_Collapsed({}": "<PERSON><PERSON>", "Accessibility_Collapse_Header_Showed({}": "Tümü Gösteriliyor", "Accessibility_State_Disabled": "<PERSON><PERSON>", "Accessibility_String_PwdInputView": "Password box, 6 digits in total, {} digits entered", "Accessibility_Type_Button": "{}, button", "Accessibility_Type_CheckBox_Selected": "Check box, selected, {}, button", "Accessibility_Type_CheckBox_UnSelected": "Check box, unselected, {}, button", "Accessibility_Type_Selected": "Selected, {}, button", "Accessibility_Type_UnSelected": "Unselected, {}, button", "common_check_box_check": "Check box, selected", "common_check_box_uncheck": "Check box, unselected", "OfflinePay_ChangePayment_UnConnectedNetwork_Tips": "Ağ kullanılamıyor. Ödeme yöntemi seçilemiyor.", "OfflinePay_EnablePage_UnConnectedNetwork_Tips": "<PERSON><PERSON>anılamıyor. <PERSON><PERSON> sonra tekrar deney<PERSON>.", "Fetch_Balance_To_Bank": "Çekildi", "Fetch_Balance_Amount": "Para çekme tutarı", "Fetch_Balance_Amount_Tips": "Bakiye: {}¥.", "Fetch_Balance_Amount_Exceed": "<PERSON><PERSON><PERSON> tutar mevcut Bakiyeyi aşıyor", "Fetch_Balance_Fetch_All": "Tümünü Çek", "HoneyPay_CheckPwd_Unbind_Title": "Ebeveyn Kartının Bağlantısını Kaldır", "HoneyPay_Modify_CreditLimit_Title": "Aylık limiti düzenle", "HoneyPay_Modify_CreditLimit_Desc": "Aylık limit", "HoneyPay_Modify_CreditLimit_Max_Alert": "Tutar {:.2f}¥ değ<PERSON>ni aşamaz", "balance_entry_balnce_title": "Bakiyem", "balance_entry_balnce_detail": "İşlemler", "balance_entry_powered_by_tenpay": "Bu hizmet Tenpay tarafından sağlanmaktadır", "balance_recharge_page_title": "Bakiye <PERSON>", "balance_recharge_card_info_title": "<PERSON><PERSON><PERSON><PERSON>", "balance_recharge_payment_new_card": "<PERSON><PERSON>", "HoneyPay_Add_Card": "Bir Ebeveyn Kartı hediye edin", "HoneyPay_Select_Contact_Title": "Kiş<PERSON><PERSON>", "HoneyPay_Modify_Comment": "<PERSON><PERSON><PERSON>", "HoneyPay_MoneyInput_Hint": "Tu<PERSON><PERSON> girin", "HoneyPay_CreateCard_Btn": "<PERSON><PERSON><PERSON>", "HoneyPay_Max_Amount_Notice": "Tutar {:.2f}¥ değ<PERSON>ni aşamaz", "HoneyPay_Modify_Credit": "Aylık limit", "HoneyPay_Main_Title": "Ebeveyn Ka<PERSON>ı", "hbrefund_info_tips": "Açıklamalar", "hbrefund_set_button": "<PERSON><PERSON><PERSON>", "hbrefund_time_title": "Kırmızı Zarf İade Süresi", "hbrefund_forbid_way": "Bu geri ödeme yöntemi artık desteklenmiyor.", "hbrefund_had_set": "Başarıyla ayarlandı", "hbrefund_origin_desc": "24 saat içinde açılmayan Kırmızı Zarflar orijinal yöntem ile iade edilecektir.", "hbrefund_set_tips": "<PERSON><PERSON> yapıldıktan sonra kabul edilmeyen para orijinal ödeme yöntemine iade edilecektir. Bu seçenek \"Bakiyeye İade\" olarak değiştirilemez. Devam edilsin mi?", "TeenagerPayDetailUIPage_NotSet": "Yok", "TeenagerPayDetailUIPage_LimitedAmount": "{:.{}f}¥", "TeenagerPaySetLimitModal_AmountTitle": "<PERSON><PERSON>", "TeenagerPaySetLimitModal_MaxAmount": "7 basamağa kadar izin verilir", "TeenagerPayGetDetailUseCase_LimitOn": "<PERSON><PERSON><PERSON>", "TeenagerPayGetDetailUseCase_LimitOff": "Limit yok", "TeenagerPayUseCase_Set_Ok": "Başarıyla ayarlandı", "TeenagerPayUseCase_Close_Ok": "<PERSON>tar sınırı devre dışı", "TeenagerPayUseCase_Limit_Max": "İşlem ba<PERSON><PERSON>na <PERSON>, günlük ödeme limitinden fazla olamaz.", "TeenagerPayUseCase_Limit_Min": "Günlük ödeme limiti, işlem başına ödeme limitinden az olamaz.", "Dcep_Loading_Wording": "Yükleniyor...", "Dcep_Uninstalled_Error": "You have not installed E-CNY. Please install it and try again, or continue payment with ___<BRAND_Pay>___", "TeenagerPayUseCase_Input_Accesibility": "<PERSON><PERSON>", "bankcard_detail": "{} Kuyruk numarası {}", "bankcard_qmf_detail": "{} <PERSON><PERSON><PERSON><PERSON>, {}", "FaceCheck_Agreement_title": "<PERSON><PERSON><PERSON>", "FaceCheck_Success_title": "Doğrulandı", "FaceCheck_Result_Retry": "<PERSON><PERSON><PERSON> dene", "TabBar_NewBadge": "<PERSON><PERSON>", "common_delete_alert_title": "Silme işlemi on<PERSON>ın mı?", "common_delete": "Sil", "transfer_to_bank_name_input_placeholder": "Alacaklı Adı", "transfer_to_bank_card_input_placeholder": "Alacaklı Banka Kartı No.", "transfer_to_bank_bank_select_placeholder": "Banka seçin", "transfer_to_bank_arrival_time_select_title": "Saat", "transfer_to_bank_arrival_time_modal_title": "Transfer Zamanını Seç", "transfer_to_bank_arrival_time_modal_desc": "Havale talebinde bulund<PERSON>tan sonra, fonlar takip eden zamana kadar alacaklının hesabına yatırılacaktır.", "transfer_to_bank_history_page_title": "Alacaklı Seç", "transfer_to_bank_history_page_empty_prompt": "Önceki alacaklılar mevcut değil", "transfer_to_bank_history_me_section_title": "<PERSON>", "transfer_to_bank_history_others_section_title": "Önceki Alacaklılar", "transfer_to_bank_history_modify_remark_action": "Not", "transfer_to_bank_history_set_remark_title": "<PERSON>ir <PERSON>", "transfer_to_bank_history_delete_action": "Sil", "transfer_to_bank_bank_unavailable_alert": "Bankada bakım çalışması yapılmaktadır. Transferler şu anda yapı<PERSON>.", "transfer_to_bank_money_input_title": "<PERSON><PERSON>", "transfer_to_bank_info_receiver_format": "Alacaklı: {}", "transfer_to_bank_info_charge_fee": "<PERSON>z<PERSON>", "transfer_to_bank_info_charge_fee_rate_format": "(oran: {:.2f})", "transfer_to_bank_info_total_amount": "Toplam", "transfer_to_bank_info_transfer_explain": "<PERSON><PERSON>ı<PERSON><PERSON>", "transfer_to_bank_info_transfer_explain_edit_hint_format": "<PERSON><PERSON> ödeme yapan hem de alacaklı tarafından görülebilir. Maksimum {} karakter.", "transfer_to_bank_info_add_transfer_explain": "<PERSON>ir <PERSON>", "transfer_to_bank_info_detail_title": "Ayrıntılar", "transfer_to_bank_info_detail_current_state": "Durum", "transfer_to_bank_info_detail_paid_success": "bir aktar<PERSON>m ba<PERSON><PERSON>ı", "transfer_to_bank_info_detail_withdrawn_success": "Para çekme işlemi tamamlandı", "HoneyPay_PrepareCardUI_Title": "Ebeveyn Kartını Ayarla", "none": "Yok", "mobile_item_key_bank": "Bankaya kayıtlı cep telefonu numarası", "mobile_item_place_holder_short": "Cep telefonu numarasını girin", "FillCard_Number_Default_Mobile_Modify_Tips_New": "Son kez ba<PERSON><PERSON>n cep telefonu numarası otomatik olarak dolduruldu. Gerektiği gibi değiştirebilirsiniz.", "HoneyPay_MoneyInput_Hint_New": "Tu<PERSON><PERSON> girin", "AddPayCard_No_Card_Bind_Card_Title": "<PERSON><PERSON> numa<PERSON> girmeden ba<PERSON>la", "AddPayCard_Manual_Bind_Card_Title": "<PERSON><PERSON> numa<PERSON> g<PERSON> ba<PERSON>", "FillCard_Number_Reg_Hint_V3": "{} banka kartı numarasını girin", "FastBindCardSelectBankUIV2_Title": "<PERSON><PERSON> numa<PERSON> girmeden ba<PERSON>a", "FastBindCardSelectBankUIV2_Search_Hint": "{} banka ara", "qrcode_collection_settings": "<PERSON>", "qrcode_collection_amount": "<PERSON><PERSON>", "qrcode_collection_remark": "Para Alma Talimatları", "OfflinePay_Banner_Use_Tips": "<PERSON><PERSON><PERSON>", "OfflinePay_Banner_Expand_Tips": "Genişlet", "OfflinePay_Banner_Collapse_Tips": "<PERSON><PERSON><PERSON>", "OfflinePay_Close_WalletLock_HalfDialog_Title": "Ödeme Kodunu Devre Dışı Bırak", "OfflinePay_Close_WalletLock_HalfDialog_Content": "Ödeme kodunu kullanırken güvenlik için güvenlik kilidi ayarlayabilirsiniz. Ayarladı<PERSON><PERSON> son<PERSON>, ödeme kodunu kullandığınızda güvenlik doğrulaması gerekecektir.", "FillCardNumberV2_CountryCode_Hint": "<PERSON><PERSON><PERSON>/bölge kodunu girin", "FillCardNumberV2_CountryCodeView_Hint": "Lütfen Seçin", "paydesk_main_page_not_use_favor": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "paysecurity_digital_cert_not_install": "<PERSON><PERSON><PERSON>", "WCPay_Digital_Cert_Desc_Not_Install": "Dijital sertif<PERSON>", "WCPay_Digital_Cert_Desc_Already_Install": "Dijit<PERSON> sertifika etkin", "WCPay_Digital_Cert_Manage_Content_Desc": "Cihazınızda dijital sertifika etkinleştiriliyor:", "WCPay_Digital_Cert_Manage_Content_Desc_1": "• Cihazınızdan çok daha güvenli ödemeler yapar", "WCPay_Digital_Cert_Manage_Content_Desc_2": "• Bakiye kullanılarak günlük ödeme limitini yükseltir", "WCPay_Digital_Cert_Install_Button_Title": "Etkinleştir", "WCPay_Digital_Cert_Delete_Button_Title": "<PERSON><PERSON> dışı bırak", "WCPay_Digital_Cert_Install_List_desc": "Sertifika yüklenmiş olan cihazlar", "WCPay_Digital_Cert_Delete_Confirm_Content": "Cihazdaki mevcut __<BRAND_ID>___ için dijital sertifikayı devre dışı bırakmak istediğinizden emin misiniz?", "WCPay_Digital_Delete_Confirm_Btn_Title": "<PERSON><PERSON> dışı bırak", "WCPay_Digital_Cert_Install_Action_Title": "Kimliği doğrula", "WCPay_Digital_Cert_Install_Action_Desc": "Sertifika yüklenmeden önce kimlik doğrulaması gerekir", "WCPay_Digital_Cert_Install_Input_Title_default": "Kimlik Kartı", "WCPay_Digital_Cert_Install_Input_Desc_default": "Kimlik numarasını girin", "WCPay_Digital_Cert_Install_Input_Desc": "{} kimlik numarasını girin", "WCPay_Digital_Cert_Verify_Button_Title": "<PERSON><PERSON><PERSON><PERSON>", "WCPay_Digital_Cert_Install_Sccuess": "Doğrulandı", "WCPay_Digital_Cert_Delete_Succ_Toast": "Devre Dışı", "LQT_Purchase_Page_Title": "İçe Transfer", "LQT_Purchase_Card_Info_Title": "Transfer Yöntemi", "LQT_MonetInputOutOfRange_Tips": "Bakiye yetersiz. Kontör y<PERSON> tekrar den<PERSON>.", "LQT_Limit_Cashier_Modal_Balance_Desc": "Bakiyed<PERSON><PERSON>", "LQT_Limit_Cashier_Modal_LQT_Desc": "Mini Para", "LQT_Limit_Cashier_Modal_Transfer_In_Tips": "İçe Transfer", "LQT_Limit_Cashier_Modal_Confirm_Button_Title": "<PERSON><PERSON><PERSON><PERSON>", "LQT_SaveAmountLargeThanBankAvaible_Tips": "Girdiğiniz tutar banka ödeme limitini aşıyor", "LQT_Redeem_Card_Info_Title": "Ş<PERSON><PERSON>", "LQT_Redeem_Page_Title": "Para Çek", "LQT_Redeem_Confirm_View_Desc": "Para Çek", "LQT_Redeem_Balance_Amount": "Mini Fon Bakiyesi {:.2f}¥", "LQT_Redeem_Balance_Insufficient": "Mini Fon Bakiyesi Yetersiz", "LQT_Redeem_Balance_Fetch_All": "Tümünü Çek", "LQT_Loading_Card_Data": "Banka kartı listesi alınıyor", "LQT_Loading_LQT_Amount": "Mini Fon bakiyesi alınıyor", "LQT_Loading_LQ_Amount": "Bakiyeden tutar alınıyor", "LQT_PerRedeem_Invalid_Default_Tips": "Tek işlem tutarı, limiti aşıyor", "LQT_Redeem_Fetch_Amount_Large_Than_Bank_Avaible": "İşlem başına maksimum tutar {:.2f}¥ değerindedir. Birden fazla işleme bölebilirsiniz.", "LQT_Redeem_Fetch_Amount_Large_Than_Max_Amount_Tips": "<PERSON>ha fazla bilgi alın", "HoneyPay_Record_Receive_Title": "<PERSON><PERSON>", "HoneyPay_Record_Donate_Title": "<PERSON><PERSON> Ebeveyn Ka<PERSON>ı", "LQTDetail_balance_Accessibility": "Bakiye {:.2f}¥", "WCPay_LQT_OnClickPurchase_Fail_Network_Error": "<PERSON><PERSON> hatası. Banka kartı listeniz alınamadı. <PERSON>ha sonra tekrar deneyin.", "WCPay_LQT_OnClickRedeem_Fail_Network_Error": "<PERSON><PERSON> hatası. Banka kartı listeniz alınamadı. <PERSON>ha sonra tekrar deneyin.", "WCPay_LQT_PurchaseFund_Fail_Network_Error": "<PERSON><PERSON><PERSON>n alı<PERSON>. <PERSON><PERSON> sonra tekrar deneyin.", "WCPay_LQT_QryPurchaseResult_Fail_Network_Error": "Satın alma sonucu kontrol edilemiyor", "WCPay_LQT_PreRedeemFund_Fail_Network_Error": "İtfa emri veril<PERSON>. <PERSON>ha sonra tekrar den<PERSON>.", "WCPay_LQT_RedeemFund_Fail_Network_Error": "Para kullanılamıyor. <PERSON><PERSON> sonra tekrar deneyin.", "WCPay_LQT_MoneyVC_Save_TextFieldTips": "Transfer Tutarı", "WCPay_LQT_MoneyVC_Fetch_TextFieldTips": "<PERSON><PERSON>", "LQT_Purchase_Keyboard_Confirm_Title": "<PERSON><PERSON><PERSON><PERSON>", "LQT_Redeem_Keyboard_Confirm_Title": "Para Çek", "Wallet_Lock_Default_Title": "Güvenlik Kilidi", "Wallet_Lock_FaceLock": "<PERSON><PERSON><PERSON> kilit açma", "Wallet_Lock_TouchLock": "Parmak iziyle kilit a<PERSON>ma", "Wallet_Lock_BioLock": "Yüzle/Parmak İziyle kilit açma", "Wallet_Lock_PatternLock": "<PERSON><PERSON> parolasıyla kilit açma", "Wallet_Lock_PatternLock_Modify_Verify_Title": "Eski desen parolasını girin", "Wallet_Lock_PatternLock_Modify": "Desen parolasını değiştir", "Wallet_Lock_PatternLock_Modify_SubTltle": "Yeni bir model parolası belirleyin", "Wallet_Lock_Close_Tips": "Güvenlik kilidi devre dışı bırak<PERSON>ld<PERSON>ğ<PERSON>, \"<PERSON>\" > \"Hizmetler\" bölümüne erişmek için kilit açma yöntemi gerekmez.", "Wallet_Lock_TouchLock_Verify_Title": "Devam etmek için Touch ID'yi doğrulayın", "Wallet_Lock_FaceLock_Verify_Title": "<PERSON><PERSON> etmek için Face ID'yi do<PERSON>n", "Wallet_Lock_PatternLock_Verify_Title": "<PERSON>en parolasını girin", "Wallet_Lock_Verify_byPwd": "Ödeme parolasını doğrulama", "Wallet_Lock_Verify_Btn_FaceID": "Yüzü doğrulayın", "Wallet_Lock_Verify_Btn_TouchID": "Parmak izini doğrulayın", "Wallet_Lock_PatternLock_Setup_Title": "Bir desen parolası ayarlayın", "Wallet_Lock_PatternLock_Reset_Title": "Desen Parolasını mı Unuttunuz?", "Wallet_Lock_PatternLock_Confirm_Title": "<PERSON><PERSON> i<PERSON> tekrar girin", "Wallet_Lock_PatternLock_Confirm_Error_Tips": "Parola tutarsız. Tekrar ayarlayın.", "Wallet_Lock_PatternLock_Verify_Error_Tips": "Yanlış model. {} hakkı<PERSON><PERSON>z kaldı.", "Wallet_Lock_PatternLock_Verify_Limit_Tips": "Çok fazla deneme yapıldı. {} dakika sonra tekrar deneyin.", "Wallet_Lock_PatternLock_Modify_AfterVerify_Title": "Yeni bir model parolası belirleyin", "Wallet_Lock_PatternLock_InvalidPattern_Tips": "En az 4 puan gereklidir. Tekrar ayarlayın.", "Wallet_Lock_PatternLock_Setup_Succ_Tips": "<PERSON>en parolası kümesi", "Wallet_Lock_PatternLock_Modify_Succ_Tips": "Desen parolası değiştirildi", "Wallet_Lock_PatternLock_Setup_Cancel_Tips": "Desen kilidi devre dışı bırakılsın mı?", "Wallet_Lock_PatternLock_Setup_Cancel_LeftBtn": "Etkinleştir", "Wallet_Lock_PatternLock_Setup_Cancel_RightBtn": "<PERSON><PERSON><PERSON>", "Wallet_Lock_Not_Support_TouchID_Tip": "Touch ID bu cihazda kullanılamıyor. Güvenlik kilidini sıfırlayın.", "Wallet_Lock_Not_Support_FaceID_Tip": "Face ID bu cihazda mevcut değil. Güvenlik kilidini sıfırlayın.", "Wallet_Lock_Close_Wallet_Lock_Tip": "Güvenlik kilidini devre dışı bırak", "Wallet_Lock_Setup_Pattern_Lock_Tip": "<PERSON>en parolasını ayarla", "Wallet_Lock_TouchID_NotEnrolled_Tip": "Touch ID etkinleştirilmedi. Sistem ayarlarında Touch ID'yi etkinleştirin veya güvenlik kilidini sıfırlayın.", "Wallet_Lock_FaceID_NotEnrolled_Tip": "Face ID etkin değil. Sistem ayarlarında Face ID'yi etkinleştirin veya güvenlik kilidini sıfırlayın.", "Wallet_Lock_FingerPrint_NotEnrolled_Tip": "Sistemde parmak izi yok. Parmak izinizi kaydedin ve güvenlik kilidini tekrar ayarlayın.", "Wallet_Lock_PatternLock_VerifyBlock_Tip": "Çok fazla deneme yapıldı. \"Desen Parolasını Sıfırla\" ile kimliğinizi doğrulayın veya 10 dakika sonra tekrar deneyin.", "Wallet_Lock_PatternLock_VerifyBlock_Reset_Tip": "Desen Parolasını Sıfırla", "Wallet_Lock_New_FingerPrint_Authen_Tips": "Yeni parmak izi girildi. Kimliği doğrulamak için ödeme parolasını girin.", "Wallet_Lock_New_TouchID_Authen_Tips": "Bu cihazdaki parmak izi bilgisi değişti. Kimliğinizi doğrulamak için ödeme parolası girin.", "Wallet_Lock_New_FaceID_Authen_Tips": "Bu cihazdaki yüz bilgisi değişti. Kimliğinizi doğrulamak için ödeme parolası girin.", "Wallet_Lock_Forget_Pwd": "Parolamı Unuttum", "Wallet_Lock_Retry_Pwd": "<PERSON><PERSON><PERSON> dene", "LQT_Detail_Operation_More_Product_Title": "<PERSON><PERSON>", "pay_settings_biometric_pay_touchid_title": "Parmak <PERSON>zi<PERSON>", "pay_settings_biometric_pay_faceid_title": "<PERSON><PERSON><PERSON>", "pay_settings_biometric_pay_multi_title": "<PERSON><PERSON><PERSON>/Parmak İziyle <PERSON>", "pay_settings_biometric_pay_touchid_desc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ödeme işlemlerini daha hızlı gerçekleştirmek için parmak izi doğrulamasını kullanabilirsiniz.", "pay_settings_biometric_pay_faceid_desc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ödeme işlemlerini daha hızlı gerçekleştirmek için yüz doğrulamasını kullanabilirsiniz.", "pay_settings_biometric_pay_multi_desc": "Ödemeleri daha hızlı yapmak için yüz veya parmak i<PERSON>yle <PERSON>meyi etkinleştirin.", "pay_settings_biometric_pay_enable_faceid": "<PERSON><PERSON><PERSON>", "pay_settings_biometric_pay_enable_touchid": "Parmak İziyle <PERSON> etkinleştir", "common_reddot_accessibility": "Yeni mesa<PERSON>ı<PERSON><PERSON> var", "common_help": "Yardım", "bind_new_card_input_name": "Bankada kayıtlı adı girin", "paydesk_title_accessibility_selected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RedDot_New": "NEW", "renewal_or_sign_time_item_key": "Ruhsatlar/<PERSON><PERSON>", "WCPay_Option_Item": "Opsiyonel", "VoiceOver_OfflinePay_Unselected": "Seçilmedi", "FillCard_Number_Reg_Hint_Self": "Ücret yok", "common_continue": "devam", "WCPay_Risk_Dialog_Title": "Potansiyel risk tespit edildi. Lütfen devam etmek isteyip istemediğinizi onaylayın. <PERSON><PERSON> ise, kimliğinizin doğrulanması gerekecektir.", "WCPay_Risk_Not_Support_Dialog_Title": "Potansiyel risk tespit edildi. Lütfen devam etmeden önce riskleri çözün.", "WCPay_Risk_Failed_Dialog_Title": "Kimlik doğrulaması başarısız oldu ve işlemin kapatılması gerekiyor", "bind_card_agreement_protocal_and_next": "Kabul Et ve Ekle", "wallet": "Cüzdan", "awaiting_real_name_verification": "Bekleyen kimlik doğrulaması", "five_stars": "*****", "Card_UserAgreement_Read_And_Agreement_Title": "<PERSON>kleme yapılırken onay alınması gerekir", "FaceCheck_Common_Error": "Sistem meşgul. <PERSON><PERSON><PERSON> den<PERSON>.", "FaceCheck_MP_Request_Use": " <PERSON><PERSON><PERSON><PERSON> is<PERSON>", "FaceCheck_MP_Confirm_Tip": "___<BRAND>___ kimliğinizi doğrulamak için yüz tanıma özelliğini kullanır. Bu yalnızca hesap sahibi tarafından ayarlanmalıdır.", "FaceCheck_MP_Front_Feedback": "<PERSON><PERSON><PERSON>", "FaceCheck_Recoging": "Tanınıyor...", "waiting_for_real_name_authentication": "Bekleyen kimlik doğrulaması", "collect_sub_title": "<PERSON><PERSON>", "collect_main_add_desc_title_simple_change": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "collect_main_add_desc_title": "Not Ekle", "remittance_amount_lowest_limit": "En düşük transfer miktarı 0.01¥'dir.", "collect_main_fixed": "Tutarı Belirle", "collect_main_first_enter_tips_title": "Para Alınıyor", "collect_main_first_enter_tips_new": "Alınan para ___<BRAND_Balance>___'nize (\"<PERSON>\" > \"<PERSON><PERSON><PERSON><PERSON>\" > \"<PERSON><PERSON>zda<PERSON>\") yatırılacak ve burada harcanabilecek veya çekilebilecektir.", "collect_main_close_ring_tone": "Alındı Uyarı Sesini Kapat", "collect_main_close_ring_tone_tips": "Devre Dışı", "collect_main_open_ring_tone": "Alındı Uyarı Sesini Aç", "collect_main_open_ring_tone_tips": "Etkin. Lütfen medya sesinizin açık olup olmadığını kontrol edin.", "collect_main_qrcode_usage_other": "Di<PERSON><PERSON><PERSON><PERSON>", "collect_main_qrcode_usage_other_placeholder": "<PERSON><PERSON><PERSON> (en fazla 16 karakter)", "collect_main_payer_desc_default_placeholder": "Alı<PERSON>ı için bir not e<PERSON>in.", "collect_qrcode_save_failed": "<PERSON><PERSON><PERSON> başarıs<PERSON>z oldu", "collect_material_guide_save_text_toast": "<PERSON><PERSON><PERSON><PERSON>", "collect_mch_module_title": "Şahsa Ait İşletme Ödeme Kodu", "collect_personal_module_title": "Kişiye Özel QR Kodu", "collect_setting_title": "Makbuz <PERSON>ları", "collect_main_fixed_cancel": "Tutarı Temizle", "collect_main_more_function": "<PERSON><PERSON>", "collect_main_save_qrcode": "<PERSON><PERSON><PERSON>", "collect_main_receive_title": "Toplam", "collect_main_paying": "Ödeme yapılıyor...", "collect_main_pay_suc": "Ödeme başarılı", "collect_main_pay_cancel": "Ödemeyi İptal Et", "collect_main_loading_title": "QR kodu yükleniyor...", "collect_main_ring_not_support": "Bu sistemde desteklenmiyor", "WCPay_Transfer_To_Format": "{}'a transfer et", "WCPay_Transfer_Weixin_Alias_Prefix": "___<BRAND_ID>___:", "WCPay_Transfer_InputAmount_Tips": "<PERSON>nce tutarı girin", "WCPay_Transfer_Cashier_Desc_Format": "{}'a transfer et", "WCPay_Transfer_Succ_Tips": "{} tarafından alınmayı bekliyor", "WCPay_Service": "<PERSON><PERSON><PERSON><PERSON>", "recognize_and_pay": "<PERSON>", "bizf2f_input_ui_page_to_person": "Bireye Ödeme", "bizf2f_input_ui_page_add_remark": "Not Ekle", "bizf2f_input_ui_page_amount_title": "<PERSON><PERSON><PERSON>", "WCPay_Verify_Password_Get_SMS_Code": "Doğrulama Kodu Alın", "WCPay_VerifyCode_Header_Description": "İşlem için SMS doğrulaması gereklidir.", "bizf2f_input_ui_page_pay_action": "<PERSON><PERSON><PERSON> yap", "bizf2f_input_ui_page_change_remark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bizf2f_input_ui_page_pay_title": "<PERSON><PERSON><PERSON> yap", "bizf2f_favor_title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bizf2f_favor_total_fee": "Toplam Tutar", "bizf2f_favor_calculating": "Hesaplıyor...", "bizf2f_favor_select_favor": "<PERSON><PERSON><PERSON>", "UN_BIND_CARD_TITLE": "Banka Kartının Bağlantısını Kaldır", "WCPay_system_version_limitation_tip": "Daha fazla özellik için HarmonyOS 4.2 veya daha eski sürümlerini veya diğer cihazları kontrol edin.", "reconfirm_payment_amount_title": "Ödeme tutarını tekrar onaylayın", "reconfirm_payment_amount_des": "Varlıklarınızın güvenliği için, hatalardan kaçınmak adına tutarı teyit edin.", "reconfirm_amount_page_tip": "Düzenleyici gereklilikler nedeniyle, statik QR kodu limitlerini aşan ödemeler aşağıdaki dinamik QR kodunu tarayarak tamamlanmalıdır. Ödemeyi doğrulamak ve tamamlamak için düğmeye dokunun.", "Hongbao_SendUI_NavigationBar_Title": "Kırmızı Zarf Gönder", "Hongbao_SendUI_Send_Button_Titlle": "Kırmızı Zarfı Hazırla", "Hongbao_SendUI_Count_Title": "<PERSON><PERSON><PERSON>", "Hongbao_SendUI_Amount_Title_Group": "Toplam", "Hongbao_SendUI_Amount_Title_Single": "<PERSON><PERSON>", "Hongbao_SendUI_RandomLuckyMode_Title": "<PERSON><PERSON><PERSON><PERSON>", "Hongbao_SendUI_Count_Tips": "<PERSON><PERSON><PERSON> girin", "Hongbao_SendUI_Amount_Tips": "0.00¥", "Hongbao_SendUI_Default_Wishing": "En iyi dileklerimizle", "Hongbao_Per_Hongbao_Max_Amount_Format": "Her Kırmızı Zarf i<PERSON> {}CNY'ye kadar", "HongBao_SendTips": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HongBao_OpenTips": "Aç", "HongBao_AmoutTips": "CNY", "HongBao_MainTitle": "Kırmızı Zarf", "HongBao_Cashier_Desc": "___<BRAND_hongbao>___", "Hongbao_SendUI_RandomMode_Title": "<PERSON><PERSON><PERSON><PERSON>", "Hongbao_SendUI_NormalMode_Title": "Aynı Miktar", "Hongbao_SendUI_ExclusiveMode_Title": "Se<PERSON><PERSON>", "Hongbao_SendUI_Select_Count_Suffix": "", "Hongbao_SendUI_Group_Member_Count_Format": "Bu grubun {} <PERSON><PERSON><PERSON> var", "Hongbao_SendUI_Amount_Title_Group_Normal": "Her Bir Tutar", "Hongbao_SendUI_Perperson_Maxvalue_Error_Tips": "Her Kırmızı Zarf i<PERSON> {}CNY'ye kadar", "Hongbao_SendUI_Perperson_Minvalue_Error_Tips": "Her Kırmızı Zarf için en az {:.2f}", "Hongbao_SendUI_Count_Larger_Than_Group_Mem_Count_Error_Tips": "Kırmızı Zarfların sayısı grup üyelerininkini geçemez.", "Hongbao_SendUI_Total_Num_Error_Tips": "<PERSON><PERSON><PERSON><PERSON> miktar {}.", "Hongbao_SendUI_Select_Count_Data_Invalid_Error_Tips": "\"<PERSON>kt<PERSON>\" giri<PERSON><PERSON>i", "Hongbao_SendUI_Amount_Data_Invalid_Error_Tips": "\"Toplam\" giri<PERSON><PERSON>i", "Hongbao_SendUI_Select_Count_Zero_Error_Tips": "<PERSON><PERSON><PERSON><PERSON> seçin.", "Hongbao_SendUI_Amount_Larger_Than_Max_Amount_Error_Tips": "<PERSON>lam tutar {}CNY'yi geçemez.", "Hongbao_ReceiveModal_Detail_Link": "Detayları görüntüle", "Hongbao_DetailUI_Load_More_Text": "Daha fazlasını yüklemek için tıklayın", "TransferPhone_Entry_Title": "Transfer Yöntemi Seç", "TransferPhone_To_Bank_Title": "Banka Kartına Transfer", "TransferPhone_To_Bank_Desc": "Alıcının banka hesabına para aktarmak için banka kartını girin.", "TransferPhone_To_Phone_Title": "Cep Telefonu Numarasına Aktar", "TransferPhone_To_Phone_Desc": "___<BRAND_Balance>___'<PERSON><PERSON> aktarmak için al<PERSON>lının cep telefonu numarasını girin.", "TransferPhone_To_PaySetting": "Cep Telefonu Numarasına Transfer Ayarları", "WCPay_ThirdParty_Tips_Title": "Sorumluluk Reddi", "WCPay_Service_Manage": "Hizmetleri Yönetme", "identify_and_pay": "<PERSON>", "Hongbao_SendUI_Exclusive_Mode_Send_To_Who": "<PERSON><PERSON><PERSON>", "Hongbao_SendUI_Exclusive_Mode_Send_To_Who_Error_Tips": "\"<PERSON><PERSON><PERSON>\" se<PERSON><PERSON>", "Hongbao_SendUI_Amount_Data_Invalid_Error_Tips_2": "\"Tutar\" giri<PERSON><PERSON>i", "MerchantPay_Input_Remark_Hint_Format": "Alacaklı tarafından görülebilir. Maksimum {} karakter.", "MerchantPay_Input_Remark_Title": "Not Ekle", "MerchantPay_Transfer_To_Format": "{}'e öde", "Greeting_Hongbao_Random_Change_Amount": "Tutarı Değiştir", "Greeting_Hongbao_Who_Receive_Hongbao_Format": "{} tarafından açıldı", "set_amount": "Tutarı Belirle"}