function mm_infoParser(returnJsonString) {
    urlShareSource = new Object();

    var iconSource;
    var titleSource;
    var descSource;
    var urlSource = document.URL;
    //获取facebook的og:image，详见：
    //https://developers.facebook.com/docs/opengraph/using-objects?locale=zh_CN
    var metas = document.getElementsByTagName("meta");
    for (var i = 0; i < metas.length; ++i) {
        var meta = metas[i];
        if (meta.attributes["property"] != undefined && meta.attributes["property"].value === "og:image" && meta.attributes["content"] != undefined) {
            iconSource = meta.attributes["content"].value;
        }
        if (!iconSource && meta.attributes["name"] != undefined && meta.attributes["name"].value === "og:image" && meta.attributes["content"] != undefined) {
            iconSource = meta.attributes["content"].value;
        }
        
        if (meta.attributes["property"] != undefined && meta.attributes["property"].value === "og:title" && meta.attributes["content"] != undefined) {
            titleSource = meta.attributes["content"].value;
        }
        if (!titleSource && meta.attributes["name"] != undefined && meta.attributes["name"].value === "og:title" && meta.attributes["content"] != undefined) {
            titleSource = meta.attributes["content"].value;
        }
        
        if (meta.attributes["property"] != undefined && meta.attributes["property"].value === "og:description" && meta.attributes["content"] != undefined) {
            descSource = meta.attributes["content"].value;
        }
        if (!descSource && meta.attributes["name"] != undefined && meta.attributes["name"].value === "og:description" && meta.attributes["content"] != undefined) {
            descSource = meta.attributes["content"].value;
        }
    }
    //获取苹果safari icon
    if (!iconSource) {
        var linkTag = document.getElementsByTagName("link");
        for (var i = 0; i < linkTag.length; i++) {
            if (linkTag[i].rel == "apple-touch-icon-precomposed" || linkTag[i].rel == "apple-touch-icon") {
                iconSource = linkTag[i].href;
                break;
            }
            if (linkTag[i].rel === 'icon') {
                iconSource = linkTag[i].href;
            }
        }
    }

    if (!titleSource) {
        titleSource = document.title;
    }

    urlShareSource.descSource = descSource;
    urlShareSource.titleSource = titleSource;
    urlShareSource.iconSource = iconSource;
    urlShareSource.urlSource = urlSource;

    if (returnJsonString) {
        return JSON.stringify(urlShareSource);
    } else {
        return urlShareSource;
    }
}
/**
 下面为Safari预处理JS相关
 */
//=====================================================================
var MMExtensionClass = function() {};

MMExtensionClass.prototype = {
    run: function(arguments) {
        if (!document.body) {
            arguments.completionFunction({"title": document.title,
                                         "url": document.URL});
            return;
        }

        var urlShareSource = mm_infoParser();

        arguments.completionFunction({  "title": urlShareSource.titleSource,
                                        "image": urlShareSource.iconSource,
                                        "url": urlShareSource.urlSource,
                                        "desc": urlShareSource.descSource });
    }
};

var ExtensionPreprocessingJS = new MMExtensionClass;
//=====================================================================
