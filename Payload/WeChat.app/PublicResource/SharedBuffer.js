
WeixinSharedBuffer = {
    lockCallbacks : {},
    lockCallbackId: 0,
    new : function(ab){
        return SharedBufferCore.new(ab);
    },
    get : function(id){
        return SharedBufferCore.get(id);
    },
    destroy : function(id){
        return SharedBufferCore.destroy(id);
    },
    Lock : function(id, callback, write){
        var callbackId = ++WeixinSharedBuffer.lockCallbackId
        WeixinSharedBuffer.lockCallbacks[callbackId] = callback;
        return SharedBufferCore.Lock(id, callbackId, true);
    },
    Unlock : function(id, itemid){
        return SharedBufferCore.Unlock(id, itemid);
    },
    onLockCallback : function(callbackId){
        var callback = WeixinSharedBuffer.lockCallbacks[callbackId];
        if (typeof callback == 'function') {
          callback();
        }
        delete WeixinSharedBuffer.lockCallbacks[callbackId];
    }
}
