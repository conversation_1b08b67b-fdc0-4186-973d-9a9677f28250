/*
 * 在全局暴露一个 NativeGlobal 对象
 * 设计文档：http://git.code.oa.com/wxweb/game-design/blob/master/global/NativeGlobal/README.md
 */
(function() {

 // 默认从 Native 生成，如果 Native 没有生成，就声明一个
 if (typeof NativeGlobal === 'undefined') {
   NativeGlobal = {}
 }

// 有 Watcher，说明是 MagicBrushCore 环境
if (NativeGlobal.Watcher) {
    // 预载的时候可能用到; 环境真正拉起的时候，__wxConfig 会被覆盖掉
    NativeGlobal.__wxConfig = __wxConfig;
    let info = NativeGlobal.getSystemInfo();
    NativeGlobal.__wxConfig.devicePixelRatio = info.pixelRatio;
    NativeGlobal.__wxConfig.screenWidth = info.screenWidth;
    NativeGlobal.__wxConfig.screenHeight = info.screenHeight;
    NativeGlobal.findElementById = undefined;
    NativeGlobal.BindingObject = NativeGlobal.Watcher;
    let ScreenCanvas = NativeGlobal.ScreenCanvas;
    let OffscreenCanvas = NativeGlobal.OffscreenCanvas;
    var hasScreenCanvas = false
    NativeGlobal.Canvas = function() {
        if (!hasScreenCanvas) {
            hasScreenCanvas = true
            return new ScreenCanvas();
        } else {
            return new OffscreenCanvas(300, 150);
        }
    }
    let onbindingobjectdestruct;
    let EventHandler = NativeGlobal.EventHandler;
    Object.defineProperty(NativeGlobal.EventHandler,'onbindingobjectdestruct', {
        get:()=>{ return onbindingobjectdestruct },
        set:(l)=>{
            if (!!onbindingobjectdestruct) {
                EventHandler.removeEventListener('watcherdestruct', onbindingobjectdestruct);
            }
            if (!!l) {
                EventHandler.addEventListener('watcherdestruct', l);
                onbindingobjectdestruct = l;
            }
    }});
    let performanceNow = NativeGlobal.performanceNow;
    NativeGlobal.performanceNow = function() {
        return performanceNow() * 1000;
    }
    return;
}
 
var g = NativeGlobal
g.XMLHttpRequest = Ejecta.HttpRequest
g.Audio = Ejecta.Audio
g.XMLHttpRequest = Ejecta.HttpRequest
g.Download = Ejecta.Download
g.WSS = Ejecta.WebSocketTask
 
g.fs = {}
g.fs.FileReader = Ejecta.FileReader
g.fs.readFileSync = function (path, encoding) { return ej.readFileSync(path, encoding) }

// The 'ej' object provides some basic info and utility functions
var ej = new Ejecta.GlobalUtils()
var Canvas = Ejecta.Canvas
var BindingObject = Ejecta.BindingObject

// 把全局暴露的 __wxConfig 挪到 NativeGlobal
g.__wxConfig = __wxConfig

// TODO Reporter-SDK 里面会用到这个全局变量，不能直接移除
// __wxConfig = undefined

    
// 补充 __wxConfig 的属性
g.__wxConfig.devicePixelRatio = ej.devicePixelRatio
g.__wxConfig.screenWidth = ej.screenWidth
g.__wxConfig.screenHeight = ej.screenHeight

g.log = function (str) { ej.log(str) }
    
const canvasSet = new Set()
g.ScreenCanvas = function () {
    var canvas = new Canvas(1)
    canvasSet.add(canvas)
    var old = canvas.remove
    canvas.remove = () => {
      canvasSet.delete(canvas)
      old.call(canvas)
    }
    return canvas;
}
 
g.setTimeout = function (cb, t) { return ej.setTimeout(cb, t || 0) }
g.setInterval = function(cb, t){ return ej.setInterval(cb, t || 0) }
g.clearTimeout = function(id){ return ej.clearTimeout(id) }
g.clearInterval = function(id){ return ej.clearInterval(id) }
g.requestAnimationFrame = function(cb){ return ej.requestAnimationFrame(cb) }
g.cancelAnimationFrame = function(id){ return ej.cancelAnimationFrame(id) }
g.setPreferredFramesPerSecond = function(fps){ return ej.setPreferredFramesPerSecond(fps) }
g.loadFont = function(path){ return ej.loadFont(path) }
g.encodeArrayBuffer = function(str, code){ return ej.encodeArrayBuffer(str, code) }
g.decodeArrayBuffer = function(buffer, code){ return ej.decodeArrayBuffer(buffer, code) }
g.performanceNow = function(){ return ej.performanceNow() }
g.performanceNowUs = function(){ return ej.performanceNowUs() }
g.recordFrame = function(canvasid){ return ej.recordFrame(canvasid) }
g.getTextLineHeight = function(style,weight,size,family,text){ return ej.getTextLineHeight(style,weight,size,family,text) }
g.startProfile = function () { return ej.startProfile() }
g.stopProfile = function () { return ej.stopProfile() }
g.getProfileResult = function () { return ej.getProfileResult() }
g.decodeUint64Array = function () { return ej.decodeUint64Array.apply(ej, arguments) }
g.decodeVarintArray = function () { return ej.decodeVarintArray.apply(ej, arguments) }
g.getSystemInfo = function () { return ej.getSystemInfo() }
    
g.OffscreenCanvas = function () {
    var c = new Canvas()
    c.uid = c.__canvasId()
    return c
}
    
g.setGlobalAttribute = function (a){return ej.setGlobalAttribute(a) }
commandRender = function (str, sync){ return ej.batchRender(str, sync) }
 
// TODO 临时暴露到全局以解决 Reporter 错误
setTimeout = g.setTimeout;
clearTimeout = g.clearTimeout;
setInterval = g.setInterval;
clearInterval = g.clearInterval;
    
 var Image = Ejecta.Image;
 g.Image = function(w, h){
 var img = new Image(w, h)
 img.uid = img.__id();
 return img;
 }
 
var screenCanvas = new Ejecta.Canvas()
var hasScreenCanvas = false
g.Canvas = function () {
  if (hasScreenCanvas) {
      return g.OffscreenCanvas()
  } else {
    hasScreenCanvas = true
    screenCanvas.uid = screenCanvas.__canvasId()
    return screenCanvas
  }
}
g.BindingObject = function () {
  return new BindingObject(ej)
}
g.EventHandler = {}
g.EventHandler.ontouchstart = g.EventHandler.ontouchend = g.EventHandler.ontouchmove = null
g.Path2D = Ejecta.Path2D;
g.ImageData = Ejecta.ImageData;

function copyTouchArray(touches) {
  return touches.map(function (touch) {
    return Object.assign({}, touch)
  })
}

var touchInput = new Ejecta.TouchInput(screenCanvas)
var touchEventNames = ['ontouchstart', 'ontouchmove', 'ontouchend', 'ontouchcancel']
touchEventNames.forEach(function (touchEventName) {
  var FUNCTION_STR = 'function'
  var OBJECT_STR = 'object'
  var dstName = touchEventName + 'Dst'
  touchInput[touchEventName] = function (touches, changedTouches, timestamp) {
    var stop = false
    var target  = null;
    if(touches.length > 0 && canvasSet.has(touches[0].target)){
     target = touches[0].target;
    }
    if(target == null && changedTouches.length > 0 && canvasSet.has(changedTouches[0].target)){
     target = changedTouches[0].target;
    }
    if(target){
      if (typeof target[touchEventName] === 'function'){
        var event = {
            type: touchEventName,
            touches: copyTouchArray(touches),
            changedTouches: copyTouchArray(changedTouches),
            timeStamp: timestamp
          }
        var ret = target[touchEventName](event)
        if (typeof ret === 'boolean' && !ret) stop = true
      }
    }
    if (stop) return
    if (typeof g.EventHandler[touchEventName] === FUNCTION_STR) {
      var event
      if (typeof g.EventHandler[dstName] === OBJECT_STR) {
        event = g.EventHandler[dstName]
        event.type = touchEventName
        event.touches = touches
        event.changedTouches = changedTouches
        event.timeStamp = timestamp
      } else {
        event = {
          type: touchEventName,
          touches: copyTouchArray(touches),
          changedTouches: copyTouchArray(changedTouches),
          timeStamp: timestamp
        }
      }
      g.EventHandler[touchEventName].call(g, event)
    }
  }
})
 
ej.onbindingobjectdestruct = function (id) {
  if (typeof g.EventHandler.onbindingobjectdestruct === 'function') {
    g.EventHandler.onbindingobjectdestruct.call(g, id)
  }
}

g.createSignal = function () { return ej.createSignal() }
    
var global = (function () { return this })()
delete global.Ejecta
})();
