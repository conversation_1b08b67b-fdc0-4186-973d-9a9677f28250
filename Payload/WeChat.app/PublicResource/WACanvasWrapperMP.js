/*
 * 从 WACanvasWrapper 迁移过来。作为小程序下统一的 webgl 实现基础库
 * NativeGlobal 设计文档：
 * http://git.code.oa.com/wxweb/game-design/blob/master/global/NativeGlobal/README.md
 */
(function(global) {

var g = (function() {
    if (!global.WANativeGlobal) {
        global.WANativeGlobal = {};
    }
    return global.WANativeGlobal;
})()
var w = WeixinCanvas

// The 'ej' object provides some basic info and utility functions
var ej = new Ejecta.GlobalUtils()
var Canvas = Ejecta.Canvas
var TouchInput = Ejecta.TouchInput
var BindingObject = Ejecta.BindingObject

g.log = function (str) { ej.log(str) }
g.setTimeout = function (cb, t) { return ej.setTimeout(cb, t || 0) }
g.setInterval = function(cb, t){ return ej.setInterval(cb, t || 0) }
g.clearTimeout = function(id){ return ej.clearTimeout(id) }
g.clearInterval = function(id){ return ej.clearInterval(id) }
g.requestAnimationFrame = function(cb){ return ej.requestAnimationFrame(cb) }
g.cancelAnimationFrame = function(id){ return ej.cancelAnimationFrame(id) }
g.setPreferredFramesPerSecond = function(fps){ return ej.setPreferredFramesPerSecond(fps) }
g.loadFont = function(path, params){ return ej.loadFont(path, params) }
g.encodeArrayBuffer = function(str, code){ return ej.encodeArrayBuffer(str, code) }
g.decodeArrayBuffer = function(buffer, code){ return ej.decodeArrayBuffer(buffer, code) }
g.performanceNow = function(){ return ej.performanceNow() }
g.performanceNowUs = function(){ return ej.performanceNowUs() }
g.recordFrame = function(canvasid){ return ej.recordFrame(canvasid) }
g.getTextLineHeight = function(style,weight,size,family,text){ return ej.getTextLineHeight(style,weight,size,family,text) }
g.startProfile = function () { return ej.startProfile() }
g.stopProfile = function () { return ej.stopProfile() }
g.getProfileResult = function () { return ej.getProfileResult() }
g.setGlobalAttribute = function (a) {return ej.setGlobalAttribute(a) }
g.ImageData = Ejecta.ImageData;
g.Path2D = Ejecta.Path2D;
g.ExternalTexture = Ejecta.ExternalTexture;
createCommandBuffer = function (buffer){ return ej.createCommandBuffer(buffer) }
processCommandBuffer = function (sync){ return ej.processCommandBuffer(sync) }

g.BindingObject = function () { return new BindingObject(ej) }
var Image = Ejecta.Image;
g.Image = function(){
    var img = new Image
    img.uid = img.__id();
    return img;
}

g.SharedCanvas = function () {
    return new Canvas(1)
}
g.OffscreenCanvas = function (width, height) {
    return new Canvas(width, height)
}

var touchEventNames = ['ontouchstart', 'ontouchmove', 'ontouchend', 'ontouchcancel']
g.ScreenCanvas = function (viewId) {
    var c = new Canvas(viewId);
    c.uid = c.__canvasId();
    c.id = c.__id;
    let touchInput = new TouchInput({}, c.id);

    touchEventNames.forEach(function (touchEventName) {
        var FUNCTION_STR = "function";
        touchInput[touchEventName] = function (touches, changedTouches, timestamp) {
            if (typeof WeixinCanvas[touchEventName] === FUNCTION_STR) {
                var event = {
                    type: touchEventName,
                    touches: touches,
                    changedTouches: changedTouches,
                    timeStamp: timestamp,
                    canvasId: viewId
                };
                WeixinCanvas[touchEventName].call(g, event)
            }
        };
    });
    return c;
}

ej.onbindingobjectdestruct = function (id) {
  if (typeof g.EventHandler.onbindingobjectdestruct === 'function') {
    g.EventHandler.onbindingobjectdestruct.call(g, id)
  }
}

w.createCanvas = function(id) {
    g.log("Create ScreenCanvas (" + id + ")");
    return new g.ScreenCanvas(id);
}
w.createOffscreenCanvas = function(id, w, h) {
    g.log("Create OffscreenCanvas (" + w + "," + h + ")");
    return new g.OffscreenCanvas(w, h);
}
w.recordFrame = function(id, offscreenId) {
    return g.recordFrame(offscreenId);
}
w.requestAnimationFrame = g.requestAnimationFrame;
w.cancelAnimationFrame = g.cancelAnimationFrame;
w.createImage = function() {
    return new g.Image();
};
w.ImageData = g.ImageData;
w.Path2D = g.Path2D;
w.ExternalTexture = g.ExternalTexture;
w.loadFont = g.loadFont;

delete global.Ejecta;
})(this);
