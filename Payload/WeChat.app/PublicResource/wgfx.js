(function() {
  if (NativeGlobal && NativeGlobal.buildGfx) {
      return;
  }
  var wgfx;
  (function (wgfx) {
    function some(opt0, opt1) {
      return opt0 != null ? opt0 : opt1;
    }
    wgfx.some = some;
    function some3(opt0, opt1, opt2) {
      return opt0 != null ? opt0 : (opt1 != null ? opt1 : opt2);
    }
    wgfx.some3 = some3;

    class Gfx {
      constructor(ctx) {
        this.gfx = ctx;
        wgfx.setGfxConsts(this);
        this.features = [];
        this.features_getted = false;
      }

      queryFeature(feature) {
        if (!this.features_getted) {
          this.features = this.gfx.queryFeatures();
          this.features_getted = true;
        }
        return this.features[feature];
      }

      makePass(o) {
        var pass = this.gfx.makePass(JSON.stringify(o));
        pass.desc = o;
        return pass;
      }

      makeBuffer(o) {
        var oldContent;
        var nativeBuffer;
        if (o.content != null) {
          if (ArrayBuffer.isView(o.content)) {
            nativeBuffer = this.gfx.createNativeBuffer(o.content);
            oldContent = o.content;
            o.content = nativeBuffer.id;
          } else {
            oldContent = o.content;
            o.content = o.id;
          }
        }
        var buffer = this.gfx.makeBuffer(JSON.stringify(o));
        if (oldContent) {
          o.content = oldContent;
        }
        return buffer;
      }

      updateBuffer(buffer, offset, data, dataOffset, dataSize) {
        if (ArrayBuffer.isView(data)) {
          var nativeBuffer = this.gfx.createNativeBuffer(data);
          if (!dataOffset) {
            dataOffset = 0;
          }
          if (!dataSize) {
            dataSize = data.byteLength - dataOffset;
          }
          this.gfx.updateBuffer(buffer.id, offset, nativeBuffer.id, dataOffset, dataSize);
        } else {
          if (!dataOffset) {
            dataOffset = 0;
          }
          if (!dataSize) {
            dataSize = data.data.byteLength - dataOffset;
          }
          this.gfx.updateBuffer(buffer.id, offset, data.id, dataOffset, dataSize);
        }
      }

      makeShader(o) {
        if (!o.binary) {
          return this.gfx.makeShader(JSON.stringify(o));
        }
        if (ArrayBuffer.isView(o.binary)) {
          o.binary = this.gfx.createNativeBuffer(o.binary);
        }
        o.binary = o.binary.id;
        return this.gfx.makeShader(JSON.stringify(o));
      }

      makeImage(o) {
        var nativeBuffers = [];
        if (o.content != null && o.content.subimage != null) {
          for (var i = 0; i < this.CUBEFACE_NUM; i++) {
            nativeBuffers[i] = [];
            if (o.content.subimage[i] == null) {
              continue;
            }
            for (var j = 0; j < this.MAX_MIPMAPS; j++) {
              if (o.content.subimage[i][j] == null) {
                continue;
              }
              var subimage = o.content.subimage[i][j];
              if (ArrayBuffer.isView(subimage)) {
                var nativeBuffer = this.gfx.createNativeBuffer(subimage);
                nativeBuffers[i][j] = {
                  buffer: nativeBuffer,
                  oldSubImage: subimage
                };
                o.content.subimage[i][j] = {
                  buffer_id: nativeBuffer.id
                }
              } else if (subimage.__uid) {
                nativeBuffers[i][j] = {
                  buffer: null,
                  oldSubImage: o.content.subimage[i][j]
                };
                o.content.subimage[i][j] = {
                  image_id: o.content.subimage[i][j].__uid()
                };
              } else {
                nativeBuffers[i][j] = {
                  oldSubImage: subimage
                };
                o.content.subimage[i][j] = {
                  buffer_id: subimage.id
                }
              }
            }
          }
        }
        var img = this.gfx.makeImage(JSON.stringify(o));

        for (var i = 0; i < this.CUBEFACE_NUM; i++) {
          if (nativeBuffers[i] != null) {
            for (var j = 0; j < this.MAX_MIPMAPS; j++) {
              if (nativeBuffers[i][j] != null) {
                o.content.subimage[i][j] = nativeBuffers[i][j].oldSubImage;
              }
            }
          }
        }
        return img;
      }


      updateImage(img, o) {
        var nativeBuffers = [];
        if (o.content != null && o.content.subimage != null) {
          for (var i = 0; i < this.CUBEFACE_NUM; i++) {
            nativeBuffers[i] = [];
            if (o.content.subimage[i] == null) {
              continue;
            }
            for (var j = 0; j < this.MAX_MIPMAPS; j++) {
              if (o.content.subimage[i][j] == null) {
                continue;
              }
              var subimage = o.content.subimage[i][j];
              if (ArrayBuffer.isView(subimage)) {
                var nativeBuffer = this.gfx.createNativeBuffer(subimage);
                nativeBuffers[i][j] = {
                  buffer: nativeBuffer,
                  oldSubImage: subimage
                };
                o.content.subimage[i][j] = {
                  buffer_id: nativeBuffer.id
                }
              } else if (o.content.subimage[i][j].__uid) {
                nativeBuffers[i][j] = {
                  buffer: null,
                  oldSubImage: o.content.subimage[i][j]
                };
                o.content.subimage[i][j] = {
                  image_id: o.content.subimage[i][j].__uid()
                };
              } else {
                nativeBuffers[i][j] = {
                  oldSubImage: subimage
                };
                o.content.subimage[i][j] = {
                  buffer_id: subimage.id
                }
              }
            }
          }
        }
        this.gfx.updateImage(img.id, JSON.stringify(o));

        for (var i = 0; i < this.CUBEFACE_NUM; i++) {
          if (nativeBuffers[i] != null) {
            for (var j = 0; j < this.MAX_MIPMAPS; j++) {
              if (nativeBuffers[i][j] != null) {
                o.content.subimage[i][j] = nativeBuffers[i][j].oldSubImage;
              }
            }
          }
        }
      }

      makePipeline(o) {
        var obj = this.gfx.makePipeline(JSON.stringify(o));
        if (o.shader) obj.shader = o.shader;
        return obj;
      }

      makePassAction(desc) {
        return JSON.stringify(desc);
      }

      beginPass(pass, passAction) {
        this.gfx.beginPass(pass.id, passAction);
      }

      beginDefaultPass(passAction, width, height) {
        this.gfx.beginDefaultPass(passAction, width, height);
      }

      applyPipeline(pip) {
        this.gfx.applyPipeline(pip.id);
      }

      makeBindings(bind) {
        return {
          bindings: bind,
          json: JSON.stringify(bind)
        }
      }

      applyBindings(bind) {
        this.gfx.applyBindings(bind.json);
      }

      queryGlyphBitmapInfo(text, font) {
        return this.gfx.queryGlyphBitmapInfo(text, font);
      }

      queryShaderBinary(shader) {
        return this.gfx.queryShaderBinary(shader.id);
      }

      applyUniforms(shaderStage, index, data) {
        if (ArrayBuffer.isView(data)) {
          var nativeBuffer = this.gfx.createNativeBuffer(data);
          this.gfx.applyUniforms(shaderStage, index, nativeBuffer.id);
        } else {
          this.gfx.applyUniforms(shaderStage, index, data.id);
        }
      }

      applyUniformBlock(shaderStage, ub_index, ubo) {
        this.gfx.applyUniformBlock(shaderStage, ub_index, ubo.id);
      }

      applyViewport(x, y, width, height, origin_top_left) {
        this.gfx.applyViewport(x, y, width, height, origin_top_left);
      }

      applyScissorRect(x, y, width, height, origin_top_left) {
        this.gfx.applyScissorRect(x, y, width, height, origin_top_left);
      }

      draw(baseElement, numElements, numInstances) {
        this.gfx.draw(baseElement, numElements, numInstances);
      }

      endPass() {
        this.gfx.endPass();
      }
      commit() {
        this.gfx.commit();
      }

      createNativeBuffer(arraybufferView) {
        return this.gfx.createNativeBuffer(arraybufferView);
      }

      queryPassState(pass) {
        return this.gfx.queryPassState(pass.id);
      }
      queryBufferState(buf) {
        return this.gfx.queryBufferState(buf.id);
      }
      queryImageState(image) {
        return this.gfx.queryImageState(image.id);
      }
      queryShaderState(shd) {
        return this.gfx.queryShaderState(shd.id);
      }
      queryPipelineState(pip) {
        return this.gfx.queryPipelineState(pip.id);
      }
      queryBackend() {
        return this.gfx.queryBackend();
      }
    }

    wgfx.Gfx = Gfx;

    function setGfxConsts(gfx) {
      // sg_backend
      gfx.BACKEND_GLES2 = 0,
      gfx.BACKEND_GLES3 = 1,
      gfx.BACKEND_METAL = 2,

      // sg_feature
      gfx.FEATURE_INSTANCING = 0;
      gfx.FEATURE_TEXTURE_COMPRESSION_DXT = 1;
      gfx.FEATURE_TEXTURE_COMPRESSION_PVRTC = 2;
      gfx.FEATURE_TEXTURE_COMPRESSION_ATC = 3;
      gfx.FEATURE_TEXTURE_COMPRESSION_ETC2 = 4;
      gfx.FEATURE_TEXTURE_FLOAT = 5;
      gfx.FEATURE_TEXTURE_HALF_FLOAT = 6;
      gfx.FEATURE_ORIGIN_BOTTOM_LEFT = 7;
      gfx.FEATURE_ORIGIN_TOP_LEFT = 8;
      gfx.FEATURE_MSAA_RENDER_TARGETS = 9;
      gfx.FEATURE_PACKED_VERTEX_FORMAT_10_2 = 10;
      gfx.FEATURE_MULTIPLE_RENDER_TARGET = 11;
      gfx.FEATURE_IMAGETYPE_3D = 12;
      gfx.FEATURE_IMAGETYPE_ARRAY = 13;
      gfx.FEATURE_TEXTURE_COMPRESSION_ETC1 = 14;
      gfx.FEATURE_TEXTURE_COMPRESSION_ASTC_8x8 = 15;
      gfx.FEATURE_TEXTURE_COMPRESSION_ASTC_FULL = 16;

      gfx.FEATURE_SHADER_BINARY = 17;
      gfx.NUM_FEATURES = 18;

      // sg_resource_state
      gfx.RESOURCESTATE_INITIAL = 0;
      gfx.RESOURCESTATE_ALLOC = 1;
      gfx.RESOURCESTATE_VALID = 2;
      gfx.RESOURCESTATE_FAILED = 3;
      gfx.RESOURCESTATE_INVALID = 4;

      // sg_usage
      gfx.USAGE_IMMUTABLE = 1;
      gfx.USAGE_DYNAMIC = 2;
      gfx.USAGE_STREAM = 3;

      // sg_buffer_type
      gfx.BUFFERTYPE_VERTEXBUFFER = 1;
      gfx.BUFFERTYPE_INDEXBUFFER = 2;

      // sg_index_type
      gfx.INDEXTYPE_NONE = 1;
      gfx.INDEXTYPE_UINT16 = 2;
      gfx.INDEXTYPE_UINT32 = 3;

      // sg_image_type
      gfx.IMAGETYPE_2D = 1;
      gfx.IMAGETYPE_CUBE = 2;
      gfx.IMAGETYPE_3D = 3;
      gfx.IMAGETYPE_ARRAY = 4;

      // sg_cube_face
      gfx.CUBEFACE_POS_X = 0;
      gfx.CUBEFACE_NEG_X = 1;
      gfx.CUBEFACE_POS_Y = 2;
      gfx.CUBEFACE_NEG_Y = 3;
      gfx.CUBEFACE_POS_Z = 4;
      gfx.CUBEFACE_NEG_Z = 5;
      gfx.CUBEFACE_NUM = 6;

      // sg_shader_stage
      gfx.SHADERSTAGE_VS = 0;
      gfx.SHADERSTAGE_FS = 1;

      // sg_pixel_format
      gfx.PIXELFORMAT_NONE = 1;
      gfx.PIXELFORMAT_RGBA8 = 2;
      gfx.PIXELFORMAT_RGB8 = 3;
      gfx.PIXELFORMAT_RGBA4 = 4;
      gfx.PIXELFORMAT_R5G6B5 = 5;
      gfx.PIXELFORMAT_R5G5B5A1 = 6;
      gfx.PIXELFORMAT_R10G10B10A2 = 7;
      gfx.PIXELFORMAT_RGBA32F = 8;
      gfx.PIXELFORMAT_RGBA16F = 9;
      gfx.PIXELFORMAT_R32F = 10;
      gfx.PIXELFORMAT_R16F = 11;
      gfx.PIXELFORMAT_L8 = 12;
      gfx.PIXELFORMAT_DXT1 = 13;
      gfx.PIXELFORMAT_DXT3 = 14;
      gfx.PIXELFORMAT_DXT5 = 15;
      gfx.PIXELFORMAT_DEPTH = 16;
      gfx.PIXELFORMAT_DEPTHSTENCIL = 17;
      gfx.PIXELFORMAT_PVRTC2_RGB = 18;
      gfx.PIXELFORMAT_PVRTC4_RGB = 19;
      gfx.PIXELFORMAT_PVRTC2_RGBA = 20;
      gfx.PIXELFORMAT_PVRTC4_RGBA = 21;
      gfx.PIXELFORMAT_ETC2_RGB8 = 22;
      gfx.PIXELFORMAT_ETC2_SRGB8 = 23;
      gfx.PIXELFORMAT_ETC1_RGB8 = 24;
      gfx.PIXELFORMAT_PVR_CCZ = 25;
      gfx.PIXELFORMAT_PVR_GZ = 26;

      // sg_primitive_type
      gfx.PRIMITIVETYPE_POINTS = 1;
      gfx.PRIMITIVETYPE_LINES = 2;
      gfx.PRIMITIVETYPE_LINE_STRIP = 3;
      gfx.PRIMITIVETYPE_TRIANGLES = 4;
      gfx.PRIMITIVETYPE_TRIANGLE_STRIP = 5;

      // sg_filter
      gfx.FILTER_NEAREST = 1;
      gfx.FILTER_LINEAR = 2;
      gfx.FILTER_NEAREST_MIPMAP_NEAREST = 3;
      gfx.FILTER_NEAREST_MIPMAP_LINEAR = 4;
      gfx.FILTER_LINEAR_MIPMAP_NEAREST = 5;
      gfx.FILTER_LINEAR_MIPMAP_LINEAR = 6;

      // sg_wrap
      gfx.WRAP_REPEAT = 1;
      gfx.WRAP_CLAMP_TO_EDGE = 2;
      gfx.WRAP_MIRRORED_REPEAT = 3;

      // sg_vertex_format
      gfx.VERTEXFORMAT_INVALID = 0;
      gfx.VERTEXFORMAT_FLOAT = 1;
      gfx.VERTEXFORMAT_FLOAT2 = 2;
      gfx.VERTEXFORMAT_FLOAT3 = 3;
      gfx.VERTEXFORMAT_FLOAT4 = 4;
      gfx.VERTEXFORMAT_BYTE4 = 5;
      gfx.VERTEXFORMAT_BYTE4N = 6;
      gfx.VERTEXFORMAT_UBYTE4 = 7;
      gfx.VERTEXFORMAT_UBYTE4N = 8;
      gfx.VERTEXFORMAT_SHORT2 = 9;
      gfx.VERTEXFORMAT_SHORT2N = 10;
      gfx.VERTEXFORMAT_SHORT4 = 11;
      gfx.VERTEXFORMAT_SHORT4N = 12;
      gfx.VERTEXFORMAT_UINT10_N2 = 13;

      // sg_vertex_step
      gfx.VERTEXSTEP_PER_VERTEX = 1;
      gfx.VERTEXSTEP_PER_INSTANCE = 2;

      // sg_uniform_type
      gfx.UNIFORMTYPE_INVALID = 0;
      gfx.UNIFORMTYPE_FLOAT = 1;
      gfx.UNIFORMTYPE_FLOAT2 = 2;
      gfx.UNIFORMTYPE_FLOAT3 = 3;
      gfx.UNIFORMTYPE_FLOAT4 = 4;
      gfx.UNIFORMTYPE_MAT4 = 5;

      // sg_cull_mode
      gfx.CULLMODE_NONE = 1;
      gfx.CULLMODE_FRONT = 2;
      gfx.CULLMODE_BACK = 3;

      // sg_face_winding
      gfx.FACEWINDING_CCW = 1;
      gfx.FACEWINDING_CW = 2;

      // sg_compare_func
      gfx.COMPAREFUNC_NEVER = 1;
      gfx.COMPAREFUNC_LESS = 2;
      gfx.COMPAREFUNC_EQUAL = 3;
      gfx.COMPAREFUNC_LESS_EQUAL = 4;
      gfx.COMPAREFUNC_GREATER = 5;
      gfx.COMPAREFUNC_NOT_EQUAL = 6;
      gfx.COMPAREFUNC_GREATER_EQUAL = 7;
      gfx.COMPAREFUNC_ALWAYS = 8;

      // sg_stencil_op
      gfx.STENCILOP_KEEP = 1;
      gfx.STENCILOP_ZERO = 2;
      gfx.STENCILOP_REPLACE = 3;
      gfx.STENCILOP_INCR_CLAMP = 4;
      gfx.STENCILOP_DECR_CLAMP = 5;
      gfx.STENCILOP_INVERT = 6;
      gfx.STENCILOP_INCR_WRAP = 7;
      gfx.STENCILOP_DECR_WRAP = 8;

      // sg_blend_factor
      gfx.BLENDFACTOR_ZERO = 1;
      gfx.BLENDFACTOR_ONE = 2;
      gfx.BLENDFACTOR_SRC_COLOR = 3;
      gfx.BLENDFACTOR_ONE_MINUS_SRC_COLOR = 4;
      gfx.BLENDFACTOR_SRC_ALPHA = 5;
      gfx.BLENDFACTOR_ONE_MINUS_SRC_ALPHA = 6;
      gfx.BLENDFACTOR_DST_COLOR = 7;
      gfx.BLENDFACTOR_ONE_MINUS_DST_COLOR = 8;
      gfx.BLENDFACTOR_DST_ALPHA = 9;
      gfx.BLENDFACTOR_ONE_MINUS_DST_ALPHA = 10;
      gfx.BLENDFACTOR_SRC_ALPHA_SATURATED = 11;
      gfx.BLENDFACTOR_BLEND_COLOR = 12;
      gfx.BLENDFACTOR_ONE_MINUS_BLEND_COLOR = 13;
      gfx.BLENDFACTOR_BLEND_ALPHA = 14;
      gfx.BLENDFACTOR_ONE_MINUS_BLEND_ALPHA = 15;

      // sg_blend_op
      gfx.BLENDOP_ADD = 1;
      gfx.BLENDOP_SUBTRACT = 2;
      gfx.BLENDOP_REVERSE_SUBTRACT = 3;

      // sg_color_mask
      gfx.COLORMASK_NONE = (0x10);     /* special value for 'all channels disabled */
      gfx.COLORMASK_R = (1 << 0);
      gfx.COLORMASK_G = (1 << 1);
      gfx.COLORMASK_B = (1 << 2);
      gfx.COLORMASK_A = (1 << 3);
      gfx.COLORMASK_RGB = 0x7;
      gfx.COLORMASK_RGBA = 0xF;

      // sg_action
      gfx.ACTION_CLEAR = 1;
      gfx.ACTION_LOAD = 2;
      gfx.ACTION_DONTCARE = 3;

      // constants
      gfx.INVALID_ID = 0;
      gfx.NUM_SHADER_STAGES = 2;
      gfx.NUM_INFLIGHT_FRAMES = 2;
      gfx.MAX_COLOR_ATTACHMENTS = 4;
      gfx.MAX_SHADERSTAGE_BUFFERS = 4;
      gfx.MAX_SHADERSTAGE_IMAGES = 12;
      gfx.MAX_SHADERSTAGE_UBS = 4;
      gfx.MAX_UB_MEMBERS = 16;
      gfx.MAX_VERTEX_ATTRIBUTES = 16;
      gfx.MAX_MIPMAPS = 16;
      gfx.MAX_TEXTUREARRAY_LAYERS = 128;

      //PixelType
      gfx.UNSIGNED_BYTE = 0x1401;
      gfx.FLOAT = 0x1406;
      gfx.UNSIGNED_SHORT_5_6_5 = 0x8363;
      gfx.UNSIGNED_SHORT_4_4_4_4 = 0x8033;
      gfx.UNSIGNED_SHORT_5_5_5_1 = 0x8034;
    }
    wgfx.setGfxConsts = setGfxConsts;

  })(wgfx || (wgfx = {}));

  NativeGlobal.buildGfx = function (origin_gfx) {
    return new wgfx.Gfx(origin_gfx);
  }
})()
