 ;if (this.NativeGlobal && this.NativeGlobal.initModule) {
     var that = this;
     class LazyLoadModel {
         constructor(name) { this.name = name; }
         provideDelegateStr () { return this.name + '_'; }
     };
     // WeixinCanvas.recordFrame 怎么处理？
     var lazyLoadModels = [
         'findElementById', 'OffscreenCanvas', 'Image', 'ImageData', 'Path2D', 'requestAnimationFrame', 'cancelAnimationFrame', "EventHandler"
     ].map((name) => new LazyLoadModel(name));
     for (const model of lazyLoadModels) {
         if (!that.NativeGlobal[model.name]) {
             Object.defineProperty(that.NativeGlobal, model.name, {
                 get: function() {
                     if (model.isTriggeredInit) {
                         return model[model.provideDelegateStr()];
                     }
                     model.isTriggeredInit = true;
                     that.NativeGlobal.initModule(model.name);
                     return model[model.provideDelegateStr()];
                 },
                 set: function(value) {
                     model[model.provideDelegateStr()] = value;
                 }
             });
         }
     }
 };
