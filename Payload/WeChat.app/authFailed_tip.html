<!DOCTYPE html>
<html lang="zh_CN">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
	<title></title>
	<style>
	.dn{display:none;}
	.di{display:inline;}
	.dib{display:inline-block;}
	.b_dib{display:inline;display:inline-block;zoom:1;}
	.db{display:block;}
	.vh{visibility:hidden;}
	.vv{visibility:visible;}
	.rel{position:relative;}
	.abs{position:absolute;}
	.oh{overflow:hidden;}
	.z{zoom:1;}
	.l{float:left;}
	.r{float:right;}
	.fn{float:none;}
	.cl{clear:both;}
	.tc{text-align:center;}
	.tr{text-align:right;}
	.tl{text-align:left;}
	.tj{text-align:justify;text-justify:distribute;}
	.vt{vertical-align:top;}
	.vm{vertical-align:middle;}
	.vb{vertical-align:bottom;}
	.f0{font-size:0;}
	.fa{font-family:Arial;}
	.fs{font-family:SimSun;}
	.fyh{font-family:"Microsoft YaHei";}
	.indent{text-indent:2em;}
	.n{font-style:normal;font-weight:400;}
	.b{font-weight:700;}
	.i{font-style:italic;}
	.tdn{text-decoration:none;}
	.tdn:hover{text-decoration:none;}
	.poi{cursor:pointer;}
	.group{zoom:1;}
	.group:after{clear:both;content:"\200B";display:block;height:0;}
	.text_hide{line-height:999em;overflow:hidden;}
	.text_overflow{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
	html{ms-text-size-adjust:100%;webkit-text-size-adjust:100%;}
	body{color:#222222;font-family:"Helvetica Neue", Helvetica, Arial, sans-serif;line-height:1.6;}
	body, h1, h2, h3, h4, p, ul, ol, dl, dd{margin:0;}
	body{background-color:#e1e0de;}
	.page_msg{text-align:center;text-shadow:0 1px 0 rgba(255, 255, 255, 0.75);}
	.page_msg .inner{padding:50px 20px;}
	.page_msg .msg_icon_wrapper{font-size:0;}
	.page_msg .msg_content{padding-top:20px;}
	.page_msg h4{color:#000000;font-size:16px;}
	.page_msg p{color:#5a5a59;font-size:14px;}
	</style>
</head>
<body>
	<div class="page">
		<div class="body">
			<div class="page_msg">
			  <div class="inner group">
			    <!--<span class="msg_icon_wrapper"><i class="icon_page_msg warn"></i></span>-->
			    <div class="msg_content">
			      <p>网络繁忙，登录失败！</p>
			    </div>
			  </div>
			</div>
		</div>
	</div>
</body>
</html>