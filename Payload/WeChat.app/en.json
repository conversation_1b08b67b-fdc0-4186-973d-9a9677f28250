{"BindCard_Gender_Female": "Female", "BindCard_Gender_Male": "Male", "Choose_Deposit_Time": "Set Time", "Choose_Payment": "Select Deposit Method", "Continue_Pay": "Continue Payment", "Day": "{}", "Each_Day_In_Month_Deposit": "{} of every month", "Each_WeekDay_Deposit": "Every {}", "ExposureInfo_Waiting_Wording": "Please wait...", "Fetch_Balance": "Withdraw Balance", "Fetch_Balance_Bank_Proccessing": "Bank is processing", "Fetch_Balance_Open_Order": "Request Withdrawal", "Fetch_Balance_Success": "Arrived successfully", "FillCard_Info_ErrorTips_Format": "{} ({} errors total)", "FillCard_Number_Default_Mobile_Modify_Tips": "If the mobile number is incorrect, tap to edit.", "FillCard_Number_Reg_Hint": "Your bank card number", "FillCard_Number_Unreg_Hint": "___<BRAND>___ account owner's bank card number", "Friday": "Friday", "Give_Up": "Abandon", "HHC_Check_PWD_To_Add_Plan": "Enter payment password to start your Spend & Save", "HHC_Check_PWD_To_Edit_Plan": "Enter payment password to modify your Spend & Save", "HHC_Check_PWD_To_Pause_Plan": "Enter payment password to suspend Spend & Save", "HHC_Check_PWD_To_Start_Plan": "Enter payment password to enable Spend & Save", "HHC_Choose_Payment": "Choose card", "HHC_Deposit_Plan": "Deposit Schedule", "HHC_Did_Modify": "Modified", "HHC_Did_Open": "Started", "HHC_Did_Pause": "Suspended", "HHC_Name": "Spend & Save", "HHC_Plan_Check_Amount": "Invalid amount. Check and try again.", "HHC_Plan_Set_Bank_Card_Tip": "To use Spend & Save, choose deposit card first.", "LQT_Fixed_Deposit": "Recurring De<PERSON><PERSON>t", "LQT_Fixed_Deposit_Check_PWD_To_Add_Plan": "Enter payment password to enable recurring deposit", "LQT_Fixed_Deposit_Check_PWD_To_Delete_Plan": "Enter payment password to delete schedule.", "LQT_Fixed_Deposit_Check_PWD_To_Pause_Plan": "Enter payment password to suspend the schedule", "LQT_Fixed_Deposit_Check_PWD_To_Start_Plan": "Enter payment password to enable schedule.", "LQT_Fixed_Deposit_Did_Delete": "Deleted", "LQT_Fixed_Deposit_Did_Modify": "Updated", "LQT_Fixed_Deposit_Did_Open": "Enabled", "LQT_Fixed_Deposit_Did_Pause": "Suspended", "LQT_Fixed_Deposit_No_Plan": "No deposit schedule", "LQT_Fixed_Deposit_Plan": "Deposit Schedule", "LQT_Fixed_Deposit_Plan_Set_Bank_Card_Tip": "To set recurring deposit, select a card for deducting funds first.", "LQT_Fixed_Deposit_Plan_Set_Time_Tip": "To set recurring deposit, select a deposit time first.", "LQT_Fixed_Deposit_Plan_Should_Input": "To set recurring deposit, enter deposit amount first.", "ModifyPwdUseCase_ModifyPwd_Desc": "Enter payment password to verify identity", "ModifyPwdUseCase_ModifyPwd_GiveUp_Title": "Can<PERSON> changing your payment password?", "ModifyPwdUseCase_ModifyPwd_Success": "Password changed", "ModifyPwdUseCase_ModifyPwd_Title": "Change Password", "Monday": "Monday", "Monthly": "Monthly", "OfflinePay_CreateOfflinePay_ConfirmBtn_Title": "Enable Now", "OfflinePay_CreateOfflinePay_Euro_Tips": "You have not enabled payment function. Enable it and show the merchant your payment code to complete quick pay. (Only supports CNY transactions)", "OfflinePay_CreateOfflinePay_Tips": "You have not enabled payment function. Enable it and show the merchant your payment code to complete quick pay.", "OfflinePay_ReCreateOfflinePay_ConfirmBtn_Title": "Enable", "OfflinePay_ReCreateOfflinePay_Euro_Tips": "You need to re-enable payment function. Enable it and show the merchant your payment code to complete quick pay. (Only supports CNY transactions)", "OfflinePay_ReCreateOfflinePay_Tips": "You need to re-enable payment function. Enable it and show the merchant your payment code to complete quick pay.", "Saturday": "Saturday", "Sunday": "Sunday", "Thursday": "Thursday", "Tuesday": "Tuesday", "WCPay_BankCardBindTo0_0_5D_Detail": "$0.05 will be charged to verify the account", "WCPay_BankCardBindTo0_0_5D_Detail_back": "$0.05 will be charged to verify the account and refunded after verification.", "WCPay_BankCardBindTo1B_Detail": "¥0.01 will be charged to verify the account and refunded after verification.", "WCPay_BankCardBindTo1B_NotReturnDetail": "¥0.01 will be charged to your account to verify it can be used.", "WCPay_CountryCode_Title": "Country/Region", "WCPay_FaceID_Auth_Tip": "Verify your face to pay", "WCPay_GiveUpReset_Title": "Stop resetting payment password?", "WCPay_NeedChangeCard_Error_Btn": "Change payment method", "WCPay_TouchID_Auth_Tip": "Verify existing fingerprint to pay", "WCPay_TouchID_Confirm_Alert_Cancel": "Cancel", "WCPay_TouchID_Confirm_Alert_Content": "Fingerprint verified. Confirm payment?", "WCPay_TouchID_Confirm_Alert_OK": "Confirm", "WCPay_TouchID_Confirm_Alert_Title": "Pay", "WeChatPay_Name": "___<BRAND_Pay>___", "Wednesday": "Wednesday", "Weekly": "Weekly", "address_item_key": "Address", "address_item_place_holder": "Enter address", "agree": "Agree", "agree_user_protocal": "Accept User Agreement", "agreement_alert": "Please first view and agree to the \"User Agreement\".", "alertChargeFee": "Service Fee", "area_item_key": "Region", "ask_verify_fingerprint": "Verify fingerprint", "assign_pay_dialog_content": "The identity info entered differs from that linked to the current ___<BRAND>___ account. Use the ___<BRAND>___ account linked with your bank card and verify again.", "balance": "Balance", "bank_card_item_key": "Bank Cards", "bank_select_item_key": "Bank", "bind_new_card": "Add a Bank Card", "bind_new_card_section_footer": "For security reasons, currently linked cards will be unlinked.", "bind_new_card_section_header": "Link new card to retrieve account", "bind_new_card_to_pay_tip": "Enter payment password to verify identity", "bind_new_card_to_reset_mobile_desc": "Link a mobile number by linking a new card.", "binded_card_list_page_title": "Select mobile for SMS verification", "birth_date_item_key": "Birthdate", "can_not_bind_more_card": "Linked cards maximum reached.", "can_not_get_sms_with_question_mard_word": "Verification code not received?", "can_not_get_sms_word": "Verification code not received", "cancel_time": "Cancellation time: {}", "cannot_receive_sms_code_content": "SMS code has been sent to your mobile number registered with your bank. Make sure you are currently using this mobile number and the SMS code hasn't been blocked by any security apps. If you no longer use this number, contact your bank. For more help, call ___<OfficialEntity_Service>___ at +86-0755-95017.", "cannot_receive_sms_code_title": "Unable to Receive Verification Code", "card_holder_dialog_content": "1. To ensure the security of funds, a ___<BRAND_ID>___ can only link bank cards held in the same name.\n\n2. To link a card held in another name, you must update your real-name information.\n\n3. After changing real-name information, old cardholder information will be deleted and you will only be able to add cards in the new cardholder name.", "card_holder_dialog_title": "Name on card", "card_holder_item_key": "Cardholder", "card_holder_section_header": "Enter the information your previously provided to your bank. Only bank cards held in this name can be added in the future.", "card_info_section_header": "Enter card information", "card_num_item_key": "Card No.", "card_number_input_tips_title": "No fees or online banking required", "card_select_item_key": "Card Type", "card_type_section_header": "Select card type", "change_realname_word": "Change Real Name", "change_to_face_id": "Face Pay", "change_to_pwd": "Use Password", "change_to_touch_id": "Use Fingerprint", "check_pay_pwd_page_desc": "Enter your payment password to confirm your identity", "check_pay_pwd_page_title": "Identity Verification", "check_sms_desc": "Verification code has been sent to the mobile number you registered at your bank.\n\n1. Confirm that this is your current mobile number registered at your bank.\n\n2. Check if the SMS has been blocked by a security app on your mobile.\n\n3. If you cannot access this number right now, please contact your bank.\n\n4. For additional assistance, call Customer Service at 95017.", "check_sms_page_desc": "Linking bank card requires SMS verification. Verification code sent to phone {}. Follow the instructions.", "check_sms_page_favor": "You will pay {}{:.2f} (Saved {}{:.2f})", "check_sms_page_title": "Verify mobile number", "common_back": "Back", "common_cancel_word": "Cancel", "common_close": "Close", "common_done_word": "Done", "common_drop_out_word": "Quit", "common_i_know_word": "Got It", "common_more": "More", "common_next_word": "Next", "common_question_word": "FAQ", "common_select": "Select", "common_tip_word": "Reminder", "confirm_mobile_no": "Confirm Mobile", "confirm_pay": "Pay Now", "confirm_to_receive": "Confirm Receipt", "confrim_pay_and_open_deduct_word": "Pay & Enable", "confrim_pay_word": "Pay", "coupon_change_should_change_payment": "Payment amount has changed. Please reselect.", "coupon_component_need_bank_pay_tips": "Discount for Designated Payment Method", "cre_id_item_key": "ID number", "cre_id_item_place_holder": "Enter ID number", "cre_type_item_key": "ID type", "cre_type_item_place_holder": "Select ID type", "cvv_dialog_content": "The CVV is the 3 or 4 digit security code either on the back or front of your card.", "cvv_dialog_title": "What is the CVV?", "cvv_item_key": "CVV ", "cvv_item_place_holder": "3 or 4 digits usually on back of card", "default_delay_transfer_confirm_desc": "Transfer will arrive in {} hours", "email_item_key": "Email", "email_item_place_holder": "Enter email", "error_detail_title": "View Solution", "face_hongbao": "Packets Nearby", "fast_bind_card_support_bank_title_text": "Currently supports the following banks", "fill_card_and_user_info_title": "Card and Identity Information", "fill_card_info_card_holder_assign_pay_header": "Must be designated cardholder to pay.", "fill_card_info_page_favor_desc": "Use this kind of bank card to save an additional {}{:.2f}.", "fill_card_info_page_realname_cre_not_support": "Cannot use {} to link this card", "fill_card_info_page_title": "Enter card info", "fill_card_num_format_error": "Invalid card number", "fill_card_num_of_card_holder_section_header": "Enter the bank card number of the card holder", "fill_card_num_page_desc": "Link bank card", "fill_card_num_page_favor_dialog_title": "Save More With This Card", "fill_card_num_page_realname_desc": "You must add your bank card to complete real-name authentication.", "fill_card_num_page_sns_input_hint": "Only supports debit cards", "fill_card_num_page_title": "Add a Bank Card", "fill_card_number_assign_pay": "Use {}'s bank card to pay", "fill_card_number_more_favor": "Use the designated bank card to enjoy discounts", "fill_complete_name": "Enter full name", "fill_id_format_error": "ID number format is incorrect.", "fill_in_sms_key": "SMS Code", "fill_in_sms_word": "Enter verification code", "fill_phone_num_format_error": "Mobile number format is incorrect.", "finger_print_err_tips": "Retry", "first_name_item_key": "First Name", "first_name_item_place_holder": "Enter first name", "float_paydesk_modal_no_select_favor": "Unused Discounts", "foreign_mobile_header": "Enter new mobile", "forget_pay_pwd_title": "Forgot Payment Password", "get_sms_with_count_down_word": "Get Code\n({})", "get_sms_word": "Get Code", "give_up_on_new_card": "Stop linking card?", "give_up_this_order_or_not": "Abandon this transaction?", "group_aa": "Split Bill", "has_send_sms": "<PERSON><PERSON>", "has_send_sms_with_count": "Sent ({})", "hongbao_refund_way_header_title": "Red Packets not opened within 24 hrs after sending will be refunded in the below method.", "hongbao_refund_way_title": "Red Packet Refund To", "id_card_name": "ID card", "install_cert_error": "Failed to install certificate", "last_name_item_key": "Last Name", "last_name_item_place_holder": "Enter last name", "loading_title": "Loading...", "lottery_network_error": "Sorry, better luck next time!", "lqt": "Mini Fund", "lqt_reset_mobile_desc": "Select a bank card. Verify your Mini Fund using your mobile number held by that card's bank.", "mobile_dialog_content": "The mobile number registered on your bank account is the one you provided the bank when opening the account. If you didn't provide a mobile number to the bank, you have forgotten it, or it is no longer accessible, contact your bank and update the mobile number.", "mobile_dialog_title": "Mobile numbers", "mobile_item_key": "Mobile", "mobile_item_place_holder": "Enter mobile number held by bank", "name_item_key": "Name", "name_item_place_holder": "Enter name on card", "nationality_item_key": "Country/Region", "nationality_place_holder": "Enter country/region", "new_mobile_item_key": "New mobile number", "new_mobile_item_place_holder": "Enter mobile number held by bank", "new_user_card_num_input_safety_desc": "Link a card belonging to the ___<BRAND>___ account owner", "new_user_card_num_input_safety_desc_v2": "Link a card belonging to the ___<BRAND>___ account owner", "no": "No", "offline_choose_payment": "Select Default Payment Method", "offline_choose_payment_fail": "If your default payment method is unsuccessful, other methods will be attempted to complete payment.", "offline_click_view_code": "Tap to view payment code number", "offline_pay_modify_limit": "Edit Amount", "offline_pay_only_pay_title": "Pay", "offline_pay_select_card_invalid": "Currently unable to use the selected payment method. Try another payment method.", "offline_pay_title": "Money", "offline_pay_to_merchant": "Pay Merchant", "offline_prefer_payment": "Default payment method", "offline_show_code_warning": "Only use the payment code number to show cashiers when paying. For your security, don't share it with anyone else.", "offline_view_code_warning": "Only use the payment code number to show cashiers when paying. For your security, don't share it with anyone else.", "ok": "OK", "order_address_section_header": "Billing address", "pay_card_detail_contact_user_info": "Contact Customer Service: ", "pay_card_detail_tel_number": "95017", "pay_power_by_tenpay_word": "Provided by Ten<PERSON>y", "pay_success": "Payment successful", "paydesk_coupon_page_title": "Payment Discounts Page", "paydesk_float_page_title": "Payment Password", "paydesk_main_page_more_favor": "More discounts", "paydesk_main_page_title": "Payment Page", "paydesk_payment_page_title": "Payment Card List", "paydesk_sub_page_title": "Payment Subpage", "payee_remark_title": "Payer Remarks", "paying_alert_tips": "Payment already submitted. Wait for the payment result message to check if you need to resubmit payment.", "payment_method": "Payment Method", "paysecurity_digital_cert_not_install": "Not Enabled", "phone_number_item_key": "Phone", "phone_number_item_place_holder": "Enter phone number", "profession_item_key": "Occupation", "pure_bind_card_succ_tips": "Linked successfully", "pwd_repeat_error_tip": "Passwords do not match", "rebind_bank_card_section_header": "Link the card again to retrieve your account", "receipt": "Receive Money", "receive_done": "Received", "receive_remark_title": "Receipt Remarks", "receive_time": "Time of receipt: {}", "receiver_remark_title": "Payee Remarks", "refund_doing_tip": "Processing refund. The amount will be refunded to your card in 1-3 business days.", "refund_done": "Refunded", "refund_done_and_expired": "Returned (expired)", "refund_time": "Time returned: {}", "refund_transfer_or_not": "Return transfer from {}?", "refund_word": "Return", "renewal_time_item_key": "Times Changed", "resend_message_or_not": "Resend this message?", "resend_sms": "Resend", "resend_sms_with_count": "Resend ({})", "resend_success_tip": "Message resent", "resend_word": "Resend", "reset_ban_mobile_fill_card_info_credit_tip_header": "Enter bank card information for checking", "reset_ban_mobile_fill_card_num_tip_header": "Add a new bank card and use the mobile number held by your bank to verify Balance payments.", "reset_cvv_and_valid_date_tip": "You are updating linked card info and making a payment at the same time. If you are unsure of anything, call your bank's customer service: ", "reset_cvv_title": "Change CVV", "reset_lqt_mobile_fill_card_num_tip_header": "Add a new card and use that card's registered mobile number to complete SMS verifications for Mini Fund.", "reset_mobile_bank_card_number": "Card", "reset_mobile_card_desc_format": "{}{} ({}) registered mobile number", "reset_mobile_card_desc_with_update_format": "{}{} ({}) registered mobile number. ", "reset_mobile_new_mobile_info_btn": "Details", "reset_mobile_new_mobile_number": "New Mobile", "reset_mobile_phone_page_title": "Edit mobile number", "reset_phone_tip": "You can pay once identity is verified. To confirm the phone number held by your bank, call ", "reset_pwd_fill_rebind_card_info_page_title": "Enter bank card information", "reward": "Reward Code", "safety_dialog_content": "Security measures: Account protection, real-time monitoring, emergency freezing. \n\nTwo-step verification: Your payment password is required for each payment. SMS verification is required for large payments. \n\nPrivacy protection: Strong data encryption is used to protect user data. \n\nPayment insurance: Payments are insured by PICC.", "safety_dialog_title": "Security protections", "scan_card_num_title": "<PERSON><PERSON>", "select_payment": "Select payment method", "select_payment_card": "Select Payment Method", "send_verify_code_btn_wording": "Send", "send_verify_code_switch_btn_wording": "Change verification method", "send_verify_code_tips_format": "SMS verification code will be sent to: \n{}", "set_pay_pwd_confirm_page_title": "Re-enter to confirm", "set_pay_pwd_page_desc": "Set a ___<BRAND_Pay>___ password to verify your payments", "set_pay_pwd_page_title": "Set Payment Password", "set_pwd_success": "Settings updated", "succ_page_open_biometric_cancel_btn_title": "Not Now", "succ_page_open_biometric_dialog_content": "You can enable face or fingerprint pay to make payments faster.", "succ_page_open_biometric_faceid_btn_title": "Face Pay", "succ_page_open_biometric_touchid_btn_title": "Fingerprint Pay", "succ_page_open_face_id_dialog_content": "Enable Face Pay to use face recognition to complete payments quickly and securely.", "succ_page_open_face_id_right_btn_title": "Enable Face Pay", "succ_page_open_touch_id_dialog_content": "Enable Touch Pay to use fingerprint recognition to complete payments quickly and securely.", "succ_page_open_touch_id_left_btn_title": "Maybe Later", "succ_page_open_touch_id_right_btn_title": "Enable Touch Pay", "to_be_confirm_receive": "Receipt not confirmed", "transfer": "Transfer", "transfer_account": "Transfer amount", "transfer_amount_input_invalid_hint": "Amount entered is incorrect", "transfer_bank": "Transfer to Bank Card", "transfer_explain": "Add transfer note", "transfer_modify_explain": "Change", "transfer_second_left_button": "Cancel", "transfer_second_right_button": "Continue", "transfer_second_title": "Transfer reminder", "transfer_time": "Transfer time: {}", "transfer_ui_title": "Transfer to Friend", "understand_safety": "Security protections", "update_word": "Update", "user_card_type_select_placeholder_v2": "Select bank card and card type", "user_info_section_header": "Enter personal information", "user_protocal": " \"User Agreement\"", "valid_date_item_key": "Expiration", "verify_cre_tip": "Enter the last 4 digits of {}'s {} to verify identity", "verify_fingerprint_fail": "Fingerprint verification failed", "verify_id_ui_true_name_tips": "{} (Enter full name)", "wechat_bank_agreement": "Bank Agreement", "wechat_mobile_phone_word": "Mobile linked to ___<BRAND>___", "wechat_user_agreement": "___<BRAND_Pay>___ User Agreement", "wxp_common_cancel": "Cancel", "wxp_common_confirm": "OK", "wxp_common_i_know": "Got It", "wxp_common_remind": "Reminder", "wxp_network_error": "System is busy. Try again later.", "wxp_payment_network_error": "Transaction submitted. You will receive a payment status notification from the ___<BRAND_Pay>___ Official Account. Confirm payment status before resubmitting the payment if necessary.", "wxp_system_error": "System is busy. Try again later.", "wxp_wcpay_system_error": "System is busy. Try again later.", "yes": "Yes", "zip_item_key": "Post code", "zip_item_place_holder": "Enter post code", "common_confirm_word": "Confirm", "ModifyPwdUseCase_ModifyPwd_GiveUp_Yes": "Discard Changes", "ModifyPwdUseCase_ModifyPwd_GiveUp_No": "Continue", "disagree": "Don't Agree", "card_user_agreement": "User Service Agreement", "card_bank_agreement": "Bank Agreement", "Card_UserAgreement_Title": "To add bank cards, it is necessary to agree to the terms in the agreement below.", "pay_settings_delay_transfer_page_title": "Transfer Time", "pay_settings_delay_transfer_page_desc": "Once accepted, the funds will be deposited to the other user's Balance by the following time. Transfer cannot be recalled after sending, so check the payee's information carefully before transferring.", "pay_settings_delay_transfer_no_delay": "Instant", "pay_settings_delay_transfer_two_hour": "In 2 hours", "pay_settings_delay_transfer_one_day": "In 24 hours", "pay_settings_biometric_pay_enabled": "Enabled", "pay_settings_biometric_pay_disabled": "Disabled", "pay_settings_biometric_pay_multi_support_title": "Face/Fingerprint Pay", "pay_settings_biometric_pay_faceid_enabled": "Face pay enabled", "pay_settings_biometric_pay_touchid_enabled": "Fingerprint pay enabled", "pay_settings_biometric_pay_multi_support_desc": "After enabling, you can use face or fingerprint verification to make payments faster.", "f2f_pay_extrabuy_detail_modal_original_price": "(Original price ¥{:.2f})", "common_button": "<PERSON><PERSON>", "common_check_box_uncheck": "Check box, unselected", "common_check_box_check": "Check box, selected", "Accessibility_Type_Button": "{}, button", "Accessibility_Type_CheckBox_Selected": "Check box, selected, {}, button", "Accessibility_Type_CheckBox_UnSelected": "Check box, unselected, {}, button", "Accessibility_Type_Selected": "Selected, {}, button", "Accessibility_Type_UnSelected": "Unselected, {}, button", "Accessibility_String_PwdInputView": "Password box, 6 digits in total, {} digits entered", "Accessibility_Type_SwitchView_Selected": "{}, switch button, enable", "Accessibility_Type_SwitchView_UnSelected": "{}, switch button, disable", "YunShanFu_Loading_Wording": "Opening QuickPass...", "YunShanFu_Uninstalled_Error": "You have not installed QuickPass yet. Please install it and try again, or continue payment with ___<BRAND_Pay>___", "OfflinePay_Setting_CloseOfflinePay_AlertMessage_ShowFeaturePassword": "When security lock is enabled, selected unlock method is required to access Quick Pay, helping keep your payments secure. Disable?", "OfflinePay_Setting_CloseOfflinePay_AlertMessage": "Disable Quick Pay?", "OfflinePay_Setting_CloseOfflinePay_BtnTitle": "Disable", "OfflinePay_Setting_CloseOfflinePay_Cancel": "Continue", "OfflinePay_Setting_CloseOfflinePay_OpenWalletLock": "Set Security Lock", "Wallet_Mix_Paid_UnKnown_Error": "Transaction request submitted. You will receive a status notification from the ___<BRAND_Pay>___ Hong Kong Official Account. Don't resubmit payment until the payment status has been confirmed.", "bank_card_info": "Note", "HHC_Did_Add": "Added", "VoiceOver_OfflinePay_barCode": "Payment Barcode, can be shown to the cashier. Double-tap to show full-screen payment code", "VoiceOver_OfflinePay_barCode_short": "Payment Code", "VoiceOver_OfflinePay_Qrcode": "Payment QR Code", "VoiceOver_OfflinePay_barcode_clickHint": "Double-tap to go back", "VoiceOver_OfflinePay_Qrcode_clickHint": "Double-tap to show in full-screen", "OfflinePay_Close_WalletLock_HalfDialog_Title": "Disable Payment Code", "OfflinePay_Close_WalletLock_HalfDialog_Content": "For the security when using payment code, you can set security lock. After setting, security verification will be required when you use payment code.", "Wallet_Lock_Default_Title": "Security Lock", "Wallet_Lock_FaceLock": "Face Unlock", "Wallet_Lock_TouchLock": "Fingerprint Unlock", "Wallet_Lock_BioLock": "Face/Fingerprint Unlock", "Wallet_Lock_PatternLock": "Pattern Password Unlock", "Wallet_Lock_PatternLock_Modify_Verify_Title": "Enter old pattern password", "Wallet_Lock_PatternLock_Modify": "Change Pattern Password", "Wallet_Lock_PatternLock_Modify_SubTltle": "Set a new pattern password", "Wallet_Lock_Close_Tips": "When security lock is disabled, no unlock method is needed to access \"Me\" > \"Services\".", "Wallet_Lock_TouchLock_Verify_Title": "Verify Touch ID to continue", "Wallet_Lock_FaceLock_Verify_Title": "Verify Face ID to continue", "Wallet_Lock_PatternLock_Verify_Title": "Enter pattern password", "Wallet_Lock_Verify_byPwd": "Payment Password Verification", "Wallet_Lock_Verify_Btn_FaceID": "Verify face", "Wallet_Lock_Verify_Btn_TouchID": "Verify fingerprint", "Wallet_Lock_PatternLock_Setup_Title": "Set a pattern password", "Wallet_Lock_PatternLock_Reset_Title": "Forgot Pattern Password?", "Wallet_Lock_PatternLock_Confirm_Title": "Enter again for confirmation", "Wallet_Lock_PatternLock_Confirm_Error_Tips": "Unmatched pattern. Set again.", "Wallet_Lock_PatternLock_Verify_Error_Tips": "Incorrect pattern. {} chance(s) left.", "Wallet_Lock_PatternLock_Verify_Limit_Tips": "Too many attempts. Try again after {} minute(s).", "Wallet_Lock_PatternLock_Modify_AfterVerify_Title": "Set a new pattern password", "Wallet_Lock_PatternLock_InvalidPattern_Tips": "At least 4 points are required. Set again.", "Wallet_Lock_PatternLock_Setup_Succ_Tips": "Pattern password set", "Wallet_Lock_PatternLock_Modify_Succ_Tips": "Pattern password changed", "Wallet_Lock_PatternLock_Setup_Cancel_Tips": "Do not enable pattern lock?", "Wallet_Lock_PatternLock_Setup_Cancel_LeftBtn": "Enable", "Wallet_Lock_PatternLock_Setup_Cancel_RightBtn": "Not Now", "Wallet_Lock_Not_Support_TouchID_Tip": "Touch ID not available on this device. Reset security lock.", "Wallet_Lock_Not_Support_FaceID_Tip": "Face ID not available on this device. Reset security lock.", "Wallet_Lock_Close_Wallet_Lock_Tip": "Disable Security Lock", "Wallet_Lock_Setup_Pattern_Lock_Tip": "Set Pattern Password", "Wallet_Lock_TouchID_NotEnrolled_Tip": "Touch ID not enabled. Enable Touch ID in system settings or reset security lock.", "Wallet_Lock_FaceID_NotEnrolled_Tip": "Face ID not enabled. Enable Face ID in system settings or reset security lock.", "Wallet_Lock_FingerPrint_NotEnrolled_Tip": "No fingerprint in system. Save your fingerprint and set security lock again.", "Wallet_Lock_PatternLock_VerifyBlock_Tip": "Too many attempts. Verify your identity in \"Reset Pattern Password\" or try again in 10 minutes.", "Wallet_Lock_PatternLock_VerifyBlock_Reset_Tip": "Reset Pattern Password", "Wallet_Lock_New_FingerPrint_Authen_Tips": "New fingerprint entered. Enter payment password to verify identity.", "Wallet_Lock_New_TouchID_Authen_Tips": "Fingerprint information on this device has changed. Enter payment password to verify your identity.", "Wallet_Lock_New_FaceID_Authen_Tips": "Facial information on this device has changed. Enter payment password to verify your identity.", "Wallet_Lock_Forget_Pwd": "Forgot Password", "Wallet_Lock_Retry_Pwd": "Retry", "common_link": "Link", "Accessibility_Collapse_Header_Collapsed": "{}, collapsed", "Accessibility_Collapse_Header_Showed": "{}, expanded", "Pay_Android_Fingerprint_Prompt_Title": "Verify fingerprint", "Pay_Android_Fingerprint_Prompt_SubTitle": "to complete payment.", "Pay_Android_Fingerprint_Prompt_Button": "Use Password", "Accessibility_Collapse_Header_Collapsed({}": "Showing Less", "Accessibility_Collapse_Header_Showed({}": "Showing All", "Accessibility_State_Disabled": "Darken", "OfflinePay_ChangePayment_UnConnectedNetwork_Tips": "Network unavailable. Unable to select payment method.", "OfflinePay_EnablePage_UnConnectedNetwork_Tips": "Network unavailable. Try again later.", "OfflinePay_Banner_Use_Tips": "Payment Code", "OfflinePay_Banner_Expand_Tips": "Expand", "OfflinePay_Banner_Collapse_Tips": "<PERSON>de", "Fetch_Balance_To_Bank": "Withdrawn to", "Fetch_Balance_Amount": "Withdrawal amount", "Fetch_Balance_Amount_Tips": "Balance: ¥{}.", "Fetch_Balance_Amount_Exceed": "Amount entered exceeds available Balance", "Fetch_Balance_Fetch_All": "Withdraw All", "HoneyPay_CheckPwd_Unbind_Title": "Unlink Relative Card", "HoneyPay_Modify_CreditLimit_Title": "Edit monthly limit", "HoneyPay_Modify_CreditLimit_Desc": "Monthly limit", "HoneyPay_Modify_CreditLimit_Max_Alert": "Amount cannot exceed ¥{:.2f}", "balance_entry_balnce_title": "My Balance", "balance_entry_balnce_detail": "Transactions", "balance_entry_powered_by_tenpay": "Provided by Ten<PERSON>y", "balance_recharge_page_title": "Top Up", "balance_recharge_card_info_title": "Top-Up Method", "balance_recharge_payment_new_card": "Add New Card", "HoneyPay_Add_Card": "Gift a Relative Card", "HoneyPay_Select_Contact_Title": "Select Contacts", "HoneyPay_Modify_Comment": "Edit Comment", "HoneyPay_MoneyInput_Hint": "Enter amount", "HoneyPay_CreateCard_Btn": "Send", "HoneyPay_Max_Amount_Notice": "Amount cannot exceed ¥{:.2f}", "HoneyPay_Modify_Credit": "Monthly limit", "HoneyPay_Main_Title": "Relative Card", "hbrefund_info_tips": "Remarks", "hbrefund_set_button": "Settings", "hbrefund_time_title": "Red Packet Refund Time", "hbrefund_forbid_way": "This refund method is no longer supported.", "hbrefund_had_set": "Set successfully", "hbrefund_origin_desc": "Red Packets not opened within 24 hrs after sending will be refunded to the original payment method.", "hbrefund_set_tips": "After setting, unaccepted money will be refunded to the original payment method. This cannot be switched to \"Refund to Balance\". Continue?", "Dcep_Loading_Wording": "Loading...", "Dcep_Uninstalled_Error": "You have not installed E-CNY. Please install it and try again, or continue payment with ___<BRAND_Pay>___", "TeenagerPayDetailUIPage_NotSet": "None", "TeenagerPayDetailUIPage_LimitedAmount": "¥{:.{}f}", "TeenagerPaySetLimitModal_AmountTitle": "Amount ", "TeenagerPaySetLimitModal_MaxAmount": "Enter up to 7 digits", "TeenagerPayGetDetailUseCase_LimitOn": "With Limit", "TeenagerPayGetDetailUseCase_LimitOff": "Without Limit", "TeenagerPayUseCase_Set_Ok": "Set already", "TeenagerPayUseCase_Close_Ok": "Amount limit disabled", "TeenagerPayUseCase_Limit_Max": "The payment limit per transaction cannot be greater than the daily payment limit.", "TeenagerPayUseCase_Limit_Min": "The daily payment limit cannot be less than the payment limit per transaction.", "TeenagerPayUseCase_Input_Accesibility": "Text bar", "transfer_to_bank_name_input_placeholder": "Payee Name", "transfer_to_bank_card_input_placeholder": "Payee Bank Card No.", "transfer_to_bank_bank_select_placeholder": "Select bank", "transfer_to_bank_arrival_time_select_title": "Time", "transfer_to_bank_arrival_time_modal_title": "Select Transfer Time", "transfer_to_bank_arrival_time_modal_desc": "After you request transfer, the funds will be deposited in the payee's account by the following time.", "transfer_to_bank_history_page_title": "Select Payee", "transfer_to_bank_history_page_empty_prompt": "No previous payees", "transfer_to_bank_history_me_section_title": "Me", "transfer_to_bank_history_others_section_title": "Previous Payees", "transfer_to_bank_history_modify_remark_action": "Note", "transfer_to_bank_history_set_remark_title": "Add a Note", "transfer_to_bank_history_delete_action": "Delete", "transfer_to_bank_bank_unavailable_alert": "Bank undergoing maintenance. Transfers not currently available.", "transfer_to_bank_money_input_title": "Amount", "transfer_to_bank_info_receiver_format": "Payee: {}", "transfer_to_bank_info_charge_fee": "Service Fee", "transfer_to_bank_info_charge_fee_rate_format": "(rate: {:.2f})", "transfer_to_bank_info_total_amount": "Total", "transfer_to_bank_info_transfer_explain": "Remark", "transfer_to_bank_info_transfer_explain_edit_hint_format": "Visible to both payer and payee. <PERSON> {} characters.", "transfer_to_bank_info_add_transfer_explain": "Add a Note", "transfer_to_bank_info_detail_title": "Details", "transfer_to_bank_info_detail_current_state": "Status", "transfer_to_bank_info_detail_paid_success": "initiated a transfer", "transfer_to_bank_info_detail_withdrawn_success": "Withdrawal completed", "bankcard_detail": "{} End digits {}", "bankcard_qmf_detail": "{} Relative Card sender, {}", "FaceCheck_Agreement_title": "Face Verification", "FaceCheck_Success_title": "Verified", "FaceCheck_Result_Retry": "Try again", "TabBar_NewBadge": "New", "common_delete_alert_title": "Confirm deletion?", "common_delete": "Delete", "HoneyPay_PrepareCardUI_Title": "Set Relative Card", "none": "None", "mobile_item_key_bank": "Mobile number registered with bank", "mobile_item_place_holder_short": "Enter mobile number", "FillCard_Number_Default_Mobile_Modify_Tips_New": "The mobile number linked previously has been automatically filled in. You can modify it as needed.", "HoneyPay_MoneyInput_Hint_New": "Enter amount", "AddPayCard_No_Card_Bind_Card_Title": "Add without entering card number", "AddPayCard_Manual_Bind_Card_Title": "Add by entering card number", "FillCard_Number_Reg_Hint_V3": "Enter {}'s bank card number", "FillCard_Number_Reg_Hint_Self": "Enter your card number", "FastBindCardSelectBankUI_Title": "Add without entering card number", "FastBindCardSelectBankUI_Search_Hint": "Search in {} bank(s)", "FastBindCardSelectBankUIV2_Title": "Linking without entering card number", "FastBindCardSelectBankUIV2_Search_Hint": "Search {} bank(s)", "qrcode_collection_settings": "Receiving Settings", "qrcode_collection_amount": "Amount", "qrcode_collection_remark": "Instructions for Receiving Money", "FillCardNumberV2_CountryCode_Hint": "Enter country/region code", "FillCardNumberV2_CountryCodeView_Hint": "Please Select", "paydesk_main_page_not_use_favor": "Do not use offers.", "WCPay_Digital_Cert_Desc_Not_Install": "Digital certificate not enabled", "WCPay_Digital_Cert_Desc_Already_Install": "Digital certificate enabled", "WCPay_Digital_Cert_Manage_Content_Desc": "Enabling a digital certificate on your device:", "WCPay_Digital_Cert_Manage_Content_Desc_1": "• Makes payments from your device even more secure", "WCPay_Digital_Cert_Manage_Content_Desc_2": "• Raises your daily limit for payments ", "WCPay_Digital_Cert_Install_Button_Title": "Enable", "WCPay_Digital_Cert_Delete_Button_Title": "Disable", "WCPay_Digital_Cert_Install_List_desc": "Devices that have enabled certificate", "WCPay_Digital_Cert_Delete_Confirm_Content": "Are you sure you want to disable the digital certificate for the current___<BRAND_ID>___on the device?", "WCPay_Digital_Delete_Confirm_Btn_Title": "Disable", "WCPay_Digital_Cert_Install_Action_Title": "Verify identity", "WCPay_Digital_Cert_Install_Action_Desc": "Identity verification required before enabling certificate", "WCPay_Digital_Cert_Install_Input_Title_default": "ID Card", "WCPay_Digital_Cert_Install_Input_Desc_default": "Enter ID number", "WCPay_Digital_Cert_Install_Input_Desc": "Enter the ID number of {}", "WCPay_Digital_Cert_Verify_Button_Title": "Verify", "WCPay_Digital_Cert_Install_Sccuess": "Verified", "WCPay_Digital_Cert_Delete_Succ_Toast": "Disabled", "LQT_Purchase_Page_Title": "Transfer In", "LQT_Purchase_Card_Info_Title": "Transfer Method", "LQT_MonetInputOutOfRange_Tips": "Insufficient balance. Top up and try again.", "LQT_Limit_Cashier_Modal_Balance_Desc": "Amount in Balance", "LQT_Limit_Cashier_Modal_LQT_Desc": "Mini Fund", "LQT_Limit_Cashier_Modal_Transfer_In_Tips": "Transfer In", "LQT_Limit_Cashier_Modal_Confirm_Button_Title": "Got It", "LQT_SaveAmountLargeThanBankAvaible_Tips": "The amount you entered exceeds the bank's payment limit", "LQT_Redeem_Card_Info_Title": "Withdraw To", "LQT_Redeem_Page_Title": "Withdraw", "LQT_Redeem_Confirm_View_Desc": "Withdraw", "LQT_Redeem_Balance_Amount": "Mini Fund Balance ¥{:.2f}", "LQT_Redeem_Balance_Insufficient": "Insufficient Mini Fund Balance", "LQT_Redeem_Balance_Fetch_All": "Withdraw All", "LQT_Loading_Card_Data": "Obtaining bank card list", "LQT_Loading_LQT_Amount": "Obtaining Mini Fund balance", "LQT_Loading_LQ_Amount": "Obtaining amount in Balance", "LQT_PerRedeem_Invalid_Default_Tips": "Amount of single transaction exceeds limit", "LQT_Redeem_Fetch_Amount_Large_Than_Bank_Avaible": "The maximum amount per transaction is ¥{:.2f}. You can split it into multiple transactions.", "LQT_Redeem_Fetch_Amount_Large_Than_Max_Amount_Tips": "Learn more", "HoneyPay_Record_Receive_Title": "Relative Card to me", "HoneyPay_Record_Donate_Title": "Relative Card from me", "LQTDetail_balance_Accessibility": "Balance ¥{:.2f}", "WCPay_LQT_OnClickPurchase_Fail_Network_Error": "Network error. Unable to obtain your bank card list. Try again later.", "WCPay_LQT_OnClickRedeem_Fail_Network_Error": "Network error. Unable to obtain your bank card list. Try again later.", "WCPay_LQT_PurchaseFund_Fail_Network_Error": "Unable to purchase. Try again later.", "WCPay_LQT_QryPurchaseResult_Fail_Network_Error": "Unable to check purchase result", "WCPay_LQT_PreRedeemFund_Fail_Network_Error": "Unable to place redemption order. Try again later.", "WCPay_LQT_RedeemFund_Fail_Network_Error": "Unable to redeem funds. Try again later.", "WCPay_LQT_MoneyVC_Save_TextFieldTips": "Transfer-in Amount", "WCPay_LQT_MoneyVC_Fetch_TextFieldTips": "<PERSON><PERSON><PERSON> Amount", "LQT_Purchase_Keyboard_Confirm_Title": "<PERSON><PERSON><PERSON><PERSON>", "LQT_Redeem_Keyboard_Confirm_Title": "Withdraw", "LQT_Detail_Operation_More_Product_Title": "More Products", "pay_settings_biometric_pay_touchid_title": "Fingerprint Pay", "pay_settings_biometric_pay_faceid_title": "Face Pay", "pay_settings_biometric_pay_multi_title": "Face/Fingerprint Pay", "pay_settings_biometric_pay_touchid_desc": "When enabled, you can use fingerprint verification to make payments faster.", "pay_settings_biometric_pay_faceid_desc": "When enabled, you can use face verification to make payments faster.", "pay_settings_biometric_pay_multi_desc": "Enable face or fingerprint pay to make payments faster.", "pay_settings_biometric_pay_enable_faceid": "Enable Face Pay", "pay_settings_biometric_pay_enable_touchid": "Enable Fingerprint Pay", "common_reddot_accessibility": "You've got new messages", "common_help": "Help", "renewal_or_sign_time_item_key": "Issuances/Permit Renewals", "WCPay_Option_Item": "Optional", "bind_new_card_input_name": "Enter name registered with bank", "paydesk_title_accessibility_selected": "Selected", "VoiceOver_OfflinePay_Unselected": "Unselected", "RedDot_New": "NEW", "common_continue": "continue", "WCPay_Risk_Dialog_Title": "Potential risk has been detected. Please confirm whether you would like to continue. If yes, your identity will need to be verified.", "WCPay_Risk_Not_Support_Dialog_Title": "Potential risk has been detected. Please resolve the risks before proceeding.", "WCPay_Risk_Failed_Dialog_Title": "Identity verification failed, operation has to be terminated Close", "bind_card_agreement_protocal_and_next": "Agree and Add", "wallet": "Wallet", "awaiting_real_name_verification": "Pending identity verification", "five_stars": "*****", "Card_UserAgreement_Read_And_Agreement_Title": "Consent required when adding", "FaceCheck_Common_Error": "System busy. Try again.", "FaceCheck_MP_Request_Use": " Request access", "FaceCheck_MP_Confirm_Tip": "___<BRAND>___ uses face recognition to verify your identity. This must be set by the account holder only.", "FaceCheck_MP_Front_Feedback": "Help Center", "FaceCheck_Recoging": "Recognizing...", "waiting_for_real_name_authentication": "Pending identity verification", "collect_sub_title": "Amount", "collect_main_add_desc_title_simple_change": "Change", "collect_main_add_desc_title": "Add Note", "remittance_amount_lowest_limit": "The lowest transfer amount is ￥0.01.", "collect_main_fixed": "Set Amount", "collect_main_first_enter_tips_title": "Receiving Money", "collect_main_first_enter_tips_new": "Money received will be deposited in your ___<BRAND_Balance>___ (\"Me\" > \"Services\" > \"Wallet\"), where it can be spent or withdrawn.", "collect_main_close_ring_tone": "Disable Receipt Alert Sound", "collect_main_close_ring_tone_tips": "Disabled", "collect_main_open_ring_tone": "Enable Receipt Alert Sound", "collect_main_open_ring_tone_tips": "Enabled. Please check your media volume is on.", "collect_main_qrcode_usage_other": "Others", "collect_main_qrcode_usage_other_placeholder": "Add info (up to 16 characters)", "collect_main_payer_desc_default_placeholder": "Add a note for the recipient.", "collect_qrcode_save_failed": "Saving failed", "collect_material_guide_save_text_toast": "Saved to Album", "collect_mch_module_title": "Business QR Code", "collect_personal_module_title": "Individually-Owned QR Code", "collect_setting_title": "Receipt Settings", "collect_main_fixed_cancel": "Clear Amount", "collect_main_more_function": "More Settings", "collect_main_save_qrcode": "Save Payment Code", "collect_main_receive_title": "Total", "collect_main_paying": "Paying...", "collect_main_pay_suc": "Payment successful", "collect_main_pay_cancel": "Cancel Payment", "collect_main_loading_title": "Loading QR code...", "collect_main_ring_not_support": "Not supported on this system", "WCPay_Transfer_To_Format": "Transfer to {}", "WCPay_Transfer_Weixin_Alias_Prefix": "___<BRAND_ID>___:", "WCPay_Transfer_InputAmount_Tips": "Enter amount first", "WCPay_Transfer_Cashier_Desc_Format": "Transfer to {}", "WCPay_Transfer_Succ_Tips": "Awaiting receipt by {}", "WCPay_Service": "Services", "recognize_and_pay": "Scan and Pay", "bizf2f_input_ui_page_to_person": "Pay to Individual", "bizf2f_input_ui_page_add_remark": "Add Note", "bizf2f_input_ui_page_amount_title": "Payment Amount", "WCPay_Verify_Password_Get_SMS_Code": "Receive Verification Code", "WCPay_VerifyCode_Header_Description": "SMS verification required for the transaction.", "bizf2f_input_ui_page_pay_action": "Pay", "bizf2f_input_ui_page_change_remark": "Change", "bizf2f_input_ui_page_pay_title": "Pay", "bizf2f_favor_title": "Offers", "bizf2f_favor_total_fee": "Total Amount", "bizf2f_favor_calculating": "Calculating...", "bizf2f_favor_select_favor": "Select Discount", "UN_BIND_CARD_TITLE": "Unlink Bank Card", "WCPay_system_version_limitation_tip": "For more features, check on HarmonyOS 4.2 or earlier, or other devices.", "reconfirm_payment_amount_title": "Confirm payment amount again", "reconfirm_payment_amount_des": "For your asset security, confirm the amount to avoid mistakes.", "reconfirm_amount_page_tip": "Due to regulatory requirements, payments exceeding static QR code limits must be completed by scanning the dynamic QR code below. Tap the button to verify and complete payment.", "Hongbao_SendUI_NavigationBar_Title": "Send Red Packet", "Hongbao_SendUI_Send_Button_Titlle": "Prepare Red Packet", "Hongbao_SendUI_Count_Title": "Quantity", "Hongbao_SendUI_Amount_Title_Group": "Total", "Hongbao_SendUI_Amount_Title_Single": "Amount", "Hongbao_SendUI_RandomLuckyMode_Title": "Random Amount", "Hongbao_SendUI_Count_Tips": "Enter quantity", "Hongbao_SendUI_Amount_Tips": "¥0.00", "Hongbao_SendUI_Default_Wishing": "Best wishes", "Hongbao_Per_Hongbao_Max_Amount_Format": "Up to {}CNY for each Red Packet", "HongBao_SendTips": "Sent Packets", "HongBao_OpenTips": "Open", "HongBao_AmoutTips": "CNY", "HongBao_MainTitle": "Red Packet", "HongBao_Cashier_Desc": "___<BRAND_hongbao>___", "Hongbao_SendUI_RandomMode_Title": "Random Amount", "Hongbao_SendUI_NormalMode_Title": "Identical Amount", "Hongbao_SendUI_ExclusiveMode_Title": "Exclusive", "Hongbao_SendUI_Select_Count_Suffix": "", "Hongbao_SendUI_Group_Member_Count_Format": "This group has {} members", "Hongbao_SendUI_Amount_Title_Group_Normal": "Amount Each", "Hongbao_SendUI_Perperson_Maxvalue_Error_Tips": "Up to {}CNY for each Red Packet", "Hongbao_SendUI_Perperson_Minvalue_Error_Tips": "At least {:.2f} for each Red Packet", "Hongbao_SendUI_Count_Larger_Than_Group_Mem_Count_Error_Tips": "Number of Red Packets cannot exceed that of group members.", "Hongbao_SendUI_Total_Num_Error_Tips": "Maximum quantity is {}.", "Hongbao_SendUI_Select_Count_Data_Invalid_Error_Tips": "\"Quantity\" not entered", "Hongbao_SendUI_Amount_Data_Invalid_Error_Tips": "\"Total\" is not entered", "Hongbao_SendUI_Select_Count_Zero_Error_Tips": "Select the quantity.", "Hongbao_SendUI_Amount_Larger_Than_Max_Amount_Error_Tips": "Total amount cannot exceed {}CNY.", "Hongbao_ReceiveModal_Detail_Link": "View details", "Hongbao_DetailUI_Load_More_Text": "Click to load more", "TransferPhone_Entry_Title": "Select Transfer Method", "TransferPhone_To_Bank_Title": "Transfer to Bank Card", "TransferPhone_To_Bank_Desc": "Enter payee's bank card to transfer to their bank account.", "TransferPhone_To_Phone_Title": "Transfer to Mobile Number", "TransferPhone_To_Phone_Desc": "Enter payee's mobile number to transfer to their ___<BRAND_Balance>___.", "TransferPhone_To_PaySetting": "Mobile Number Transfer Settings", "WCPay_ThirdParty_Tips_Title": "Disclaimer", "WCPay_Service_Manage": "Manage Services", "identify_and_pay": "Scan and Pay", "Hongbao_SendUI_Exclusive_Mode_Send_To_Who": "Send to", "Hongbao_SendUI_Exclusive_Mode_Send_To_Who_Error_Tips": "\"Send to\" not selected", "Hongbao_SendUI_Amount_Data_Invalid_Error_Tips_2": "\"Amount\" not entered", "MerchantPay_Input_Remark_Hint_Format": "Visible to payee. <PERSON> {} characters.", "MerchantPay_Input_Remark_Title": "Add Note", "MerchantPay_Transfer_To_Format": "Pay to {}", "Greeting_Hongbao_Random_Change_Amount": "Change Amount", "Greeting_Hongbao_Who_Receive_Hongbao_Format": "Opened by {}", "set_amount": "Set Amount", "Card_Record_Component_FQF_Left_Title": "Fund ___<Channels>___"}