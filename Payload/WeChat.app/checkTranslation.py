#!/usr/bin/python

import sys
import os
import glob
import zlib
import struct
import hashlib
import shutil

dictWord = {}

def processDirectory ( args, dirname, filenames ):
    for filename in filenames:
        dictWord.clear()
        fullPath = dirname + "/" + filename + "/mm.strings"
        if os.path.isfile(fullPath):
            contents = open(fullPath,'rb').readlines()
            chinesePath = dirname + "/zh_CN.lproj/mm.strings"
            chineseContents = open(chinesePath,'rb').readlines()
            for line in contents:
                endIndex = line.find("\"", 2)
                if endIndex > 0:
                    dictWord[line[:endIndex+1]] = 1
            for line in chineseContents:
                endIndex = line.find("\"", 2)
                if endIndex > 0 and line[:endIndex+1] not in dictWord.keys():
                    print filename, line[:endIndex+1]

def main(args):

    path = os.path.abspath('.')

    if len(args) >= 2:
       path = args[1]

    print path

    os.path.walk(path, processDirectory, None )

if __name__ == "__main__":
    main(sys.argv)
