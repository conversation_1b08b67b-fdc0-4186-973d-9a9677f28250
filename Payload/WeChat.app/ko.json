{"BindCard_Gender_Female": "여자", "BindCard_Gender_Male": "남자", "Choose_Deposit_Time": "시간 설정", "Choose_Payment": "입금 방법 선택", "Continue_Pay": "결제 계속", "Day": "{}", "Each_Day_In_Month_Deposit": "매달 {}", "Each_WeekDay_Deposit": "매 {}", "ExposureInfo_Waiting_Wording": "기다려 주십시오...", "Fetch_Balance": "출금 잔액", "Fetch_Balance_Bank_Proccessing": "은행업무 처리 중", "Fetch_Balance_Open_Order": "출금 요청", "Fetch_Balance_Success": "도착 완료", "FillCard_Info_ErrorTips_Format": "{} (총 {}개의 오류)", "FillCard_Number_Default_Mobile_Modify_Tips": "휴대폰 번호가 틀렸으면 눌러서 편집하십시오.", "FillCard_Number_Reg_Hint": "귀하의 은행 카드 번호", "FillCard_Number_Unreg_Hint": "___<BRAND>___ 계정 소유자의 은행 카드 번호", "Friday": "금요일", "Give_Up": "중지", "HHC_Check_PWD_To_Add_Plan": "지출 및 절약을 시작하려면 결제 암호를 입력하세요.", "HHC_Check_PWD_To_Edit_Plan": "지출 및 절약을 수정하려면 결제 암호를 입력하세요.", "HHC_Check_PWD_To_Pause_Plan": "지출 및 절약을 일시 중단하려면 결제 암호를 입력하세요", "HHC_Check_PWD_To_Start_Plan": "지출 및 절약을 사용 설정하려면 결제 암호를 입력하세요.", "HHC_Choose_Payment": "카드 선택", "HHC_Deposit_Plan": "입금 일정", "HHC_Did_Modify": "수정됨", "HHC_Did_Open": "시작됨", "HHC_Did_Pause": "일시 중단됨", "HHC_Name": "지출 및 절약", "HHC_Plan_Check_Amount": "유효하지 않은 금액입니다. 확인하고 다시 시도해 보세요.", "HHC_Plan_Set_Bank_Card_Tip": "지출 및 절약을 사용하려면 먼저 입금 카드를 선택하세요.", "LQT_Fixed_Deposit": "반복 입금", "LQT_Fixed_Deposit_Check_PWD_To_Add_Plan": "결제 암호를 입력해 반복 입금 사용", "LQT_Fixed_Deposit_Check_PWD_To_Delete_Plan": "일정을 삭제하려면 결제 암호를 입력하세요.", "LQT_Fixed_Deposit_Check_PWD_To_Pause_Plan": "일정을 일시 중단하려면 결제 암호를 입력하세요", "LQT_Fixed_Deposit_Check_PWD_To_Start_Plan": "일정을 사용하려면 결제 암호를 입력하세요.", "LQT_Fixed_Deposit_Did_Delete": "삭제됨", "LQT_Fixed_Deposit_Did_Modify": "업데이트 완료", "LQT_Fixed_Deposit_Did_Open": "사용", "LQT_Fixed_Deposit_Did_Pause": "일시 중단됨", "LQT_Fixed_Deposit_No_Plan": "입금 일정 없음", "LQT_Fixed_Deposit_Plan": "입금 일정", "LQT_Fixed_Deposit_Plan_Set_Bank_Card_Tip": "반복 입금을 설정하려면, 먼저 자금을 인출할 카드를 선택하세요.", "LQT_Fixed_Deposit_Plan_Set_Time_Tip": "반복 입금을 설정하려면, 먼저 입금 시간을 선택하세요.", "LQT_Fixed_Deposit_Plan_Should_Input": "반복 입금을 설정하려면, 먼저 입금 금액을 입력하세요.", "ModifyPwdUseCase_ModifyPwd_Desc": "신원 인증을 위해 결제 암호를 입력하십시오", "ModifyPwdUseCase_ModifyPwd_GiveUp_Title": "결제 암호 변경을 취소하시겠습니까?", "ModifyPwdUseCase_ModifyPwd_Success": "암호 변경 완료", "ModifyPwdUseCase_ModifyPwd_Title": "암호 변경", "Monday": "월요일", "Monthly": "매월", "OfflinePay_CreateOfflinePay_ConfirmBtn_Title": "지금 사용", "OfflinePay_CreateOfflinePay_Euro_Tips": "Quick Pay가 사용 설정되지 않았습니다. 이를 사용할 경우, 코드를 매장에 보여주고 빠르게 결제할 수 있습니다(CNY 거래만 지원됨).", "OfflinePay_CreateOfflinePay_Tips": "Quick Pay가 사용 설정되지 않았습니다. 이를 사용할 경우, 코드를 점원에게 보여주고 빠르게 결제할 수 있습니다.", "OfflinePay_ReCreateOfflinePay_ConfirmBtn_Title": "사용", "OfflinePay_ReCreateOfflinePay_Euro_Tips": "Quick Pay가 사용 설정되지 않았습니다. 이를 사용할 경우, 코드를 매장에 보여주고 빠르게 결제할 수 있습니다. CNY 거래만 지원됩니다.", "OfflinePay_ReCreateOfflinePay_Tips": "Quick Pay가 사용 설정되지 않았습니다. 이를 사용할 경우, 코드를 매장에 보여주고 빠르게 결제할 수 있습니다.", "Saturday": "토요일", "Sunday": "일요일", "Thursday": "목요일", "Tuesday": "화요일", "WCPay_BankCardBindTo0_0_5D_Detail": "$0.05가 계정 인증으로 공제됩니다", "WCPay_BankCardBindTo0_0_5D_Detail_back": "$0.05가 계정 인증으로 공제되며 인증 후 금액이 환불됩니다.", "WCPay_BankCardBindTo1B_Detail": "¥0.01가 계정 인증으로 공제되며 인증 후 금액이 환불됩니다.", "WCPay_BankCardBindTo1B_NotReturnDetail": "¥0.01가 계정 인증으로 공제됨", "WCPay_CountryCode_Title": "국가/지역", "WCPay_FaceID_Auth_Tip": "결제를 위해 얼굴을 인증합니다", "WCPay_GiveUpReset_Title": "결제 암호 재설정을 중단하시겠습니까?", "WCPay_NeedChangeCard_Error_Btn": "결제 방법 변경", "WCPay_TouchID_Auth_Tip": "결제를 위해 기존의 지문을 인증하십시오", "WCPay_TouchID_Confirm_Alert_Cancel": "취소", "WCPay_TouchID_Confirm_Alert_Content": "지문 인증 완료. 결제를 확인하겠습니까?", "WCPay_TouchID_Confirm_Alert_OK": "확인", "WCPay_TouchID_Confirm_Alert_Title": "결제", "WeChatPay_Name": "___<BRAND_Pay>___", "Wednesday": "수요일", "Weekly": "주간", "address_item_key": "주소", "address_item_place_holder": "주소 입력", "agree": "동의", "agree_user_protocal": "사용자 동의 수락", "agreement_alert": "\"사용자 동의서\"를 먼저 확인하고 동의하십시오.", "alertChargeFee": "서비스 비용", "area_item_key": "구역", "ask_verify_fingerprint": "지문 확인", "assign_pay_dialog_content": "입력된 신원정보가 현재 ___<BRAND>___ 계좌에 연결된 정보와 다릅니다. 귀하의 은행 카드와 연결된 ___<BRAND>___ 계좌를 사용하여 다시 인증하십시오.", "balance": "잔액", "bank_card_item_key": "은행 카드", "bank_select_item_key": "은행", "bind_new_card": "은행 카드 추가", "bind_new_card_section_footer": "보안 상의 이유로 현재 연결된 카드의 연결이 해제됩니다.", "bind_new_card_section_header": "새로운 카드를 연결하여 계좌 검색", "bind_new_card_to_pay_tip": "신원 인증을 위해 결제 암호를 입력하십시오", "bind_new_card_to_reset_mobile_desc": "새로운 카드를 연결해 휴대폰 번호를 연결하십시오.", "binded_card_list_page_title": "SMS 인증을 위한 휴대폰 선택", "birth_date_item_key": "생년월일", "can_not_bind_more_card": "연결된 카드가 최대 수량에 도달했습니다.", "can_not_get_sms_with_question_mard_word": "인증 코드를 수신하지 않았습니까?", "can_not_get_sms_word": "인증 코드 미수신", "cancel_time": "취소 시간: {}", "cannot_receive_sms_code_content": "은행에 등록된 휴대폰 번호로 SMS 코드가 전송되었습니다. 현재 이 휴대폰 번호를 사용하고 있으며 보안 앱이 SMS 코드를 차단하지 않았는지 확인하십시오. 이 번호를 더 이상 사용하고 있지 않다면 은행에 문의하십시오. 도움이 필요하시면 ___<OfficialEntity_Service>___ 센터 +86-0755-95017번으로 문의하십시오.", "cannot_receive_sms_code_title": "인증 코드를 수신할 수 없습니다", "card_holder_dialog_content": "1. 자금의 보안을 유지하기 위해, ___<BRAND_ID>___는 동명 은행 카드만 연결할 수 있습니다.\n\n2. 차명 카드를 연결하려면, 실명 정보를 업데이트해야 합니다.\n\n3. 실명 정보를 변경하면 기존 카드 소지자 정보가 삭제되고 새 카드 소지자 이름으로만 카드를 추가할 수 있습니다.", "card_holder_dialog_title": "카드 상의 성명", "card_holder_item_key": "카드 소유자", "card_holder_section_header": "이전에 은행에 제공했던 정보를 입력하십시오. 해당 이름에 등록된 은행 카드만 추후에 추가될 수 있습니다.", "card_info_section_header": "카드 정보 입력", "card_num_item_key": "카드 번호", "card_number_input_tips_title": "수수료 또는 온라인 뱅킹이 필요 없음", "card_select_item_key": "카드 유형", "card_type_section_header": "카드 종류 선택", "change_realname_word": "실명 변경", "change_to_face_id": "안면인식 결제", "change_to_pwd": "비밀번호 사용", "change_to_touch_id": "지문 사용", "check_pay_pwd_page_desc": "결제 비밀번호 입력하여 본인인증", "check_pay_pwd_page_title": "본인인증", "check_sms_desc": "인증 코드가 은행에 등록된 휴대폰 번호로 전송되었습니다\n\n1. 은행에 등록된 번호가 본인의 현재 휴대폰 번호인지 확인하십시오\n\n2. 휴대폰의 보안 앱으로 인해 SMS가 차단되지 않았는지 확인하십시오\n\n3. 현재 이 번호를 이용할 수 없는 경우 본인 은행으로 연락하십시오\n\n4. 추가 지원은 고객 서비스 95017번으로 전화하십시오.", "check_sms_page_desc": "은행 카드에 연결하려면 SMS 인증이 필요합니다. 휴대폰 {}(으)로 인증 코드가 전송되었습니다. 지시사항을 따르십시오.", "check_sms_page_favor": "{}{:.2f} ({}{:.2f} 면제)을 지불하게 됩니다", "check_sms_page_title": "휴대폰 번호 인증", "common_back": "뒤로", "common_cancel_word": "취소", "common_close": "닫기", "common_done_word": "완료", "common_drop_out_word": "종료", "common_i_know_word": "알겠습니다", "common_more": "더 보기", "common_next_word": "다음", "common_question_word": "FAQ", "common_select": "선택", "common_tip_word": "알림", "confirm_mobile_no": "휴대폰 확인", "confirm_pay": "즉시 결제", "confirm_to_receive": "수신 확인", "confrim_pay_and_open_deduct_word": "결제 및 사용", "confrim_pay_word": "결제", "coupon_change_should_change_payment": "결제 금액이 변경되었습니다. 다시 선택하십시오.", "coupon_component_need_bank_pay_tips": "지정 결제 방법용 할인", "cre_id_item_key": "ID 번호", "cre_id_item_place_holder": "ID 번호 입력", "cre_type_item_key": "ID 유형", "cre_type_item_place_holder": "ID 유형 선택", "cvv_dialog_content": "CVV 번호는 카드 뒷면 또는 앞면의 3 또는 4자리의 보안 코드입니다.", "cvv_dialog_title": "CVV 번호가 무엇입니까?", "cvv_item_key": "CVV ", "cvv_item_place_holder": "카드 뒷면의 통상 3 또는 4자리 숫자", "default_delay_transfer_confirm_desc": "{}시간 후에 계좌이체가 전달됩니다", "email_item_key": "이메일 주소", "email_item_place_holder": "이메일 입력", "error_detail_title": "해결 방법 보기", "face_hongbao": "주변 패킷", "fast_bind_card_support_bank_title_text": "현재 지원하는 은행은 다음과 같습니다", "fill_card_and_user_info_title": "은행 카드와 신원 정보를 입력하십시오", "fill_card_info_card_holder_assign_pay_header": "지불 할 카드의 소지자를 지정해야 합니다.", "fill_card_info_page_favor_desc": "이 종류의 은행 카드를 이용하고 추가로 {}{:.2f}를 절약하십시오.", "fill_card_info_page_realname_cre_not_support": "카드에 링크하는데 {}를 사용할 수 없습니다", "fill_card_info_page_title": "카드 정보 입력", "fill_card_num_format_error": "잘못된 카드 번호", "fill_card_num_of_card_holder_section_header": "카드 소유자의 은행 카드 번호를 입력하십시오", "fill_card_num_page_desc": "은행 카드 연결", "fill_card_num_page_favor_dialog_title": "이 카드로 절약을 더하십시오", "fill_card_num_page_realname_desc": "실명 인증을 완료하려면 은행 카드를 추가해야 합니다.", "fill_card_num_page_sns_input_hint": "직불 카드만 지원", "fill_card_num_page_title": "은행 카드 추가", "fill_card_number_assign_pay": "결제하려면 {}의 은행 카드를 사용하십시오", "fill_card_number_more_favor": "할인 혜택을 받으려면 지정된 은행 카드를 이용하십시오", "fill_complete_name": "성명 입력", "fill_id_format_error": "ID 번호 형식이 올바르지 않습니다.", "fill_in_sms_key": "인증 코드", "fill_in_sms_word": "인증 코드 입력", "fill_phone_num_format_error": "휴대폰 번호가 올바르지 않습니다.", "finger_print_err_tips": "다시 시도", "first_name_item_key": "이름", "first_name_item_place_holder": "이름 입력", "float_paydesk_modal_no_select_favor": "사용하지 않은 할인", "foreign_mobile_header": "새로운 휴대폰 입력", "forget_pay_pwd_title": "결제 비밀번호를 잊어버렸습니다", "get_sms_with_count_down_word": "인증 코드 받기 \n({})", "get_sms_word": "인증 코드 받기", "give_up_on_new_card": "카드 연결을 중단하겠습니까?", "give_up_this_order_or_not": "이 거래를 중지하겠습니까?", "group_aa": "분할 청구", "has_send_sms": "전송됨", "has_send_sms_with_count": "전송됨 ({})", "hongbao_refund_way_header_title": "보낸 후 24시간 이내에 개봉하지 않은 빨간 봉투는 아래의 방법으로 환불됩니다.", "hongbao_refund_way_title": "빨간 봉투 환불 방식", "id_card_name": "ID 카드", "install_cert_error": "인증서 설치 실패", "last_name_item_key": "성", "last_name_item_place_holder": "성 입력", "loading_title": "불러오는 중...", "lottery_network_error": "죄송합니다, 다음 번엔 행운을 빕니다!", "lqt": "미니 자금", "lqt_reset_mobile_desc": "은행 카드를 선택하십시오. 카드 은행에 등록된 휴대폰 번호를 이용해 귀하의 미니펀드를 확인하십시오.", "mobile_dialog_content": "은행계좌에 등록된 휴대폰 번호는 계정 가입 시 귀하가 제공한 번호입니다. 은행에 휴대폰 번호를 제공하지 않았거나 번호를 잊어버린 경우, 또는 해당 번호를 더 이상 이용할 수 없는 경우 은행에 연락하여 현재 휴대폰 번호를 알리시기 바랍니다.", "mobile_dialog_title": "휴대폰 번호", "mobile_item_key": "휴대폰 번호", "mobile_item_place_holder": "은행에 등록된 번호 입력", "name_item_key": "이름", "name_item_place_holder": "카드에 이름 입력", "nationality_item_key": "국가/지역", "nationality_place_holder": "국가/지역 입력", "new_mobile_item_key": "새로운 휴대폰 번호", "new_mobile_item_place_holder": "은행에 등록된 번호 입력", "new_user_card_num_input_safety_desc": "___<BRAND>___ 계좌 소유자가 소유한 카드를 연결하십시오", "new_user_card_num_input_safety_desc_v2": "___<BRAND>___ 계정 소유자의 카드를 연결하십시오", "no": "아니요", "offline_choose_payment": "기본 결제 방법 선택", "offline_choose_payment_fail": "기본 결제 방법에 실패한 경우 결제를 완료하기 위해 다른 방법을 사용해 보십시오.", "offline_click_view_code": "결제 코드 번호를 보려면 누르세요", "offline_pay_modify_limit": "금액 수정", "offline_pay_only_pay_title": "결제", "offline_pay_select_card_invalid": "현재 선택한 결제 방법을 사용할 수 없습니다. 다른 결제 방법을 사용해 보십시오.", "offline_pay_title": "돈", "offline_pay_to_merchant": "공급업체에 결제", "offline_prefer_payment": "기본 결제 방법", "offline_show_code_warning": "결제 코드 번호는 결제할 때 점원에게 보여주기 위해서만 사용하십시오. 안전을 위해 다른 사람과 공유하지 마십시오.", "offline_view_code_warning": "결제 코드 번호는 결제할 때 점원에게 보여주기 위해서만 사용하십시오. 안전을 위해 다른 사람과 공유하지 마십시오.", "ok": "확인", "order_address_section_header": "청구서 주소", "pay_card_detail_contact_user_info": "고객 서비스 문의: ", "pay_card_detail_tel_number": "95017", "pay_power_by_tenpay_word": "본 서비스는 Tenpay가 제공합니다", "pay_success": "결제 성공", "paydesk_coupon_page_title": "결제 할인 페이지", "paydesk_float_page_title": "결제 비밀번호", "paydesk_main_page_more_favor": "추가 할인", "paydesk_main_page_title": "결제 페이지", "paydesk_payment_page_title": "결제 카드 목록", "paydesk_sub_page_title": "결제 하위페이지", "payee_remark_title": "지급인 설명", "paying_alert_tips": "결제가 이미 제출되었습니다. 결제를 다시 제출해야 하는 경우 결제 결과 메시지를 기다린 후 확인하세요.", "payment_method": "결제 방법", "phone_number_item_key": "전화", "phone_number_item_place_holder": "전화 번호 입력", "profession_item_key": "직업", "pure_bind_card_succ_tips": "성공적으로 연결됨", "pwd_repeat_error_tip": "비밀번호가 일치하지 않습니다", "rebind_bank_card_section_header": "카드를 다시 연결하여 계좌를 검색하십시오", "receipt": "금액 수령", "receive_done": "수신 완료", "receive_remark_title": "영수증 설명", "receive_time": "수신 시간: {}", "receiver_remark_title": "수취인 설명", "refund_doing_tip": "환불 처리 중. 영업일 1-3일 이내에 귀하의 카드로 금액이 환불됩니다.", "refund_done": "환불됨", "refund_done_and_expired": "되돌아감(만료)", "refund_time": "반환된 시간: {}", "refund_transfer_or_not": "{}(으)로부터 계좌이체를 반환하겠습니까?", "refund_word": "돌아가기", "renewal_time_item_key": "변경 횟수", "resend_message_or_not": "이 메시지를 재전송하겠습니까?", "resend_sms": "재전송", "resend_sms_with_count": "재전송 ({})", "resend_success_tip": "메시지 재전송 완료", "resend_word": "재전송", "reset_ban_mobile_fill_card_info_credit_tip_header": "확인을 위한 은행 카드 정보를 입력", "reset_ban_mobile_fill_card_num_tip_header": "새로운 은행 카드를 추가하고 은행에 등록된 휴대폰 번호를 이용하여 잔액 결제를 확인하십시오.", "reset_cvv_and_valid_date_tip": "귀하는 현재 연결된 카드 정보 업데이트와 결제를 동시에 하고 있습니다. 불확실한 사항이 있을 경우 은행의 고객 서비스에 전화하십시오. ", "reset_cvv_title": "CVV 변경", "reset_lqt_mobile_fill_card_num_tip_header": "새로운 카드를 추가하고 카드에 등록된 휴대폰 번호를 이용해 미니펀드를 위한 SMS 인증을 완료하십시오.", "reset_mobile_bank_card_number": "카드", "reset_mobile_card_desc_format": "{}{} ({}) 등록된 휴대폰 번호", "reset_mobile_card_desc_with_update_format": "{}{} ({}) 등록된 휴대폰 번호입니다. ", "reset_mobile_new_mobile_info_btn": "세부 정보", "reset_mobile_new_mobile_number": "새 휴대폰", "reset_mobile_phone_page_title": "휴대폰 번호 편집", "reset_phone_tip": "본인인증이 완료되면 결제할 수 있습니다. 은행에 등록된 전화번호를 확인하려면 전화하십시오 ", "reset_pwd_fill_rebind_card_info_page_title": "은행 카드 정보를 입력", "reward": "후원 코드", "safety_dialog_content": "보안 수단: 계좌 보호, 실시간 감시, 긴급 동결. \n\n2단계 인증: 결제할 때마다 결제 비밀번호가 요구됩니다. 결제 금액이 큰 경우 SMS 인증이 필요합니다. \n\n개인정보 보호: 사용자 정보 보호를 위해 강력한 정보 암호화가 사용됩니다. \n\n결제 보험: 결제는 PICC에 의해 보험 적용을 받습니다.", "safety_dialog_title": "보안 보호", "scan_card_num_title": "카드 스캔", "select_payment": "결제 방법 선택", "select_payment_card": "결제 방법 선택", "send_verify_code_btn_wording": "보내기", "send_verify_code_switch_btn_wording": "인증 방법 변경", "send_verify_code_tips_format": "SMS 인증 코드 전송: \n{}", "set_pay_pwd_confirm_page_title": "확인을 위해 재입력하십시오", "set_pay_pwd_page_desc": "결제 인증을 위해 ___<BRAND_Pay>___ 암호를 설정하십시오", "set_pay_pwd_page_title": "결제 암호 설정", "set_pwd_success": "설정 업데이트 완료", "succ_page_open_biometric_cancel_btn_title": "허용 안 함", "succ_page_open_biometric_dialog_content": "얼굴이나 지문 결제를 사용해 빠르게 결제할 수 있습니다.", "succ_page_open_biometric_faceid_btn_title": "얼굴 결제", "succ_page_open_biometric_touchid_btn_title": "지문 결제", "succ_page_open_face_id_dialog_content": "페이스 페이를 사용해 얼굴 인식을 사용하고 결제를 빠르고 안전하게 완료하십시오.", "succ_page_open_face_id_right_btn_title": "페이스 페이 사용", "succ_page_open_touch_id_dialog_content": "터치 페이를 사용해 지문 인식을 사용하고 결제를 빠르고 안전하게 완료하십시오.", "succ_page_open_touch_id_left_btn_title": "나중에 시도하십시오", "succ_page_open_touch_id_right_btn_title": "터치 페이 사용", "to_be_confirm_receive": "수신 미확인", "transfer": "계좌이체", "transfer_account": "계좌이체 금액", "transfer_amount_input_invalid_hint": "입력된 금액이 올바르지 않습니다", "transfer_bank": "은행 카드로 계좌이체", "transfer_explain": "계좌이체 메모 추가", "transfer_modify_explain": "변경", "transfer_second_left_button": "취소", "transfer_second_right_button": "계속", "transfer_second_title": "계좌이체 알림", "transfer_time": "계좌이체 시간: {}", "transfer_ui_title": "친구에게 계좌이체", "understand_safety": "보안 보호", "update_word": "업데이트", "user_card_type_select_placeholder_v2": "은행 카드 및 카드 종류를 선택", "user_info_section_header": "개인 정보 입력", "user_protocal": "\"사용자 동의서\"", "valid_date_item_key": "만료", "verify_cre_tip": "본인인증을 위해 {}의 {} 뒤 4자리 숫자를 입력하십시오", "verify_fingerprint_fail": "지문 인증 실패", "verify_id_ui_true_name_tips": "{} (이름 입력)", "wechat_bank_agreement": "은행 계약", "wechat_mobile_phone_word": "___<BRAND>___에 휴대폰이 연결되었습니다", "wechat_user_agreement": "___<BRAND_Pay>___ 사용자 계약", "wxp_common_cancel": "취소", "wxp_common_confirm": "확인", "wxp_common_i_know": "알겠습니다", "wxp_common_remind": "알림", "wxp_network_error": "시스템 사용량이 많습니다. 나중에 다시 시도하십시오.", "wxp_payment_network_error": "거래가 제출되었습니다. ___<BRAND_Pay>___ 공식 계정으로부터 결제 현황 알림을 받게 됩니다. 필요한 경우 결제 사항을 다시 제출하기 전에 결제 현황을 확인하십시오.", "wxp_system_error": "시스템 사용량이 많습니다. 나중에 다시 시도하십시오.", "wxp_wcpay_system_error": "시스템 사용량이 많습니다. 나중에 다시 시도하십시오.", "yes": "예", "zip_item_key": "우편 번호", "zip_item_place_holder": "우편 번호 입력", "common_confirm_word": "확인", "ModifyPwdUseCase_ModifyPwd_GiveUp_Yes": "변경 사항 취소", "ModifyPwdUseCase_ModifyPwd_GiveUp_No": "계속", "disagree": "동의 안 함", "card_user_agreement": "사용자 서비스 계약", "card_bank_agreement": "은행 계약", "Card_UserAgreement_Title": "은행 카드를 추가하려면 아래의 계약에 동의해야 합니다.", "pay_settings_delay_transfer_page_title": "송금 시간", "pay_settings_delay_transfer_page_desc": "승인되면, 자금은 다음 시간까지 다른 사용자의 잔액에 입금됩니다. 송금은 완료 후에 회수할 수 없으니 송금 전에 수취인 정보를 주의 깊게 확인하시기 바랍니다.", "pay_settings_delay_transfer_no_delay": "즉시", "pay_settings_delay_transfer_two_hour": "2시간 내", "pay_settings_delay_transfer_one_day": "24시간 내", "pay_settings_biometric_pay_enabled": "사용", "pay_settings_biometric_pay_disabled": "사용 안 함", "pay_settings_biometric_pay_multi_support_title": "얼굴 인식/지문 결제", "pay_settings_biometric_pay_faceid_enabled": "얼굴 인식 결제 사용", "pay_settings_biometric_pay_touchid_enabled": "지문 결제 사용함", "pay_settings_biometric_pay_multi_support_desc": "사용함으로 설정되면 얼굴이나 지문 인증을 사용해 보다 빠르게 결제할 수 있습니다.", "f2f_pay_extrabuy_detail_modal_original_price": "(정가 ¥{:.2f})", "common_button": "버튼", "Accessibility_Type_SwitchView_Selected": "{}, 스위치 버튼, 사용", "Accessibility_Type_SwitchView_UnSelected": "{}, 스위치 버튼, 사용 안 함", "YunShanFu_Loading_Wording": "QuickPass 여는 중...", "YunShanFu_Uninstalled_Error": "아직 QuickPass를 설치하지 않으셨습니다. 설치 후 다시 시도해 보거나 ___<BRAND_Pay>___로 결제를 계속하세요.", "OfflinePay_Setting_CloseOfflinePay_AlertMessage_ShowFeaturePassword": "보안 잠금을 사용하면, 귀하가 선택한 잠금 해제 방법을 이용해야 Quick Pay에 접근할 수 있어 결제 정보 보호에 도움이 됩니다. 사용 안 함으로 설정하시겠습니까?", "OfflinePay_Setting_CloseOfflinePay_AlertMessage": "빠른 결제를 사용 안 하시겠습니까?", "OfflinePay_Setting_CloseOfflinePay_BtnTitle": "사용 안 함", "OfflinePay_Setting_CloseOfflinePay_Cancel": "계속", "OfflinePay_Setting_CloseOfflinePay_OpenWalletLock": "보안 잠금 설정", "Wallet_Mix_Paid_UnKnown_Error": "거래 요청이 제출되었습니다. ___<BRAND_Pay>___ Hong Kong 공식 계정에서 상태 알림을 받게 됩니다. 결제 상태가 확인될 때까지 다시 결제하지 마십시오.", "bank_card_info": "메모", "HHC_Did_Add": "추가됨", "VoiceOver_OfflinePay_barCode": "결제 바코드는 계산원에게 보여줄 수 있습니다. 전체 화면 결제 코드를 표시하려면 두 번 누르세요.", "VoiceOver_OfflinePay_barCode_short": "결제 코드", "VoiceOver_OfflinePay_Qrcode": "결제 QR 코드", "VoiceOver_OfflinePay_barcode_clickHint": "돌아가려면 두 번 누르세요", "VoiceOver_OfflinePay_Qrcode_clickHint": "전체 화면으로 보려면 두 번 누르세요", "common_link": "링크", "Accessibility_Collapse_Header_Collapsed": "{}, 축소됨", "Accessibility_Collapse_Header_Showed": "{}, 확장됨", "Pay_Android_Fingerprint_Prompt_Title": "지문 인증하기", "Pay_Android_Fingerprint_Prompt_SubTitle": "후 결제를 완료합니다.", "Pay_Android_Fingerprint_Prompt_Button": "암호 사용", "Accessibility_Collapse_Header_Collapsed({}": "축소", "Accessibility_Collapse_Header_Showed({}": "모두 표시", "Accessibility_State_Disabled": "어둡게", "Accessibility_String_PwdInputView": "Password box, 6 digits in total, {} digits entered", "Accessibility_Type_Button": "{}, button", "Accessibility_Type_CheckBox_Selected": "Check box, selected, {}, button", "Accessibility_Type_CheckBox_UnSelected": "Check box, unselected, {}, button", "Accessibility_Type_Selected": "Selected, {}, button", "Accessibility_Type_UnSelected": "Unselected, {}, button", "common_check_box_check": "Check box, selected", "common_check_box_uncheck": "Check box, unselected", "OfflinePay_ChangePayment_UnConnectedNetwork_Tips": "네트워크를 사용할 수 없습니다. 결제 수단을 선택할 수 없습니다.", "OfflinePay_EnablePage_UnConnectedNetwork_Tips": "네트워크에 연결할 수 없습니다. 나중에 다시 시도해 보십시오", "Fetch_Balance_To_Bank": "다음으로 출금", "Fetch_Balance_Amount": "출금액", "Fetch_Balance_Amount_Tips": "잔액: ¥{}.", "Fetch_Balance_Amount_Exceed": "입력 금액이 사용 가능 잔액을 초과했습니다", "Fetch_Balance_Fetch_All": "모두 출금하기", "HoneyPay_CheckPwd_Unbind_Title": "가족 카드 연결 해제", "HoneyPay_Modify_CreditLimit_Title": "월별 한도액 편집", "HoneyPay_Modify_CreditLimit_Desc": "월 한도액", "HoneyPay_Modify_CreditLimit_Max_Alert": "금액은 ¥{:.2f}을 초과할 수 없습니다", "balance_entry_balnce_title": "내 잔액", "balance_entry_balnce_detail": "거래", "balance_entry_powered_by_tenpay": "본 서비스는 Tenpay가 제공합니다", "balance_recharge_page_title": "충전하기", "balance_recharge_card_info_title": "충전 방법", "balance_recharge_payment_new_card": "새 카드 추가", "HoneyPay_Add_Card": "가족 카드 선물하기", "HoneyPay_Select_Contact_Title": "친구 목록 선택", "HoneyPay_Modify_Comment": "댓글 편집", "HoneyPay_MoneyInput_Hint": "금액 입력", "HoneyPay_CreateCard_Btn": "보내기", "HoneyPay_Max_Amount_Notice": "금액은 ¥{:.2f}을 초과할 수 없습니다", "HoneyPay_Modify_Credit": "월 한도액", "HoneyPay_Main_Title": "가족 카드", "hbrefund_info_tips": "설명", "hbrefund_set_button": "설정", "hbrefund_time_title": "빨간 봉투 환불 시간", "hbrefund_forbid_way": "이 환불 방법은 더 이상 지원되지 않습니다.", "hbrefund_had_set": "설정 완료", "hbrefund_origin_desc": "보낸 후 24시간 이내에 개봉하지 않은 빨간 봉투는 원래 결제 방법으로 환불됩니다.", "hbrefund_set_tips": "설정 후 미수령 금액은 원래 결제 방법으로 환불되며, 이것은 \"잔액 환불\"로 전환할 수 없습니다. 계속하시겠습니까?", "TeenagerPayDetailUIPage_NotSet": "없음", "TeenagerPayDetailUIPage_LimitedAmount": "¥{:.{}f}", "TeenagerPaySetLimitModal_AmountTitle": "금액", "TeenagerPaySetLimitModal_MaxAmount": "최대 7자리 허용됨", "TeenagerPayGetDetailUseCase_LimitOn": "한도액 설정됨", "TeenagerPayGetDetailUseCase_LimitOff": "제한 없음", "TeenagerPayUseCase_Set_Ok": "설정 성공", "TeenagerPayUseCase_Close_Ok": "금액 제한 사용 안 함", "TeenagerPayUseCase_Limit_Max": "거래당 결제 한도는 매일 결제 한도보다 클 수 없습니다.", "TeenagerPayUseCase_Limit_Min": "매일 결제 한도는 거래당 결제 한도보다 작을 수 없습니다.", "Dcep_Loading_Wording": "불러오는 중...", "Dcep_Uninstalled_Error": "E-CNY를 설치하지 않으셨습니다. 설치 후 다시 시도해 보거나 ___<BRAND_Pay>___로 결제를 계속하세요.", "TeenagerPayUseCase_Input_Accesibility": "텍스트 바", "bankcard_detail": "{} 테일 번호 {}", "bankcard_qmf_detail": "{} 가족 카드 발신자, {}", "FaceCheck_Agreement_title": "얼굴 인증", "FaceCheck_Success_title": "인증됨", "FaceCheck_Result_Retry": "다시 시도", "TabBar_NewBadge": "새로 만들기", "common_delete_alert_title": "삭제하시겠습니까?", "common_delete": "삭제", "transfer_to_bank_name_input_placeholder": "수취인 이름", "transfer_to_bank_card_input_placeholder": "수취인 은행 카드 번호", "transfer_to_bank_bank_select_placeholder": "은행 선택", "transfer_to_bank_arrival_time_select_title": "시간", "transfer_to_bank_arrival_time_modal_title": "송금 시간 선택", "transfer_to_bank_arrival_time_modal_desc": "송금 요청 후, 자금이 다음 시기에 수취인 계정으로 입금될 것입니다.", "transfer_to_bank_history_page_title": "수취인 선택", "transfer_to_bank_history_page_empty_prompt": "이전 수취인 없음", "transfer_to_bank_history_me_section_title": "나", "transfer_to_bank_history_others_section_title": "이전 수취인", "transfer_to_bank_history_modify_remark_action": "메모", "transfer_to_bank_history_set_remark_title": "메모 추가", "transfer_to_bank_history_delete_action": "삭제", "transfer_to_bank_bank_unavailable_alert": "은행이 유지 관리 중입니다. 현재 계좌이체 사용이 불가합니다.", "transfer_to_bank_money_input_title": "금액", "transfer_to_bank_info_receiver_format": "수취인: {}", "transfer_to_bank_info_charge_fee": "서비스 요금", "transfer_to_bank_info_charge_fee_rate_format": "(요율: {:.2f})", "transfer_to_bank_info_total_amount": "총계", "transfer_to_bank_info_transfer_explain": "설명", "transfer_to_bank_info_transfer_explain_edit_hint_format": "결제자와 수취인에게 표시. 최대 {}자.", "transfer_to_bank_info_add_transfer_explain": "메모 추가", "transfer_to_bank_info_detail_title": "세부 정보", "transfer_to_bank_info_detail_current_state": "상태", "transfer_to_bank_info_detail_paid_success": "계좌이체 시작", "transfer_to_bank_info_detail_withdrawn_success": "출금 완료", "HoneyPay_PrepareCardUI_Title": "가족 카드 설정", "none": "없음", "mobile_item_key_bank": "은행에 등록된 휴대폰 번호", "mobile_item_place_holder_short": "휴대폰 번호 입력", "FillCard_Number_Default_Mobile_Modify_Tips_New": "저번에 연결한 휴대폰 번호는 자동으로 채워져 있습니다. 필요에 따라 수정하실 수 있습니다.", "HoneyPay_MoneyInput_Hint_New": "금액 입력", "AddPayCard_No_Card_Bind_Card_Title": "카드 번호를 입력하지 않고 연결", "AddPayCard_Manual_Bind_Card_Title": "카드 번호를 입력하여 연결", "FillCard_Number_Reg_Hint_V3": "{}의 은행 카드 번호 입력", "FastBindCardSelectBankUIV2_Title": "카드 번호를 입력하지 않고 연결", "FastBindCardSelectBankUIV2_Search_Hint": "{} 은행 검색", "qrcode_collection_settings": "수신 설정", "qrcode_collection_amount": "금액", "qrcode_collection_remark": "금액 수령 지침", "OfflinePay_Banner_Use_Tips": "결제 코드", "OfflinePay_Banner_Expand_Tips": "확장", "OfflinePay_Banner_Collapse_Tips": "숨기기", "OfflinePay_Close_WalletLock_HalfDialog_Title": "결제 코드 사용 안 함", "OfflinePay_Close_WalletLock_HalfDialog_Content": "결제 코드 사용 시 보안을 위해 보안 잠금을 설정할 수 있습니다. 설정 후 결제 코드 사용 시 보안 인증을 거쳐야 합니다.", "FillCardNumberV2_CountryCode_Hint": "국가/지역 코드 입력", "FillCardNumberV2_CountryCodeView_Hint": "선택하기", "paydesk_main_page_not_use_favor": "제안을 사용하지 않습니다.", "paysecurity_digital_cert_not_install": "사용 안 함", "WCPay_Digital_Cert_Desc_Not_Install": "디지털 인증서 사용 설정되지 않음", "WCPay_Digital_Cert_Desc_Already_Install": "디지털 인증서 사용", "WCPay_Digital_Cert_Manage_Content_Desc": "장치에서 디지털 인증서 사용:", "WCPay_Digital_Cert_Manage_Content_Desc_1": "• 장치에서 보다 안전하게 결제", "WCPay_Digital_Cert_Manage_Content_Desc_2": "• 잔액을 사용해 매일의 결제 한도 증가", "WCPay_Digital_Cert_Install_Button_Title": "사용", "WCPay_Digital_Cert_Delete_Button_Title": "사용 안 함", "WCPay_Digital_Cert_Install_List_desc": "인증서 사용 설정된 장치", "WCPay_Digital_Cert_Delete_Confirm_Content": "장치의 기존___<BRAND_ID>___에 대한 디지털 인증서를 사용 안 함 설정하시겠습니까?", "WCPay_Digital_Delete_Confirm_Btn_Title": "사용 안 함", "WCPay_Digital_Cert_Install_Action_Title": "신원 인증하기", "WCPay_Digital_Cert_Install_Action_Desc": "인증서 설치 전 신원 인증 필요", "WCPay_Digital_Cert_Install_Input_Title_default": "신분증", "WCPay_Digital_Cert_Install_Input_Desc_default": "ID 번호 입력", "WCPay_Digital_Cert_Install_Input_Desc": "{}의 ID 번호를 입력하세요", "WCPay_Digital_Cert_Verify_Button_Title": "인증하기", "WCPay_Digital_Cert_Install_Sccuess": "인증됨", "WCPay_Digital_Cert_Delete_Succ_Toast": "사용 안 함", "LQT_Purchase_Page_Title": "계좌 입금", "LQT_Purchase_Card_Info_Title": "송금 모드", "LQT_MonetInputOutOfRange_Tips": "잔액이 부족합니다. 충전 후 다시 시도하세요.", "LQT_Limit_Cashier_Modal_Balance_Desc": "잔액의 금액", "LQT_Limit_Cashier_Modal_LQT_Desc": "미니 잔금", "LQT_Limit_Cashier_Modal_Transfer_In_Tips": "계좌 입금", "LQT_Limit_Cashier_Modal_Confirm_Button_Title": "알겠습니다", "LQT_SaveAmountLargeThanBankAvaible_Tips": "입력한 금액이 은행의 결제 한도를 초과합니다", "LQT_Redeem_Card_Info_Title": "다음으로 출금하기", "LQT_Redeem_Page_Title": "출금하기", "LQT_Redeem_Confirm_View_Desc": "출금하기", "LQT_Redeem_Balance_Amount": "미니 잔금 잔액 ¥{:.2f}", "LQT_Redeem_Balance_Insufficient": "미니 잔금 잔액 부족", "LQT_Redeem_Balance_Fetch_All": "모두 출금하기", "LQT_Loading_Card_Data": "은행 카드 목록 가져오는 중", "LQT_Loading_LQT_Amount": "미니 잔금 잔액 가져오는 중", "LQT_Loading_LQ_Amount": "잔액 가져오는 중", "LQT_PerRedeem_Invalid_Default_Tips": "1회 거래 한도 초과", "LQT_Redeem_Fetch_Amount_Large_Than_Bank_Avaible": "거래당 최대 금액은 ¥{:.2f}입니다. 여러 거래로 분할할 수 있습니다.", "LQT_Redeem_Fetch_Amount_Large_Than_Max_Amount_Tips": "자세히 알아보기", "HoneyPay_Record_Receive_Title": "나에게 올 가족 카드", "HoneyPay_Record_Donate_Title": "나에게서 나갈 가족 카드", "LQTDetail_balance_Accessibility": "잔액 ¥{:.2f}", "WCPay_LQT_OnClickPurchase_Fail_Network_Error": "네트워크 오류입니다. 은행 카드 목록을 가져올 수 없습니다. 나중에 다시 시도해 보십시오.", "WCPay_LQT_OnClickRedeem_Fail_Network_Error": "네트워크 오류입니다. 은행 카드 목록을 가져올 수 없습니다. 나중에 다시 시도해 보십시오.", "WCPay_LQT_PurchaseFund_Fail_Network_Error": "구매할 수 없습니다. 나중에 다시 시도하세요.", "WCPay_LQT_QryPurchaseResult_Fail_Network_Error": "구매 결과를 확인할 수 없습니다", "WCPay_LQT_PreRedeemFund_Fail_Network_Error": "경품 수령을 주문할 수 없습니다. 나중에 다시 시도하세요.", "WCPay_LQT_RedeemFund_Fail_Network_Error": "자금을 되돌릴 수 없습니다. 나중에 다시 시도하세요.", "WCPay_LQT_MoneyVC_Save_TextFieldTips": "송금 금액", "WCPay_LQT_MoneyVC_Fetch_TextFieldTips": "금액", "LQT_Purchase_Keyboard_Confirm_Title": "입금하기", "LQT_Redeem_Keyboard_Confirm_Title": "출금하기", "Wallet_Lock_Default_Title": "보안 잠금", "Wallet_Lock_FaceLock": "얼굴 잠금 해제", "Wallet_Lock_TouchLock": "지문 잠금 해제", "Wallet_Lock_BioLock": "얼굴/지문 잠금 해제", "Wallet_Lock_PatternLock": "패턴 암호 잠금 해제", "Wallet_Lock_PatternLock_Modify_Verify_Title": "이전 패턴 암호 입력", "Wallet_Lock_PatternLock_Modify": "패턴 암호 변경", "Wallet_Lock_PatternLock_Modify_SubTltle": "새 패턴 암호 설정", "Wallet_Lock_Close_Tips": "보안 잠금을 사용 안 함 설정한 경우, \"나\" > \"서비스\"에 접근하려면 잠금 해제 방법이 필요하지 않습니다.", "Wallet_Lock_TouchLock_Verify_Title": "Touch ID를 인증해 계속합니다", "Wallet_Lock_FaceLock_Verify_Title": "Face ID를 인증해 계속합니다", "Wallet_Lock_PatternLock_Verify_Title": "패턴 암호 입력", "Wallet_Lock_Verify_byPwd": "결제 암호 인증", "Wallet_Lock_Verify_Btn_FaceID": "얼굴 인증하기", "Wallet_Lock_Verify_Btn_TouchID": "지문 인증하기", "Wallet_Lock_PatternLock_Setup_Title": "패턴 암호 설정", "Wallet_Lock_PatternLock_Reset_Title": "패턴 암호를 잊으셨나요?", "Wallet_Lock_PatternLock_Confirm_Title": "확인을 위해 다시 입력", "Wallet_Lock_PatternLock_Confirm_Error_Tips": "암호 불일치. 다시 설정하세요.", "Wallet_Lock_PatternLock_Verify_Error_Tips": "잘못된 패턴입니다. 남은 기회는 {}회입니다.", "Wallet_Lock_PatternLock_Verify_Limit_Tips": "너무 많이 시도했습니다. {}분 후 다시 시도하세요.", "Wallet_Lock_PatternLock_Modify_AfterVerify_Title": "새 패턴 암호 설정", "Wallet_Lock_PatternLock_InvalidPattern_Tips": "최소 4개의 점을 연결해야 합니다. 다시 설정하세요.", "Wallet_Lock_PatternLock_Setup_Succ_Tips": "패턴 암호 설정", "Wallet_Lock_PatternLock_Modify_Succ_Tips": "패턴 암호 변경됨", "Wallet_Lock_PatternLock_Setup_Cancel_Tips": "패턴 잠금을 사용 설정하지 않으시겠습니까?", "Wallet_Lock_PatternLock_Setup_Cancel_LeftBtn": "사용", "Wallet_Lock_PatternLock_Setup_Cancel_RightBtn": "나중에", "Wallet_Lock_Not_Support_TouchID_Tip": "Touch ID를 이 장치에서 사용할 수 없습니다. 보안 잠금을 재설정하세요.", "Wallet_Lock_Not_Support_FaceID_Tip": "Face ID를 이 장치에서 사용할 수 없습니다. 보안 잠금을 재설정하세요.", "Wallet_Lock_Close_Wallet_Lock_Tip": "보안 잠금 사용 안 함", "Wallet_Lock_Setup_Pattern_Lock_Tip": "패턴 암호 설정", "Wallet_Lock_TouchID_NotEnrolled_Tip": "Touch ID가 사용 안 함으로 설정되었습니다. 시스템 설정에서 Touch ID를 사용함으로 설정하거나 보안 잠금을 재설정하세요.", "Wallet_Lock_FaceID_NotEnrolled_Tip": "Face ID가 사용 안 함으로 설정되었습니다. 시스템 설정에서 Face ID를 사용함으로 설정하거나 보안 잠금을 재설정하세요.", "Wallet_Lock_FingerPrint_NotEnrolled_Tip": "시스템에 지문 정보가 없습니다. 지문 정보를 저장한 뒤 보안 잠금을 다시 설정하세요.", "Wallet_Lock_PatternLock_VerifyBlock_Tip": "너무 많이 시도했습니다. \"패턴 암호 재설정\"에서 신원을 인증하거나 10분 안에 다시 시도하세요.", "Wallet_Lock_PatternLock_VerifyBlock_Reset_Tip": "패턴 암호 재설정", "Wallet_Lock_New_FingerPrint_Authen_Tips": "새 지문 입력됨. 신원 인증을 위해 결제 암호를 입력하세요.", "Wallet_Lock_New_TouchID_Authen_Tips": "이 장치의 지문 정보가 변경되었습니다. 귀하의 신원 인증을 위해 결제 암호를 입력하세요.", "Wallet_Lock_New_FaceID_Authen_Tips": "이 장치의 얼굴 정보가 변경되었습니다. 귀하의 신원 인증을 위해 결제 암호를 입력하세요.", "Wallet_Lock_Forget_Pwd": "암호를 잊으셨나요?", "Wallet_Lock_Retry_Pwd": "다시 시도", "LQT_Detail_Operation_More_Product_Title": "제품 더 보기", "pay_settings_biometric_pay_touchid_title": "지문 결제", "pay_settings_biometric_pay_faceid_title": "얼굴 인식 결제", "pay_settings_biometric_pay_multi_title": "얼굴 인식/지문 결제", "pay_settings_biometric_pay_touchid_desc": "사용함으로 설정되면 지문 인증을 사용해 보다 빠르게 결제할 수 있습니다.", "pay_settings_biometric_pay_faceid_desc": "사용함으로 설정되면 얼굴 인증을 사용해 보다 빠르게 결제할 수 있습니다.", "pay_settings_biometric_pay_multi_desc": "얼굴이나 지문 결제를 사용해 빠르게 결제하세요.", "pay_settings_biometric_pay_enable_faceid": "얼굴 인식 결제 사용", "pay_settings_biometric_pay_enable_touchid": "지문 결제 사용", "common_reddot_accessibility": "새 메시지가 있습니다", "common_help": "도움말", "bind_new_card_input_name": "은행에 등록된 이름을 입력하하기", "paydesk_title_accessibility_selected": "선택됨", "RedDot_New": "NEW", "renewal_or_sign_time_item_key": "보험/갱신 허가", "WCPay_Option_Item": "선택 사항", "VoiceOver_OfflinePay_Unselected": "선택 해제됨", "FillCard_Number_Reg_Hint_Self": "수수료 없음", "common_continue": "계속", "WCPay_Risk_Dialog_Title": "위험 발생 가능성이 감지되었습니다. 계속할지 여부를 확인해 주세요. 계속한다면 귀하의 신원을 인증해야 합니다.", "WCPay_Risk_Not_Support_Dialog_Title": "위험 발생 가능성이 감지되었습니다. 계속하기 전에 위험을 해결하세요.", "WCPay_Risk_Failed_Dialog_Title": "본인 인증 실패로 작업이 종료됩니다. 닫기", "bind_card_agreement_protocal_and_next": "동의 및 추가", "wallet": "지갑", "awaiting_real_name_verification": "신원 인증 보류 중", "five_stars": "*****", "Card_UserAgreement_Read_And_Agreement_Title": "추가 시 동의 필요", "FaceCheck_Common_Error": "시스템 사용량이 많습니다. 다시 시도하세요.", "FaceCheck_MP_Request_Use": " 액세스 요청", "FaceCheck_MP_Confirm_Tip": "___<BRAND>___에서 얼굴 인식을 사용하여 신원을 인증합니다. 이는 계좌 소유자만 설정해야 합니다.", "FaceCheck_MP_Front_Feedback": "도움말 센터", "FaceCheck_Recoging": "인식 중...", "waiting_for_real_name_authentication": "신원 인증 보류 중", "collect_sub_title": "금액", "collect_main_add_desc_title_simple_change": "변경", "collect_main_add_desc_title": "메모 추가", "remittance_amount_lowest_limit": "최소 송금액은 ¥0.01입니다.", "collect_main_fixed": "금액 설정", "collect_main_first_enter_tips_title": "금액 수령 중", "collect_main_first_enter_tips_new": "받은 금액은 귀하의 ___<BRAND_Balance>___(\"나\" > \"서비스\" > \"지갑\")에 예치되며 지출 또는 출금 가능합니다.", "collect_main_close_ring_tone": "수령 알림음 사용 안 함", "collect_main_close_ring_tone_tips": "사용 안 함", "collect_main_open_ring_tone": "수령 알림음 사용", "collect_main_open_ring_tone_tips": "사용. 미디어 볼륨이 켜져 있는지 확인하세요.", "collect_main_qrcode_usage_other": "기타", "collect_main_qrcode_usage_other_placeholder": "정보 추가(최대 16자)", "collect_main_payer_desc_default_placeholder": "수령자에게 메모를 추가합니다.", "collect_qrcode_save_failed": "저장 실패", "collect_material_guide_save_text_toast": "앨범에 저장됨", "collect_mch_module_title": "기업 QR 코드", "collect_personal_module_title": "개인 소유 QR 코드", "collect_setting_title": "수령 설정", "collect_main_fixed_cancel": "금액 지우기", "collect_main_more_function": "설정 더 보기", "collect_main_save_qrcode": "결제 코드 저장", "collect_main_receive_title": "총계", "collect_main_paying": "결제 중...", "collect_main_pay_suc": "결제 성공", "collect_main_pay_cancel": "결제 취소", "collect_main_loading_title": "QR 코드 불러오는 중...", "collect_main_ring_not_support": "이 시스템에서 지원되지 않음", "WCPay_Transfer_To_Format": "{}에게 송금", "WCPay_Transfer_Weixin_Alias_Prefix": "___<BRAND_ID>___:", "WCPay_Transfer_InputAmount_Tips": "먼저 금액을 입력하세요", "WCPay_Transfer_Cashier_Desc_Format": "{}에게 송금", "WCPay_Transfer_Succ_Tips": "{}의 수령을 기다리는 중", "WCPay_Service": "서비스", "recognize_and_pay": "인식 및 결제", "bizf2f_input_ui_page_to_person": "개인에게 결제", "bizf2f_input_ui_page_add_remark": "메모 추가", "bizf2f_input_ui_page_amount_title": "결제 금액", "WCPay_Verify_Password_Get_SMS_Code": "인증 코드 수신", "WCPay_VerifyCode_Header_Description": "SMS 인증은 거래에 필수입니다.", "bizf2f_input_ui_page_pay_action": "결제", "bizf2f_input_ui_page_change_remark": "변경", "bizf2f_input_ui_page_pay_title": "결제", "bizf2f_favor_title": "쿠폰", "bizf2f_favor_total_fee": "금액 총계", "bizf2f_favor_calculating": "계산 중...", "bizf2f_favor_select_favor": "할인 선택", "UN_BIND_CARD_TITLE": "은행 카드 연결 해제", "WCPay_system_version_limitation_tip": "기능 더 보기는 HarmonyOS 4.2 이하 또는 기타 장치에서 확인하세요.", "reconfirm_payment_amount_title": "결제 금액 다시 확인", "reconfirm_payment_amount_des": "자산 보안을 위해 실수를 방지 차원에서 금액을 확인하세요.", "reconfirm_amount_page_tip": "규제 요구 사항으로 인해 정적 QR 코드 한도를 초과하는 결제는 아래의 동적 QR 코드를 스캔하여 완료해야 합니다. 버튼을 눌러 결제 인증 후 완료하세요.", "Hongbao_SendUI_NavigationBar_Title": "빨간 봉투 보내기", "Hongbao_SendUI_Send_Button_Titlle": "빨간 패킷을 준비하십시오", "Hongbao_SendUI_Count_Title": "수량", "Hongbao_SendUI_Amount_Title_Group": "총계", "Hongbao_SendUI_Amount_Title_Single": "금액", "Hongbao_SendUI_RandomLuckyMode_Title": "임의 금액", "Hongbao_SendUI_Count_Tips": "수량 입력", "Hongbao_SendUI_Amount_Tips": "¥0.00", "Hongbao_SendUI_Default_Wishing": "행운을 빕니다", "Hongbao_Per_Hongbao_Max_Amount_Format": "각 빨간 봉투당 최대 {}CNY", "HongBao_SendTips": "보낸 빨간 봉투", "HongBao_OpenTips": "열기", "HongBao_AmoutTips": "CNY", "HongBao_MainTitle": "빨간 봉투", "HongBao_Cashier_Desc": "___<BRAND_hongbao>___", "Hongbao_SendUI_RandomMode_Title": "임의 금액", "Hongbao_SendUI_NormalMode_Title": "동일한 금액", "Hongbao_SendUI_ExclusiveMode_Title": "전용", "Hongbao_SendUI_Select_Count_Suffix": "", "Hongbao_SendUI_Group_Member_Count_Format": "이 그룹에는 {}명이 있습니다", "Hongbao_SendUI_Amount_Title_Group_Normal": "개별 금액", "Hongbao_SendUI_Perperson_Maxvalue_Error_Tips": "각 빨간 봉투당 최대 {}CNY", "Hongbao_SendUI_Perperson_Minvalue_Error_Tips": "각 빨간 봉투당 최소 {:.2f}", "Hongbao_SendUI_Count_Larger_Than_Group_Mem_Count_Error_Tips": "빨간 봉투 수는 그룹 멤버의 수를 초과할 수 없습니다.", "Hongbao_SendUI_Total_Num_Error_Tips": "최대 수량은 {}입니다.", "Hongbao_SendUI_Select_Count_Data_Invalid_Error_Tips": "\"수량\" 입력되지 않음", "Hongbao_SendUI_Amount_Data_Invalid_Error_Tips": "\"총계\" 입력되지 않음", "Hongbao_SendUI_Select_Count_Zero_Error_Tips": "수량을 선택하세요.", "Hongbao_SendUI_Amount_Larger_Than_Max_Amount_Error_Tips": "총 금액은 {}CNY를 초과할 수 없습니다.", "Hongbao_ReceiveModal_Detail_Link": "세부 정보 보기", "Hongbao_DetailUI_Load_More_Text": "클릭하여 더 보기", "TransferPhone_Entry_Title": "송금 방법 선택", "TransferPhone_To_Bank_Title": "은행 카드로 계좌이체", "TransferPhone_To_Bank_Desc": "수취인 은행 카드 번호를 입력하여 은행계좌로 송금합니다.", "TransferPhone_To_Phone_Title": "휴대폰 번호로 계좌이체", "TransferPhone_To_Phone_Desc": "___<BRAND_Balance>___로 송금할 수취인의 휴대폰 번호를 입력하십시오.", "TransferPhone_To_PaySetting": "휴대폰 번호 계좌이체 설정", "WCPay_ThirdParty_Tips_Title": "법적 고지", "WCPay_Service_Manage": "서비스 관리", "identify_and_pay": "인식 및 결제", "Hongbao_SendUI_Exclusive_Mode_Send_To_Who": "받는 사람", "Hongbao_SendUI_Exclusive_Mode_Send_To_Who_Error_Tips": "\"보내기\" 선택되지 않음", "Hongbao_SendUI_Amount_Data_Invalid_Error_Tips_2": "\"금액\" 입력되지 않음", "MerchantPay_Input_Remark_Hint_Format": "수취인 확인 가능. 최대 {}자.", "MerchantPay_Input_Remark_Title": "메모 추가", "MerchantPay_Transfer_To_Format": "{}에 결제", "Greeting_Hongbao_Random_Change_Amount": "금액 변경", "Greeting_Hongbao_Who_Receive_Hongbao_Format": "{}님이 열었음", "set_amount": "금액 설정"}