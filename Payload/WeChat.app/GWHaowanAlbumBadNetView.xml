<View nodeId="content" layout="width:100%,height:100%,flexDirection:column,alignItems:center,justifyContent:center" attr="background: #323232">
    <Image nodeId="badNetImage" layout="width:${162},height:${162},alignSelf:center" attr="localImage:haowanBadNetIconSvg"/>
    <Label nodeId="badNetTip" layout="marginTop:${13},alignSelf:center" attr="numberOfLines:1,fontSize:${15},color:#FFFFFF,text:{{badNetTip}}"/>
    <View nodeId="reloadBtn"  onClick="tapReloadAction"
        layout="alignSelf:center,alignItems:center,marginTop:${21},width:${80},height:${32}" attr="background: #07C160,borderRadius:3">
        <Label layout="width:100%,height:100%"
        attr="textAlign:center,fontSize: ${14},
                    color: #FFFFFF,text:{{reloadDesc}}"/>
    </View>
</View>
