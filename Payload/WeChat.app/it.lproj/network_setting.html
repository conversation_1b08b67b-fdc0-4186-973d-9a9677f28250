<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="Content-Language" content="en" charset="utf-8">
	<meta charset="UTF-8">
	<meta id="viewport" name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1; user-scalable=no;" />
	<style> html,p,h1,ul,li{margin:0px;padding:0px;} ul{list-style-type:none;} body{color:#000;font:14px/1.5 微软雅黑,Helvetica,&quot;Helvetica Neue&quot;,&quot;segoe UI Light&quot;,&quot;Kozuka Gothic Pro&quot;;} h1{font-size:16px; padding-bottom:12px;margin-bottom:5px;} .articleTitle{margin-bottom:6px;} .sec_body { margin:25px 15px;} p{margin-bottom:6px;} .bold{font-weight:bold;} .article{margin-bottom:32px;} .sec_body .content { padding:10px;border-width:0px; } @media (prefers-color-scheme: dark) { body { background-color: #232323; color: rgba(255, 255, 255, .8); } } </style>
	<title>Nessuna connessione a Internet</title>
</head>
<body class="sec_body">
	<div class="container">
		<h1>Il dispositivo non &egrave; connesso a Internet</h1>
		<div class="article">
			<p class="articleTitle">Per collegarti a Internet, prova uno dei metodi seguenti:</p>
			<ul>
				<li>Sul tuo dispositivo, vai a &quot;<strong>Impostazioni</strong>&quot; - &quot;<strong>Wi-Fi</strong>&quot; e accedi a una rete Wi-Fi disponibile.</li>
				<li>Sul tuo dispositivo, vai a &quot;<strong>Impostazioni</strong>&quot; - &quot;<strong>Cellulare</strong>&quot; e attiva &quot;<strong>Dati cellulare</strong>&quot; (il tuo fornitore di servizi potrebbe addebitare un costo per l&apos;utilizzo dei dati).</li>
			</ul>
		</div>
		<div class="article">
			<p class="articleTitle">Se hai associato il tuo dispositivo con un Apple Watch:</p>
			<ul>
				<li>Apri l&apos;app &quot;<strong>Watch</strong>&quot; - &quot;<strong>Dati cellulare</strong>&quot; - &quot;<strong>WeChat</strong>&quot; e concedi a WeChat il permesso di accedere ai dati.</li>
			</ul>
		</div>
		<div class="article">
			<p class="articleTitle">Se ti sei connesso a una rete Wi-Fi:</p>
			<ul>
				<li>Controlla che l&apos;hotspot Wi-Fi sia connesso a Internet o che al tuo dispositivo sia consentito l&apos;accesso all&apos;hotspot.</li>
			</ul>
		</div>
	</div>
</body>
</html>

