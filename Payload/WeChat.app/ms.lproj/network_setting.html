<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="Content-Language" content="en" charset="utf-8">
	<meta charset="UTF-8">
	<meta id="viewport" name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1; user-scalable=no;" />
	<style> html,p,h1,ul,li{margin:0px;padding:0px;} ul{list-style-type:none;} body{color:#000;font:14px/1.5 微软雅黑,Helvetica,&quot;Helvetica Neue&quot;,&quot;segoe UI Light&quot;,&quot;Kozuka Gothic Pro&quot;;} h1{font-size:16px; padding-bottom:12px;margin-bottom:5px;} .articleTitle{margin-bottom:6px;} .sec_body { margin:25px 15px;} p{margin-bottom:6px;} .bold{font-weight:bold;} .article{margin-bottom:32px;} .sec_body .content { padding:10px;border-width:0px; } @media (prefers-color-scheme: dark) { body { background-color: #232323; color: rgba(255, 255, 255, .8); } } </style>
	<title>Tiada Sambungan Internet</title>
</head>
<body class="sec_body">
	<div class="container">
		<h1>Peranti anda tidak tersambung ke internet</h1>
		<div class="article">
			<p class="articleTitle">Untuk menyambung ke internet, cuba kaedah di bawah:</p>
			<ul>
				<li>Pada peranti anda, pergi ke &quot;<strong>Seting</strong>&quot; - &quot;<strong>Wi-Fi</strong>&quot;, dan sertai rangkaian Wi-Fi yang tersedia.</li>
				<li>Pada peranti anda, pergi ke &quot;<strong>Seting</strong>&quot; - &quot;<strong>Selular</strong>&quot;, dan dayakan &quot;<strong>Data Selular</strong>&quot;. (Anda mungkin dikenakan bayaran oleh penyedia perkhidmatan untuk penggunaan data.)</li>
			</ul>
		</div>
		<div class="article">
			<p class="articleTitle">Apabila anda sudah memasangkan peranti anda dengan Apple Watch:</p>
			<ul>
				<li>Buka aplikasi &quot;<strong>Watch</strong>&quot; - &quot;<strong>Data Selular</strong>&quot; - &quot;<strong>WeChat</strong>&quot;, dan beri kebenaran WeChat untuk mengakses data.</li>
			</ul>
		</div>
		<div class="article">
			<p class="articleTitle">Apabila anda sudah tersambung ke rangkaian Wi-Fi:</p>
			<ul>
				<li>Semak sama ada hotspot Wi-Fi tersambung ke internet, atau sama ada peranti anda diberi kebenaran untuk mengakses hotspot.</li>
			</ul>
		</div>
	</div>
</body>
</html>

