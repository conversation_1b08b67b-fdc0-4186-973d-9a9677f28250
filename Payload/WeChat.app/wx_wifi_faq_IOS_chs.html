<!DOCTYPE HTML>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>FAQ</title>
    <meta name="viewport" content=" initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no">
    <style>
        ul{
            list-style-type: none;
            margin: 0;
            padding: 0;
        }
        .ui-mt-medium{
            margin-top: 10px;
        }
        .ui-dis-no{
            display: none;
        }
        .mod-faq{
            min-height: 100%;
            color: #000;
            background-color: #ffffff;
            padding: 0 12px;
            font: 14px/1.5em "微软雅黑","Lucida Grande", "Lucida Sans Unicode", Helvetica, Arial, Verdana, sans-serif;
        }
        .mod-faq__header{
            padding: 41px 0 3px;
            font-weight: bold;
            font-size: 15px;
            border-bottom: 1px solid #b0bebe;
            margin: 0;
        }
        .mod-faq__content{
            padding: 26px 0;
        }
        .mod-faq__brief{
            margin-bottom: 26px;
        }
        .mod-faq__list-item{
            margin-bottom: 26px;
        }
        .mod-faq__authority{
            text-align: right;
            padding-bottom: 26px;
        }
        .mod-faq__quest-list{
            line-height: 18px;
        }
        .mod-faq__quest{
            font-weight: bold;
            margin-bottom: 6px;
        }
        .mod-faq__answer{
            position: relative;
            color: #5e5e5e;
            margin-left: 0;
            padding-left: 24px;
            margin-bottom: 26px;
        }
        .mod-faq__tail{
            position: absolute;
            left: 0;
            top: 0;
            font-style: normal;
        }

    </style>
</head>
<body>
<div class="mod-faq">
    <h1 class="mod-faq__header">微信连Wi-Fi使用帮助</h1>
    <div class="mod-faq__content">
        <dl class="mod-faq__quest-list">
            <dt class="mod-faq__quest">Q：微信连Wi-Fi是什么？</dt>
            <dd class="mod-faq__answer"><i class="mod-faq__tail">A：</i>微信连Wi-Fi是通过微信即可连接互联网的方便联网方式。微信为你提供了一种方便的联网方式；Wi-Fi服务由商家及第三方网络服务商为你提供。</dd>
            <dt class="mod-faq__quest">Q：微信连Wi-Fi安全吗？</dt>
            <dd class="mod-faq__answer">
                <i class="mod-faq__tail">A：</i>你在任意场合使用Wi-Fi服务时，均建议你留意公共Wi-Fi环境上网安全。微信连Wi-Fi的服务连接成功后，将会在微信会话列表上方展示连接状态条。如果没有该状态条则可能连入的是伪造无线访问接入点，请你注意识别。
                <div class="ui-mt-medium">
                    <img src="ios-chs_1.jpg" width="200">
                </div>
                <div>
                    同时，提醒你注意：<br>
                    <ul>
                        <li>1）不要随意打开无法确认安全性的链接；</li>
                        <li>2）不要随意下载陌生应用；</li>
                        <li>3）不要在不能确认的网站输入自己微信/QQ的帐号和密码。</li>
                        <li>4）不要随意泄露自己的银行账号和密码等个人信息。</li>
                    </ul>
                </div>
            </dd>
            <dt class="mod-faq__quest">Q：如何通过微信连接互联网？</dt>
            <dd class="mod-faq__answer">
                <i class="mod-faq__tail">A：</i>微信Wi-Fi提供了扫一扫即连接互联网的方式，后续也会提供更多的连接方式，敬请期待。<br>
				
扫一扫Wi-Fi连接流程下如：<br>

				请先在你手机的“设置”中打开Wi-Fi、选取网络（如下图示意）；然后，打开微信的“发现”-“扫一扫”，扫描商家Wi-Fi二维码即会自动连接。
                <div><img src="ios7-chs_2.jpg" class="ui-mt-medium ui-dis-no" width="200" id="img2"></div>
                <div><img src="ios6-chs_2.jpg" class="ui-mt-medium ui-dis-no" width="200" id="ios6_img2"></div>
                <div><img src="chs-2v.jpg" class="ui-mt-medium" width="200"></div>

            </dd>


            <dt class="mod-faq__quest">Q：如何退出微信连接的Wi-Fi？</dt>
            <dd class="mod-faq__answer">
                <i class="mod-faq__tail">A：</i>打开设备的“设置”-“Wi-Fi”，切换到其它网络，或者，关闭“Wi-Fi”功能。
                <div>
                    <img src="ios7-chs_3.jpg" class="ui-mt-medium ui-dis-no" width="200" id="img3">
                    <img src="ios6-chs_3.jpg" class="ui-mt-medium ui-dis-no" width="200" id="ios6_img3">
                </div>
            </dd>
            <dt class="mod-faq__quest">Q: 微信连Wi-Fi连接失败，怎么办？</dt>
            <dd class="mod-faq__answer"><i class="mod-faq__tail">A：</i>可能是网络原因导致，建议你点击重新连接。如还有问题，你可以咨询现场工作人员，或者拨打服务提供商的客服电话咨询。
            </dd>
            <dt></dt>
            <dd></dd>
        </dl>
    </div>
</div>
<script type="text/javascript">
    var version = Boolean(navigator.userAgent.match(/OS [7-9]_\d[_\d]* like Mac OS X/i));
    var img2 = document.getElementById('img2'),
            ios6_img2 = document.getElementById('ios6_img2'),
            img3 = document.getElementById('img3'),
            ios6_img3 = document.getElementById('ios6_img3');

    if((navigator.userAgent.match(/iPhone/i) || navigator.userAgent.match(/iPod/i))) {
        if(version){
            img2.style.display="block";
            img3.style.display="block";
		    img4.style.display="block";

        }else{
            ios6_img2.style.display="block";
            ios6_img3.style.display="block";
			ios6_img4.style.display="block";
        }
    } else {
        ios6_img2.style.display="block";
        ios6_img3.style.display="block";
		ios6_img4.style.display="block";
    }
</script>
</body>
</html>
