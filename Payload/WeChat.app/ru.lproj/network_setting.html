<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="Content-Language" content="en" charset="utf-8">
	<meta charset="UTF-8">
	<meta id="viewport" name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1; user-scalable=no;" />
	<style> html,p,h1,ul,li{margin:0px;padding:0px;} ul{list-style-type:none;} body{color:#000;font:14px/1.5 微软雅黑,Helvetica,&quot;Helvetica Neue&quot;,&quot;segoe UI Light&quot;,&quot;Kozuka Gothic Pro&quot;;} h1{font-size:16px; padding-bottom:12px;margin-bottom:5px;} .articleTitle{margin-bottom:6px;} .sec_body { margin:25px 15px;} p{margin-bottom:6px;} .bold{font-weight:bold;} .article{margin-bottom:32px;} .sec_body .content { padding:10px;border-width:0px; } @media (prefers-color-scheme: dark) { body { background-color: #232323; color: rgba(255, 255, 255, .8); } } </style>
	<title>Отсутствует соединение с Интернетом</title>
</head>
<body class="sec_body">
	<div class="container">
		<h1>Ваше устройство не подключено к Интернету</h1>
		<div class="article">
			<p class="articleTitle">Чтобы подключиться к Интернету, попробуйте выполнить следующее:</p>
			<ul>
				<li>Откройте <strong>«Настройки»</strong> - <strong>«Wi-Fi»</strong> на устройстве и подключитесь к доступной сети Wi-Fi.</li>
				<li>Откройте <strong>«Настройки»</strong> - <strong>«Сотовая связь»</strong> на устройстве и включите функцию <strong>«Сотовые данные»</strong>. (Поставщик услуг может взимать дополнительную плату за передачу данных.)</li>
			</ul>
		</div>
		<div class="article">
			<p class="articleTitle">Если ваше устройство привязано к Apple Watch:</p>
			<ul>
				<li>Откройте в приложении <strong>Watch</strong> раздел <strong>«Сотовые данные»</strong> - <strong>«WeChat»</strong> и предоставьте приложению WeChat доступ к данным.</li>
			</ul>
		</div>
		<div class="article">
			<p class="articleTitle">Если вы подключены к сети Wi-Fi:</p>
			<ul>
				<li>Убедитесь, что точка доступа Wi-Fi подключена к Интернету и у вашего устройства есть доступ к этой точке доступа.</li>
			</ul>
		</div>
	</div>
</body>
</html>

