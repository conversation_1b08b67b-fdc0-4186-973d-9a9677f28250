<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BuildMachineOSBuild</key>
	<string>24D2059</string>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>zh_CN</string>
	<key>CFBundleDisplayName</key>
	<string>微信</string>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeName</key>
			<string>officeopenxml</string>
			<key>LSHandlerRank</key>
			<string>Alternate</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>org.openxmlformats.openxml</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeName</key>
			<string>data</string>
			<key>LSHandlerRank</key>
			<string>Alternate</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.data</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeName</key>
			<string>ppt</string>
			<key>LSHandlerRank</key>
			<string>Alternate</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.microsoft.powerpoint.ppt</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeName</key>
			<string>doc</string>
			<key>LSHandlerRank</key>
			<string>Alternate</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.microsoft.word.doc</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeName</key>
			<string>xls</string>
			<key>LSHandlerRank</key>
			<string>Alternate</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.microsoft.excel.xls</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeName</key>
			<string>pdf</string>
			<key>LSHandlerRank</key>
			<string>Alternate</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.adobe.pdf</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeName</key>
			<string>archive</string>
			<key>LSHandlerRank</key>
			<string>Alternate</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>org.gnu.gnu-tar-archive</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeName</key>
			<string>audivideo</string>
			<key>LSHandlerRank</key>
			<string>Alternate</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.audiovisual-content</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeName</key>
			<string>image</string>
			<key>LSHandlerRank</key>
			<string>Alternate</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.image</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeName</key>
			<string>txt</string>
			<key>LSHandlerRank</key>
			<string>Alternate</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.text</string>
			</array>
		</dict>
	</array>
	<key>CFBundleExecutable</key>
	<string>WeChat</string>
	<key>CFBundleIconFiles</key>
	<array>
		<string>DisplayIcon</string>
		<string>DisplayIcon@2x</string>
		<string>DisplayIcon@3x</string>
	</array>
	<key>CFBundleIdentifier</key>
	<string>com.tencent.xin</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>WeChat</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundlePrimaryIcon</key>
	<dict>
		<key>CFBundleIconFiles</key>
		<array>
			<string>DisplayIcon</string>
			<string>DisplayIcon@2x</string>
			<string>DisplayIcon@3x</string>
		</array>
		<key>CFBundleIconName</key>
		<string>DisplayIcon</string>
	</dict>
	<key>CFBundleShortVersionString</key>
	<string>8.0.60</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleSupportedPlatforms</key>
	<array>
		<string>iPhoneOS</string>
	</array>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.tencent.xin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wexinVideoAPI</string>
				<string>weixin</string>
				<string>weixinapp</string>
				<string>fb290293790992170</string>
				<string>wechat</string>
				<string>QQ41C152CF</string>
				<string>prefs</string>
				<string>wx703</string>
				<string>weixinULAPI</string>
				<string>wx7015</string>
				<string>mp</string>
				<string>weixinStateAPI</string>
				<string>weixinVideoStateAPI</string>
				<string>weixinQRCodePayAPI</string>
				<string>weixinURLParamsAPI</string>
				<string>weixinVideoLocalIdAPI</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>*********</string>
	<key>DTAppStoreToolsBuild</key>
	<string>16F3</string>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTPlatformBuild</key>
	<string>22C146</string>
	<key>DTPlatformName</key>
	<string>iphoneos</string>
	<key>DTPlatformVersion</key>
	<string>18.2</string>
	<key>DTSDKBuild</key>
	<string>22C146</string>
	<key>DTSDKName</key>
	<string>iphoneos18.2</string>
	<key>DTXcode</key>
	<string>1620</string>
	<key>DTXcodeBuild</key>
	<string>16C5032a</string>
	<key>FLTEnableImpeller</key>
	<false/>
	<key>FLTEnableWideGamut</key>
	<false/>
	<key>FacebookAppID</key>
	<string>290293790992170</string>
	<key>FacebookAutoInitEnabled</key>
	<false/>
	<key>FacebookAutoLogAppEventsEnabled</key>
	<false/>
	<key>From DumpDecrypter</key>
	<true/>
	<key>ITSDRMScheme</key>
	<string>v2</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>wx50a3272e1669f0c0</string>
		<string>cyberidentity</string>
		<string>qqnews</string>
		<string>weixinbeta</string>
		<string>qqnewshd</string>
		<string>qqmail</string>
		<string>whatsapp</string>
		<string>wxwork</string>
		<string>mttbrowser</string>
		<string>mqqapi</string>
		<string>mqzonev2</string>
		<string>qqmusic</string>
		<string>tenvideo2</string>
		<string>tenvideohd</string>
		<string>qnreading</string>
		<string>weread</string>
		<string>sosomap</string>
		<string>comgooglemaps</string>
		<string>iosamap</string>
		<string>baidumap</string>
		<string>sgmap</string>
		<string>fbauth2</string>
		<string>wx76fc280041c16519</string>
		<string>wx3bef52104e238bff</string>
		<string>rijvideo</string>
		<string>wx95a3a4d7c627e07d</string>
		<string>wx44b60f3ea0534cd7</string>
		<string>wxacbfe7e1bb3e800f</string>
		<string>wxfdab5af74990787a</string>
		<string>wx58164a91f1821369</string>
		<string>wx82dd7436af5db835</string>
		<string>qqstock</string>
		<string>openApp.jdMobile</string>
		<string>qmkege</string>
		<string>mqzonex</string>
		<string>wx5a4a8ac0fd48303a</string>
		<string>qqsports</string>
		<string>wetype</string>
		<string>tencentedu</string>
		<string>wemusic</string>
		<string>qqmap</string>
		<string>mqq</string>
		<string>webank</string>
		<string>weiyun</string>
		<string>weishi</string>
		<string>wemeet</string>
		<string>miaojian</string>
		<string>uppaywallet</string>
		<string>wxworkgovuniform</string>
		<string>dcep</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<false/>
	<key>MidasConfig</key>
	<dict>
		<key>HideLoading</key>
		<true/>
	</dict>
	<key>MinimumOSVersion</key>
	<string>14.0</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>Allow WeChat Use Bluetooth Peripheral.</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>Allow WeChat Use Bluetooth Peripheral.</string>
	<key>NSBonjourServices</key>
	<array>
		<string>_wechat-chatlog._tcp</string>
		<string>_wechat-chatlog._udp</string>
		<string>_airplay._tcp</string>
		<string>_ktcp-remote._tcp</string>
		<string>_homeconnect._tcp</string>
	</array>
	<key>NSCalendarsUsageDescription</key>
	<string>Allow WeChat Use your Calendars.</string>
	<key>NSCameraUsageDescription</key>
	<string>Use your Camera to record video, using VoIP features.</string>
	<key>NSContactsUsageDescription</key>
	<string>Allow WeChat Use your Contact.</string>
	<key>NSFaceIDUsageDescription</key>
	<string>Allow WeChat Use FaceID.</string>
	<key>NSFocusStatusUsageDescription</key>
	<string>Allow WeChat Get your Focus Status.</string>
	<key>NSHealthShareUsageDescription</key>
	<string>Allow WeChat Use Health Share.</string>
	<key>NSHealthUpdateUsageDescription</key>
	<string>Allow WeChat Use Health Update.</string>
	<key>NSLocalNetworkUsageDescription</key>
	<string>This app will be able to discover and connect to devices on the networks you use.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Allow WeChat Use your Location always.</string>
	<key>NSLocationTemporaryUsageDescriptionDictionary</key>
	<dict>
		<key>NSLocationTrackingUsageFullAccuracyDescription</key>
		<string>Allow WeChat Use FullAccuracy Location.</string>
	</dict>
	<key>NSLocationUsageDescription</key>
	<string>Allow WeChat Use your Location.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Allow WeChat Use your Location when in use.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Use your Microphone to record voice messages, using VoIP features.</string>
	<key>NSMotionUsageDescription</key>
	<string>Allow WeChat Use Motion.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Allow WeChat Save Photo.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Allow WeChat Use your PhotoLibrary.</string>
	<key>NSSiriUsageDescription</key>
	<string>Allow WeChat Use Siri.</string>
	<key>NSUserActivityTypes</key>
	<array>
		<string>com.tencent.xin</string>
		<string>com.tencent.xin.watch</string>
		<string>INStartCallIntent</string>
		<string>INSendMessageIntent</string>
	</array>
	<key>OSLogPreferences</key>
	<dict>
		<key>com.apple.CFBundle</key>
		<dict>
			<key>resources</key>
			<dict>
				<key>Level</key>
				<dict>
					<key>Enable</key>
					<string>Off</string>
				</dict>
			</dict>
			<key>strings</key>
			<dict>
				<key>Level</key>
				<dict>
					<key>Enable</key>
					<string>Off</string>
				</dict>
			</dict>
		</dict>
		<key>com.apple.SystemConfiguration</key>
		<dict>
			<key>SCNetworkReachability</key>
			<dict>
				<key>Level</key>
				<dict>
					<key>Enable</key>
					<string>Off</string>
				</dict>
			</dict>
		</dict>
		<key>com.apple.UIKit</key>
		<dict>
			<key>EventDispatch</key>
			<dict>
				<key>Level</key>
				<dict>
					<key>Enable</key>
					<string>Off</string>
				</dict>
			</dict>
			<key>Feedback</key>
			<dict>
				<key>Level</key>
				<dict>
					<key>Enable</key>
					<string>Off</string>
				</dict>
			</dict>
			<key>KeyboardVisualMode</key>
			<dict>
				<key>Level</key>
				<dict>
					<key>Enable</key>
					<string>Off</string>
				</dict>
			</dict>
			<key>UIDevice.battery</key>
			<dict>
				<key>Level</key>
				<dict>
					<key>Enable</key>
					<string>Off</string>
				</dict>
			</dict>
			<key>UIPeripheralHost</key>
			<dict>
				<key>Level</key>
				<dict>
					<key>Enable</key>
					<string>Off</string>
				</dict>
			</dict>
		</dict>
		<key>com.apple.cache_delete</key>
		<dict>
			<key>DEFAULT-OPTIONS</key>
			<dict>
				<key>Level</key>
				<dict>
					<key>Enable</key>
					<string>Off</string>
				</dict>
			</dict>
		</dict>
		<key>com.apple.coreaudio</key>
		<dict>
			<key>hcln</key>
			<dict>
				<key>Level</key>
				<dict>
					<key>Enable</key>
					<string>Off</string>
				</dict>
			</dict>
		</dict>
		<key>com.apple.corehaptics</key>
		<dict>
			<key>DEFAULT-OPTIONS</key>
			<dict>
				<key>Level</key>
				<dict>
					<key>Enable</key>
					<string>Off</string>
				</dict>
			</dict>
		</dict>
		<key>com.apple.corespotlight</key>
		<dict>
			<key>DEFAULT-OPTIONS</key>
			<dict>
				<key>Level</key>
				<dict>
					<key>Enable</key>
					<string>Off</string>
				</dict>
			</dict>
		</dict>
		<key>com.apple.defaults</key>
		<dict>
			<key>DEFAULT-OPTIONS</key>
			<dict>
				<key>Level</key>
				<dict>
					<key>Enable</key>
					<string>Off</string>
				</dict>
			</dict>
		</dict>
		<key>com.apple.espresso</key>
		<dict>
			<key>DEFAULT-OPTIONS</key>
			<dict>
				<key>Level</key>
				<dict>
					<key>Enable</key>
					<string>Off</string>
				</dict>
			</dict>
		</dict>
		<key>com.apple.locationd.Core</key>
		<dict>
			<key>DEFAULT-OPTIONS</key>
			<dict>
				<key>Level</key>
				<dict>
					<key>Enable</key>
					<string>Off</string>
				</dict>
			</dict>
		</dict>
		<key>com.apple.locationd.Motion</key>
		<dict>
			<key>DEFAULT-OPTIONS</key>
			<dict>
				<key>Level</key>
				<dict>
					<key>Enable</key>
					<string>Off</string>
				</dict>
			</dict>
		</dict>
		<key>com.apple.network</key>
		<dict>
			<key>DEFAULT-OPTIONS</key>
			<dict>
				<key>Level</key>
				<dict>
					<key>Enable</key>
					<string>Off</string>
				</dict>
			</dict>
		</dict>
		<key>com.apple.network.libinfo</key>
		<dict>
			<key>DEFAULT-OPTIONS</key>
			<dict>
				<key>Level</key>
				<dict>
					<key>Enable</key>
					<string>Off</string>
				</dict>
			</dict>
		</dict>
		<key>com.apple.networkextension</key>
		<dict>
			<key>DEFAULT-OPTIONS</key>
			<dict>
				<key>Level</key>
				<dict>
					<key>Enable</key>
					<string>Off</string>
				</dict>
			</dict>
		</dict>
	</dict>
	<key>PHPhotoLibraryPreventAutomaticLimitedAccessAlert</key>
	<true/>
	<key>SKAdNetworkItems</key>
	<array>
		<dict>
			<key>SKAdNetworkIdentifier</key>
			<string>f7s53z58qe.skadnetwork</string>
		</dict>
	</array>
	<key>UIAppFonts</key>
	<array>
		<string>WeChatSansStd-Bold.ttf</string>
		<string>WeChatSansStd-Medium.ttf</string>
		<string>WeChatSansStd-Regular.ttf</string>
		<string>WeChatSansSuperscript-Bold.ttf</string>
		<string>WeChatSansSuperscript-Regular.ttf</string>
		<string>WeChatSansSuperscript-Medium.ttf</string>
		<string>WeChatSansStd-Medium-PreHot.ttf</string>
	</array>
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<true/>
		<key>UISceneConfigurations</key>
		<dict>
			<key>CPTemplateApplicationSceneSessionRoleApplication</key>
			<array>
				<dict>
					<key>UISceneClassName</key>
					<string>CPTemplateApplicationScene</string>
					<key>UISceneConfigurationName</key>
					<string>CarPlayScene</string>
					<key>UISceneDelegateClassName</key>
					<string>WeChat.CarPlayScene</string>
				</dict>
			</array>
			<key>UIWindowSceneSessionRoleApplication</key>
			<array>
				<dict>
					<key>UISceneConfigurationName</key>
					<string>MainScene</string>
					<key>UISceneDelegateClassName</key>
					<string>MMProxySceneDelegate</string>
				</dict>
				<dict>
					<key>UISceneConfigurationName</key>
					<string>SubScene</string>
					<key>UISceneDelegateClassName</key>
					<string>MMProxySceneDelegate</string>
				</dict>
			</array>
		</dict>
	</dict>
	<key>UIBackgroundModes</key>
	<array>
		<string>location</string>
		<string>audio</string>
		<string>fetch</string>
		<string>voip</string>
		<string>remote-notification</string>
		<string>network-authentication</string>
	</array>
	<key>UIDeviceFamily</key>
	<array>
		<integer>1</integer>
		<integer>2</integer>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>Launch Screen</string>
	<key>UIPrerenderedIcon</key>
	<true/>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UIStatusBarHidden</key>
	<true/>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleBlackOpaque</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UISupportsDocumentBrowser</key>
	<false/>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>
	<key>UIWindowSceneSessionRoleExternalDisplay</key>
	<array>
		<dict>
			<key>UISceneConfigurationName</key>
			<string>SubScene</string>
			<key>UISceneDelegateClassName</key>
			<string>MMProxySceneDelegate</string>
		</dict>
	</array>
	<key>UIWindowSceneSessionRoleExternalDisplayNonInteractive</key>
	<array>
		<dict>
			<key>UISceneConfigurationName</key>
			<string>SubScene</string>
			<key>UISceneDelegateClassName</key>
			<string>MMProxySceneDelegate</string>
		</dict>
	</array>
	<key>WeChatBundleVersion</key>
	<string>*********</string>
	<key>WeChatFeatureIdentifier</key>
	<string>release_appstore_8.0.60</string>
	<key>build_time</key>
	<string>2025-05-22 11:27:09</string>
	<key>by</key>
	<string>wx_ios_code_helper</string>
	<key>path</key>
	<string>release@appstore@ios_2025-t2_1-8-0-60_2025_4_27_15_40_46</string>
	<key>qmap_service</key>
	<string>server_protocol.json</string>
	<key>rev</key>
	<string>a7a7e7e6a11e8a88bea28397cd2ac51538ece49e</string>
	<key>tag</key>
	<string>release_appstore_8.0.60 #17</string>
	<key>uuid</key>
	<string>c1dc4ca27e6e3bbab9d73078c56f9386</string>
	<key>ver</key>
	<string>0x18003c23</string>
</dict>
</plist>
