local stickers = {}
local firstFrame = 1
local maxSupportSticker =-1
local maxSupportInstanceCount = 5
local currentUnactivatedCount = 5
local currentFloatCounter = 0.0
local floatCounterStep = 0.6
local maxSupportHandCount = 5
local physicalSimulateRate = 15
local previousTime = 0.0
local currentTime = 0.0
local renderWidth = EffectContext.RenderWidth
local renderHeight = EffectContext.RenderHeight
local detectWidth = EffectContext.DetectWidth
local detectHeight = EffectContext.DetectHeight
local SELF_ID = EffectContext.getEnvVar("SELF_ID")
local lua_path = EffectContext.getEnvVar("FILTER_PATH")
local loadMaterial = 0
local StickerCreator = {id = 0}
local startTime = 0
local timeCounter = 0
local handVelocity = {}
local preHandPos = {} 
local handCounter = 0
local collisionTimeStep = 0.5
local elasticCoefficient = 0.4
local angleCoefficient = 0.2
local maskWidth = 0
local maskHeight = 0
local inited = 0
local resetParams = 1
local materialPath = ""
local defaultPath = "./../StickerFixed_4707341639292055168"
local timeOut = 8.0
local startAlpha = 7.0
local stickerFixedAlpha = 1.0

Matrix = {}
Matrix.__index = Matrix

-- matrix multiply
function Matrix:new(rows, cols)
    local matrix = {}
    setmetatable(matrix, Matrix)
    for i = 1, rows do
        matrix[i] = {}
        for j = 1, cols do
            matrix[i][j] = 0
        end
    end
    return matrix
end

function Matrix:multiply(other)
    local result = Matrix:new(#self, #other[1])
    for i = 1, #self do
        for j = 1, #other[1] do
            for k = 1, #other do
                result[i][j] = result[i][j] + self[i][k] * other[k][j]
            end
        end
    end
    return result
end

-- trans rotation scale matrix
function scaleMatrix(sx, sy, sz)
    local matrix = Matrix:new(4, 4)
    matrix[1][1] = sx
    matrix[2][2] = sy
    matrix[3][3] = sz
    matrix[4][4] = 1
    return matrix
end

function rotateMatrix(rz)
    local matrix = Matrix:new(4, 4)
    local cosRz = math.cos(rz)
    local sinRz = math.sin(rz)
    matrix[1][1] = cosRz
    matrix[1][2] = -sinRz
    matrix[2][1] = sinRz
    matrix[2][2] = cosRz
    matrix[3][3] = 1
    matrix[4][4] = 1
    return matrix
end

function translateMatrix(tx, ty, tz)
    local matrix = Matrix:new(4, 4)
    for i = 1, 4 do
        matrix[i][i] = 1
    end
    matrix[1][4] = tx
    matrix[2][4] = ty
    matrix[3][4] = tz
    return matrix
end

function transformModel(sx, sy, sz, rx, ry, rz, tx, ty, tz)
    local scale = scaleMatrix(sx, sy, sz)
    
    local rotate = rotateMatrix(rz)
    local translate = translateMatrix(tx, ty, tz)
    local transform = translate:multiply(rotate):multiply(scale)
    return transform
end

StickerCreator.__index = StickerCreator

function dot(v1, v2)
    return v1.x * v2.x + v1.y * v2.y
end

function subtract(v1, v2)
    return {x = v1.x - v2.x, y = v1.y - v2.y}
end

function multiplyScalar(v, scalar)
    return {x = v.x * scalar, y = v.y * scalar}
end

function length(v)
    return math.sqrt(v.x * v.x + v.y * v.y)
end

function normalize(v)
    local len = length(v)
    return {x = v.x / len, y = v.y / len}
end

function tableSum(t)
    local sum = 0
    for _, v in ipairs(t) do
        sum = sum + v
    end
    return sum
end

function StickerCreator:new(path, type)
    local self = {}
    setmetatable(self, StickerCreator)
    local ret = EffectContext.sdk:AddEffectMaterial({path = path, type = type})
    self.id = ret.id
    self.config = ret.config
    self.instanceCount = maxSupportInstanceCount
    self.acceleration = {}
    self.velocity = {}
    self.angleVelocity = {}
    self.startPosition = {}
    self.currentPos = {}
    self.scale = {}
    self.rotation = {}
    self.collisionNum = {}
    self.enableSticker = {}
    -- magic number for mass
    self.mass = 15.0 * math.max(renderHeight, renderWidth)
    self.dead = {}
    self.timer = {}

    for i=1, maxSupportInstanceCount do
        table.insert(self.acceleration, {0.0, -math.random(500, 600)/2000.0*math.max(renderHeight,renderWidth)})
        table.insert(self.velocity, {0.0,  -math.random(500, 600)/2000.0*math.max(renderHeight, renderWidth)})
        table.insert(self.angleVelocity, 0.0)
        table.insert(self.startPosition, {0.0, 0.0})
        table.insert(self.currentPos, {math.random(10, 90)*0.01*renderWidth, renderHeight*0.02+renderHeight})
        table.insert(self.scale, 0.0)
        table.insert(self.rotation, math.random(-30, 30)*3.14159/180)
        table.insert(self.collisionNum, 1)
        table.insert(self.enableSticker, 0)
        table.insert(self.timer, 0.0)
        table.insert(self.dead, 0)
    end  
    return self
end

EffectContext.logi("[X3DEffects] detectWidth, detectHeight: ", detectWidth, detectHeight)
EffectContext.logi("[X3DEffects] renderWidth, renderHeight: ", renderWidth, renderHeight)

function StickerCreator:UpdateSwitch(sw)
    EffectContext.sdk:SetParam({id = self.id, param = {enable = sw}})
end

function StickerCreator:SetTransparency(transparency)
    EffectContext.sdk:SetParam({id = self.id, param = {alpha = transparency}})
end

function StickerCreator:UpdateRST(instanceID)
    modelMatInstane = transformModel(self.scale[instanceID], self.scale[instanceID], self.scale[instanceID], 0.0, 0.0, self.rotation[instanceID], self.currentPos[instanceID][1]*2-renderWidth, self.currentPos[instanceID][2]*2-renderHeight, 0.0)
    EffectContext.sdk:SetParam({
        id = self.id,        
        param = {
            -- rotate = {[instanceID] = {0.0, 0.0, self.rotation[instanceID]}},
            -- scale = {[instanceID] = {self.scale[instanceID], self.scale[instanceID], self.scale[instanceID]}},
            -- trans = {[instanceID] = {self.currentPos[instanceID][1]*2-renderWidth, self.currentPos[instanceID][2]*2-renderHeight, 0.0}},
            modelMat = {[instanceID] = {modelMatInstane[1][1], modelMatInstane[1][2], modelMatInstane[1][3], modelMatInstane[1][4], modelMatInstane[2][1], modelMatInstane[2][2], modelMatInstane[2][3], modelMatInstane[2][4], modelMatInstane[3][1], modelMatInstane[3][2], modelMatInstane[3][3], modelMatInstane[3][4], modelMatInstane[4][1], modelMatInstane[4][2], modelMatInstane[4][3], modelMatInstane[4][4]}}
        }
    })
end

function StickerCreator:updateInstanceCount()
    EffectContext.sdk:SetParam({
        id = self.id,
        param = {instanceCount = self.instanceCount}
    })
end

function StickerCreator:setpauseSelfUpdate(pause)
    EffectContext.sdk:SetParam({id = self.id, pauseSelfUpdate = pause})
end

function calculateStickerPosition(stickerName, deltaTime, instanceID)
    stickers[stickerName].velocity[instanceID][1] = stickers[stickerName].velocity[instanceID][1] + stickers[stickerName].acceleration[instanceID][1] * deltaTime
    stickers[stickerName].velocity[instanceID][2] = stickers[stickerName].velocity[instanceID][2] + stickers[stickerName].acceleration[instanceID][2] * deltaTime
    if length({x=stickers[stickerName].velocity[instanceID][1], y=stickers[stickerName].velocity[instanceID][2]})/renderHeight > 1.0 then
        local v = normalize({x=stickers[stickerName].velocity[instanceID][1], y=stickers[stickerName].velocity[instanceID][2]})
        stickers[stickerName].velocity[instanceID][1] = v.x * renderHeight * 1.0
        stickers[stickerName].velocity[instanceID][2] = v.y * renderHeight * 1.0
    end
    stickers[stickerName].currentPos[instanceID][1] = stickers[stickerName].currentPos[instanceID][1] + stickers[stickerName].velocity[instanceID][1] * deltaTime
    stickers[stickerName].currentPos[instanceID][2] = stickers[stickerName].currentPos[instanceID][2] + stickers[stickerName].velocity[instanceID][2] * deltaTime
    -- print("velocity y:", stickers[stickerName].velocity[instanceID][2])
    -- print("delta y", stickers[stickerName].velocity[instanceID][2] * deltaTime)
    -- 0~renderwidht to -renderwidth~renderwidht
    positionX = stickers[stickerName].currentPos[instanceID][1] * 2 - renderWidth
    positionY = stickers[stickerName].currentPos[instanceID][2] * 2 - renderHeight

    angle = stickers[stickerName].rotation[instanceID] + stickers[stickerName].angleVelocity[instanceID] * deltaTime
    stickers[stickerName].rotation[instanceID] = angle

    if stickers[stickerName].collisionNum[instanceID] == 0 then
        stickers[stickerName].timer[instanceID] = deltaTime + stickers[stickerName].timer[instanceID]
    end

    if stickers[stickerName].timer[instanceID] > collisionTimeStep and stickers[stickerName].collisionNum[instanceID] == 0 then
        stickers[stickerName].timer[instanceID] = 0
        stickers[stickerName].collisionNum[instanceID] = 1
    end

    return positionX, positionY, angle
end

function calculateAngularVelocity(normal, stickerName, instanceID)
    local relativeVelocity = stickers[stickerName].velocity[instanceID]
    local dotProduct = normal.x * relativeVelocity[1] + normal.y * relativeVelocity[2]
    if normal.x < 0 then
        dotProduct = -dotProduct
    end
    local angularVelocity = dotProduct / (stickers[stickerName].mass)
    return angularVelocity
end

function bodyCollisionDetection(stickerName, instanceID)
    local posX, posY = stickers[stickerName].currentPos[instanceID][1], stickers[stickerName].currentPos[instanceID][2]
    -- EffectContext.logi("[X3DEffects] bodyCollisionDetection", posX, posY)
    local maskVal=0.0
    if EffectContext.mirror > 0.1 then
        maskVal = EffectContext.Tracker:MaskSampler(math.max(math.min(1.0-posX/renderWidth, 1.0), 0.0), math.max(math.min(1.0-posY/renderHeight, 1.0), 0.0))
    else
        maskVal = EffectContext.Tracker:MaskSampler(1.0-math.max(math.min(1.0-posX/renderWidth, 1.0), 0.0), math.max(math.min(1.0-posY/renderHeight, 1.0), 0.0))
    end
    -- EffectContext.logi("[X3DEffects] current mask value", maskVal)
    if maskVal > 10 then
        return 1
    else
        return 0
    end
end

function updateHandVelocity(resetFlag, deltaTime)
    local handCount = EffectContext.Tracker:GetHandCount()
    for j=0, handCount-1 do
        local landmarks = EffectContext.Tracker:GetHandLandmarks2D(j)
        if (#landmarks == 0) then
            EffectContext.logi("TRACKER ERROR: handness or landmarks error")
        else
            local handCenter = {x=landmarks[10].x, y= renderHeight - landmarks[10].y}
            if resetFlag == 0 then
                handVelocity[j+1] = {(handCenter.x - preHandPos[j+1][1])/deltaTime, (handCenter.y - preHandPos[j+1][2])/deltaTime}
                preHandPos[j+1] = {handCenter.x, handCenter.y}
            else
                handVelocity[j+1] = {0, 0}
                preHandPos[j+1] = {0, 0}
            end
        end
    end
end

function handCollisionDetection(stickerName, instanceID)
    local posX, posY = stickers[stickerName].currentPos[instanceID][1], stickers[stickerName].currentPos[instanceID][2]
    local handCount = EffectContext.Tracker:GetHandCount()
    for j = 0, handCount - 1 do
        local landmarks = EffectContext.Tracker:GetHandLandmarks2D(j)
        if (#landmarks == 0) then
            EffectContext.logi("TRACKER ERROR: handness or landmarks error")
            return -1
        else
            local handlength = math.max(math.sqrt((landmarks[18].x - landmarks[3].x) * (landmarks[18].x - landmarks[3].x) + (landmarks[18].y - landmarks[3].y) * (landmarks[18].y - landmarks[3].y)),
                                  math.sqrt((landmarks[11].x - landmarks[1].x) * (landmarks[11].x - landmarks[1].x) + (landmarks[11].y - landmarks[1].y) * (landmarks[11].y - landmarks[1].y)))
            local handCenter = {x=(landmarks[10].x+landmarks[3].x+landmarks[6].x+landmarks[14].x+landmarks[18].x+landmarks[1].x)/6.0, y= renderHeight - (landmarks[10].y+landmarks[3].y+landmarks[6].y+landmarks[14].y+landmarks[18].y+landmarks[1].y)/6.0}
            if(math.sqrt((posX-handCenter.x)*(posX-handCenter.x)+(posY-handCenter.y)*(posY-handCenter.y))<=handlength) then
                return j
            end
        end
    end
    return -1
end

function handComputeReflection(stickerName, instanceID, handID)
    local posX, posY = stickers[stickerName].currentPos[instanceID][1], stickers[stickerName].currentPos[instanceID][2]
    local landmarks = EffectContext.Tracker:GetHandLandmarks2D(handID)
    if (#landmarks == 0) then
        EffectContext.logi("TRACKER ERROR: handness or landmarks error")
        return {x=1.0, y=0.0}, 0.0
    else
        local handPos = {x=landmarks[10].x, y= renderHeight - landmarks[10].y}
        local handVec = handVelocity[handID+1]
        reflectVec = {(handVec[1] - stickers[stickerName].velocity[instanceID][1])*0.8, (handVec[2] - stickers[stickerName].velocity[instanceID][2])*0.8}
        -- EffectContext.loge("handComputeReflection")
        -- EffectContext.loge(handVec[1], handVec[2])
        -- EffectContext.loge(stickers[stickerName].velocity[instanceID][1], stickers[stickerName].velocity[instanceID][2])

        local normalVec = {x=posX-handPos.x, y=posY-handPos.y}
        local anglarVelocity = calculateAngularVelocity(normalVec, stickerName, instanceID)
        -- EffectContext.loge(anglarVelocity)
        return {x=reflectVec[1], y=reflectVec[2]}, anglarVelocity
        -- EffectContext.loge("handComputeReflection")
    end
end

function bodyComputeReflection(stickerName, instanceID)
    local posX, posY = stickers[stickerName].currentPos[instanceID][1], stickers[stickerName].currentPos[instanceID][2]
    -- -- debug info
    -- EffectContext.logi("[X3DEffects] get reflection value", stickerName)
    -- EffectContext.logi("[X3DEffects] current position", posX, posY)
    -- local normalVec = EffectContext.Tracker:ComputeMaskGradient(math.max(math.min(1.0-posX/renderWidth, 1.0), 0.0), math.max(math.min(1.0-posY/renderHeight, 1.0), 0.0))
    local normalVec = computeMaskGradient(1.0-posX/renderWidth, 1.0-posY/renderHeight)
    local angularVelocity = calculateAngularVelocity(normalVec, stickerName, instanceID)
    local normalN = normalize({x = normalVec.x, y = normalVec.y})
    local normalI = normalize({x = stickers[stickerName].velocity[instanceID][1], y = stickers[stickerName].velocity[instanceID][2]})
    return subtract(normalI, multiplyScalar(normalN, 2 * dot(normalN, normalI))), angularVelocity
end

function computeMaskGradient(x, y)
    leftCoordinate = {x*maskWidth-1, y*maskHeight}
    rightCoordinate = {x*maskWidth+1, y*maskHeight}
    bottomCoordinate = {x*maskWidth, y*maskHeight+1}
    topCoordinate = {x*maskWidth, y*maskHeight-1}    
    if EffectContext.mirror > 0.1 then
        leftPix = EffectContext.Tracker:MaskSampler(math.max(math.min(leftCoordinate[1]/maskWidth, 1.0), 0.0), math.max(math.min(leftCoordinate[2]/maskHeight, 1.0), 0.0))
        rightPix = EffectContext.Tracker:MaskSampler(math.max(math.min(rightCoordinate[1]/maskWidth, 1.0), 0.0), math.max(math.min(rightCoordinate[2]/maskHeight, 1.0), 0.0))
        bottomPix = EffectContext.Tracker:MaskSampler(math.max(math.min(bottomCoordinate[1]/maskWidth, 1.0), 0.0), math.max(math.min(bottomCoordinate[2]/maskHeight, 1.0), 0.0))
        topPix = EffectContext.Tracker:MaskSampler(math.max(math.min(topCoordinate[1]/maskWidth, 1.0), 0.0), math.max(math.min(topCoordinate[2]/maskHeight, 1.0), 0.0))
    else
        leftPix = EffectContext.Tracker:MaskSampler(1.0-math.max(math.min(leftCoordinate[1]/maskWidth, 1.0), 0.0), math.max(math.min(leftCoordinate[2]/maskHeight, 1.0), 0.0))
        rightPix = EffectContext.Tracker:MaskSampler(1.0-math.max(math.min(rightCoordinate[1]/maskWidth, 1.0), 0.0), math.max(math.min(rightCoordinate[2]/maskHeight, 1.0), 0.0))
        bottomPix = EffectContext.Tracker:MaskSampler(1.0-math.max(math.min(bottomCoordinate[1]/maskWidth, 1.0), 0.0), math.max(math.min(bottomCoordinate[2]/maskHeight, 1.0), 0.0))
        topPix = EffectContext.Tracker:MaskSampler(1.0-math.max(math.min(topCoordinate[1]/maskWidth, 1.0), 0.0), math.max(math.min(topCoordinate[2]/maskHeight, 1.0), 0.0))
    end
    
    -- -- debug info
    -- EffectContext.logi("left: ", math.max(math.min(leftCoordinate[1]/maskWidth, 1.0), 0.0), math.max(math.min(leftCoordinate[2]/maskHeight, 1.0), 0.0))
    -- EffectContext.logi("right: ", math.max(math.min(rightCoordinate[1]/maskWidth, 1.0), 0.0), math.max(math.min(rightCoordinate[2]/maskHeight, 1.0), 0.0))
    -- EffectContext.logi("leftPix, rightPix: ",leftPix, rightPix)
    gradientX = (leftPix - rightPix)
    gradientY = (topPix - bottomPix)
    return {x=gradientX, y=gradientY}
end


function randomActiveSticker()
    local stickerID = 0
    local stickerInstanceID = 0
    for stickerID=0, maxSupportSticker-1, 1 do
        for stickerInstanceID=1, maxSupportInstanceCount, 1 do
            if stickers["sticker"..tostring(stickerID)].enableSticker[stickerInstanceID] == 0 and stickers["sticker"..tostring(stickerID)].dead[stickerInstanceID] == 0 then
                if math.random(0, 100) > (95+currentUnactivatedCount*0.1) then
                    -- EffectContext.logi("[X3DEffects] active sticker ", stickerID, stickerInstanceID)
                    stickers["sticker"..tostring(stickerID)].enableSticker[stickerInstanceID] = 1
                    stickers["sticker"..tostring(stickerID)].scale[stickerInstanceID] = 0.025 * math.random(43, 48)
                    stickers["sticker"..tostring(stickerID)]:UpdateSwitch(1)
                    currentUnactivatedCount = currentUnactivatedCount - 1
                end
            end
        end
        
    end
end

function activeSticker()
    local currentActivateIndex = math.floor(currentFloatCounter)
    if currentActivateIndex < maxSupportInstanceCount * maxSupportSticker then
        
        local stickerID = math.floor(currentActivateIndex / maxSupportInstanceCount)
        local stickerInstanceID = currentActivateIndex % maxSupportInstanceCount + 1
        if stickers["sticker"..tostring(stickerID)].enableSticker[stickerInstanceID] == 0 and stickers["sticker"..tostring(stickerID)].dead[stickerInstanceID] == 0 then
            stickers["sticker"..tostring(stickerID)].enableSticker[stickerInstanceID] = 1
            stickers["sticker"..tostring(stickerID)].scale[stickerInstanceID] = 0.025 * math.random(43, 48)
            if renderHeight < renderWidth then
                stickers["sticker"..tostring(stickerID)].scale[stickerInstanceID] = stickers["sticker"..tostring(stickerID)].scale[stickerInstanceID]*renderHeight/renderWidth
            end
            stickers["sticker"..tostring(stickerID)]:UpdateSwitch(1)
            -- EffectContext.logi("[X3DEffects] active sticker ", stickerID, stickerInstanceID)
        end
    end
end

function killSticker()
    local stickerID = 0
    local stickerInstanceID = 0
    for stickerID=0, maxSupportSticker-1, 1 do
        for stickerInstanceID=1, maxSupportInstanceCount, 1 do
            if stickers["sticker"..tostring(stickerID)].enableSticker[stickerInstanceID] == 1 and stickers["sticker"..tostring(stickerID)].currentPos[stickerInstanceID][2] < renderHeight * 0.1 and stickers["sticker"..tostring(stickerID)].dead[stickerInstanceID] == 0 then
                stickers["sticker"..tostring(stickerID)].dead[stickerInstanceID] = 1
                stickers["sticker"..tostring(stickerID)].scale[stickerInstanceID] = 0.0
                stickers["sticker"..tostring(stickerID)]:UpdateRST(stickerInstanceID)
                -- EffectContext.logi("[X3DEffects] kill sticker ", stickerID, stickerInstanceID)
                if tableSum(stickers["sticker"..tostring(stickerID)].dead) == maxSupportInstanceCount then
                    stickers["sticker"..tostring(stickerID)]:UpdateSwitch(0)
                    -- EffectContext.logi("[X3DEffects] sticker update swith 0 ", stickerID)
                end
            end
        end
    end
end

function checkEffectFinished()
    local stickerID = 0
    local stickerInstanceID = 0
    timeCounter = EffectContext.sdk:GetCurrentTime()

    if (timeCounter - startTime)*0.001 > timeOut then
        EffectContext.logi("[X3DEffects][lua] effect time out ")
        EffectContext.sdk:SendMessage({ id = SELF_ID, message = { id = SELF_ID, animEnd = 0 } })
        if resetParams == 0 then
            resetAllParams()
        end        
    end

    for stickerID=0, maxSupportSticker-1, 1 do
        if tableSum(stickers["sticker"..tostring(stickerID)].dead) ~= maxSupportInstanceCount then
            return 0
        end
    end
    EffectContext.logi("[X3DEffects][lua] effect playback end ")
    EffectContext.sdk:SendMessage({ id = SELF_ID, message = { id = SELF_ID, animEnd = 1 } })
    if resetParams == 0 then
        resetAllParams()
    end        
    return 1
end

function updateAbsolutePath(basePath, absolutePath)
    print(updateAbsolutePath)
    contentJson = EffectContext.decodeJsonFile(basePath.."/config.json")
    print(contentJson)
    for k,v in pairs(contentJson) do
        if(k == "params") then
            params = v
            for kp, vp in pairs(params) do
                if(kp == "frame") then 
                    vp["absolutePath"] = absolutePath
                end

            end
        end    
    end
    print(contentJson)
    value = EffectContext.encodeJson(contentJson)
    outputFile = lua_path .. basePath .. "/config.json"
	EffectContext.logi("[X3DEffects][lua]: lua path ", lua_path)
    local file = io.open(outputFile, 'w+')
	if not file then
		EffectContext.logi("open file error")
	else
		io.output(file)
		io.write(value)
		io.close(file)
	end
    
end

function resetAllParams()
    for stickerID=0, maxSupportSticker-1, 1 do
        EffectContext.sdk:RemoveObject(stickers["sticker"..tostring(stickerID)].id)
    end
    stickers = {}
    firstFrame = 1
    maxSupportSticker =-1
    maxSupportInstanceCount = 5
    currentUnactivatedCount = 5
    previousTime = 0.0
    currentTime = 0.0
    loadMaterial = 0
    startTime = 0
    timeCounter = 0
    handVelocity = {}
    preHandPos = {} 
    handCounter = 0
    maskWidth = 0
    maskHeight = 0
    inited = 0
    resetParams = 1
    currentFloatCounter = 0.0
    EffectContext.logi("[X3DEffects][lua] effect reset all params ")
    EffectContext.sdk:SendMessage({ id = SELF_ID, message = { id = SELF_ID, animEnd = 2 } })
end

function setpauseSelfUpdate(part, pause) part:setpauseSelfUpdate(pause) end

Funcs = {
    onInit = function()
    end,

    onMessage = function(mes)
        EffectContext.logi("[X3DEffects][lua] onMessage: get wv_render_params ---")
        if (mes["wv_render_params"]) then
            resetParams = 0
            for k, v in pairs(mes["wv_render_params"]) do
                EffectContext.logi(k, "->", v)
                if (k == "stickerNum") then
                    maxSupportSticker = 1
                    maxSupportInstanceCount = v
                    currentUnactivatedCount = v
                    EffectContext.logi("set maxSupportInstanceCount =", maxSupportSticker)
                end
                if(k=="materialPath") then
                    materialPath = v
                    EffectContext.logi("[X3DEffects][lua] effect material path:", materialPath)
                end
            end
        end
    end,

    onDraw = function()
        if maxSupportSticker > 0 then
            if inited == 0 then
                EffectContext.logi("[X3DEffects][lua]: onInit")
                for stickerID=0, maxSupportSticker-1, 1 do
                    updateAbsolutePath(defaultPath, materialPath)
                    stickers["sticker"..tostring(stickerID)] = StickerCreator:new(defaultPath)
                    stickers["sticker"..tostring(stickerID)]:updateInstanceCount()
                    stickers["sticker"..tostring(stickerID)]:UpdateSwitch(0)
                end
                inited = 1
            end
            -- randomActiveSticker()
            activeSticker()
            currentFloatCounter = currentFloatCounter + floatCounterStep
            killSticker()
            maskWidth = EffectContext.Tracker:GetPortraitMaskInfo().width
            maskHeight = EffectContext.Tracker:GetPortraitMaskInfo().height
            if firstFrame == 1 then
                for i=1, maxSupportHandCount do
                    table.insert(handVelocity, {0.0, 0.0})
                    table.insert(preHandPos, {0, 0})
                end
                startTime = EffectContext.sdk:GetCurrentTime()
                currentTime = (EffectContext.sdk:GetCurrentTime() - startTime)*0.001
                previousTime = currentTime
                firstFrame = 0
                updateHandVelocity(1, 100000)
                handCounter = EffectContext.Tracker:GetHandCount()
            end
            currentHandCount = EffectContext.Tracker:GetHandCount()
            if currentHandCount ~= handCount then
                updateHandVelocity(1, 100000)
                handCount = currentHandCount
            end
            currentTime = (EffectContext.sdk:GetCurrentTime() - startTime)*0.001
            stickerFixedAlpha = math.min(math.max(1.0 - (currentTime - startAlpha)/(timeOut - startAlpha), 0.0), 1.0)
            deltaTime = (currentTime - previousTime)/physicalSimulateRate
            for i=0, physicalSimulateRate, 1 do
                for stickerID=0, maxSupportSticker-1, 1 do
                    for stickerInstanceID=1, stickers["sticker"..tostring(stickerID)].instanceCount, 1 do
                        if(stickers["sticker"..tostring(stickerID)].enableSticker[stickerInstanceID] == 1 and stickers["sticker"..tostring(stickerID)].dead[stickerInstanceID] == 0) then
                            local bodyCollision = bodyCollisionDetection("sticker"..tostring(stickerID), stickerInstanceID)
                            -- bodyCollision = 0
                            local handCollision = handCollisionDetection("sticker"..tostring(stickerID), stickerInstanceID)
                            -- handCollision = -1
                            if handCollision ~= -1 and stickers["sticker"..tostring(stickerID)].collisionNum[stickerInstanceID] > 0 then
                                local reflectVec, angleVec = handComputeReflection("sticker"..tostring(stickerID), stickerInstanceID, handCollision)
                                if length(reflectVec) > 0.0 then
                                    stickers["sticker"..tostring(stickerID)].velocity[stickerInstanceID] = {reflectVec.x, reflectVec.y}
                                    stickers["sticker"..tostring(stickerID)].angleVelocity[stickerInstanceID] = angleVec * angleCoefficient
                                    stickers["sticker"..tostring(stickerID)].collisionNum[stickerInstanceID] = stickers["sticker"..tostring(stickerID)].collisionNum[stickerInstanceID] - 1
                                end
                            elseif bodyCollision == 1 and handCollision == -1 and stickers["sticker"..tostring(stickerID)].collisionNum[stickerInstanceID] > 0 then
                                local reflectVec, angleVec = bodyComputeReflection("sticker"..tostring(stickerID), stickerInstanceID)
                                if length(reflectVec) > 0.0 then
                                    local absVelocity = length({x=stickers["sticker"..tostring(stickerID)].velocity[stickerInstanceID][1], y=stickers["sticker"..tostring(stickerID)].velocity[stickerInstanceID][2]})
                                    stickers["sticker"..tostring(stickerID)].velocity[stickerInstanceID] = {reflectVec.x * absVelocity*elasticCoefficient, reflectVec.y*absVelocity*elasticCoefficient}
                                    stickers["sticker"..tostring(stickerID)].angleVelocity[stickerInstanceID] = angleVec * angleCoefficient
                                    stickers["sticker"..tostring(stickerID)].collisionNum[stickerInstanceID] = stickers["sticker"..tostring(stickerID)].collisionNum[stickerInstanceID] - 1
                                end
                            end
                            calculateStickerPosition("sticker"..tostring(stickerID), deltaTime, stickerInstanceID)
                        end   
                    end            
                end            
            end    
            for stickerID=0, maxSupportSticker-1, 1 do
                for stickerInstanceID=1, stickers["sticker"..tostring(stickerID)].instanceCount, 1 do
                    stickers["sticker"..tostring(stickerID)]:UpdateRST(stickerInstanceID)
                end
                stickers["sticker"..tostring(stickerID)]:SetTransparency(stickerFixedAlpha)
            end            
            updateHandVelocity(0, currentTime - previousTime)
            previousTime = currentTime
            -- callback
            checkEffectFinished()
            -- EffectContext.logi("[X3DEffects][lua]: end of draw")
        end
    end

}

return Funcs;
