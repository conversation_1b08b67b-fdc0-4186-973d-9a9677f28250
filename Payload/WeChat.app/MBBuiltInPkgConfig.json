[{"bizName": "MagicFinderCard", "url": "https://devops.woa.com/console/pipeline/mb/p-71d012e1876c49bc81a38bc6ca936962/detail/b-78418c42d86148b583ab579881ef1273/executeDetail", "commitMsg": "更新成app-config.j<PERSON>,生成文件包括appConfig", "extInfo": "allowNullBootsVersion:true"}, {"bizName": "MagicAdBrandService", "url": "https://devops.woa.com/console/pipeline/wxad-component/p-533d707292424917ac3d536d96471c99/detail/b-e82f14274abe48a2a248d9883b45a802/outputs", "commitMsg": "支持封面图forT3", "extInfo": "allowNullBootsVersion:false"}, {"bizName": "MagicBrandService", "url": "https://devops.woa.com/console/pipeline/wechatfe/p-dbe8a170366140d6b28be4f5e5d2d3b6/detail/b-b847a5023fa94f168f0b878c90c7f4d4/executeDetail", "commitMsg": "支持封面图forT3", "extInfo": "allowNullBootsVersion:false"}, {"bizName": "wxe208ce76dfa39515", "url": "https://devops.woa.com/console/pipeline/wegame-js/p-2dfa217f285343b1973aa330cc1411c4/detail/b-dc6f67f02f17426791a27cc655207028/executeDetail", "commitMsg": "更新成app-config.j<PERSON>,生成文件包括appConfig", "extInfo": "allowNullBootsVersion:false"}, {"bizName": "MagicAdPublicService", "url": "https://devops.woa.com/console/pipeline/wxad-component/p-533d707292424917ac3d536d96471c99/detail/b-b3296e8015494d49931b6149b317f4b9/executeDetail", "commitMsg": "更新成app-config.j<PERSON>,生成文件包括appConfig", "extInfo": "allowNullBootsVersion:false"}, {"bizName": "wx2f3fb5db9f226462", "url": "https://devops.woa.com/console/pipeline/wechatfe/p-ff01a11e9cc741bebb0622c869d4e6f5/detail/b-7d1f01b1d0234b7db4a010d53fd8fde6/executeDetail", "commitMsg": "新字体接口；bugfix 修复视频播放卡顿/花屏问题;前端配置退后台的情况下不创建 canvas，T31patch合入", "extInfo": "allowNullBootsVersion:false"}, {"bizName": "MagicAdPlayableBasic", "url": "https://devops.woa.com/console/pipeline/miniprogramdocument/p-233fad8e72f44eeb91610ba90d5fbf9c/detail/b-55d1d41148f844f48a5f264171e57012/executeDetail", "commitMsg": "解决音频无法播放问题；更新成app-config.json；生成文件包括appConfig；落地版本号1.0.4", "extInfo": "allowNullBootsVersion:false"}, {"bizName": "MagicNewAdPlayableBasic", "url": "https://devops.woa.com/console/pipeline/miniprogramdocument/p-a9cc0601841f422bb08bf594eb36791e/detail/b-34aa591b89154e5496251ea2e36344f3/executeDetail", "commitMsg": "试玩规模化新基础库:引擎适配; fix systeminfo失效;fix notify接口问题; 支持wx.createVideoDecoder;unity 支持;fix：iOS 渲染 image 问题修复", "extInfo": "allowNullBootsVersion:true"}, {"bizName": "MagicNativeFinderCard", "url": "https://devops.woa.com/console/pipeline/mb/p-4a9a2d2ff18d4aeab02df48f6dc75a3e/detail/b-d26b0f696fda4ec3aa5bd9cb3349f960/executeDetail", "commitMsg": "更新成app-config.j<PERSON>,生成文件包括appConfig", "extInfo": "allowNullBootsVersion:true,useStarter:true"}, {"bizName": "MagicWxGameDynamicCard", "url": "https://devops.woa.com/console/pipeline/wegame-js/p-8d9640fad5a14398af4acd2aee13e876/detail/b-be0a1f555527400a93ab24796e8beb56/executeDetail", "commitMsg": "1、支持消息卡（预约&游戏内订阅）2、支持游戏营销卡3、增加周报卡4、增加排行卡(含游戏同局时刻信息);更新成app-config.j<PERSON>,生成文件包括appConfig；单图文、多图文，底层以「动态卡片」能力实现", "extInfo": "allowNullBootsVersion:true,useStarter:true"}, {"bizName": "MagicEcsKFDynamicCard", "url": "https://devops.woa.com/console/pipeline/brand-ecs/p-af8fef22feb443458377db87b0879713/detail/b-19c7755b540e47a2acc9173734cb4595/executeDetail", "commitMsg": "T2首个版本", "extInfo": "allowNullBootsVersion:true,useStarter:true"}, {"bizName": "MagicPermissionConfig", "url": "https://devops.woa.com/console/pipeline/mb/p-7106b5ff86c14b5a9ca90b079f0d4ecc/detail/b-748e17f6bafb4edc980170c039b9e1e2/executeDetail", "commitMsg": "UDR首个内置包", "extInfo": "allowNullBootsVersion:true"}, {"bizName": "MagicAdMiniProgram", "url": "https://devops.woa.com/console/pipeline/wxad-component/p-533d707292424917ac3d536d96471c99/detail/b-53e2c4ed6bf043b49ee3127b4470e317/executeDetail", "commitMsg": "bugfix「0.0.8」1. 新增心跳上报  2. 修复边界半px空白  3. 新增迁移游戏结束页、彩蛋挂件、视频卡顿提示,0.0.7,fix report", "extInfo": "allowNullBootsVersion:true,useStarter:true"}, {"bizName": "MagicEcsTimeline", "url": "https://devops.woa.com/console/pipeline/brand-ecs/p-de102d9f45764261832e78e674334fcf/detail/b-5bb4be8833af4d18bc99b9d6ff8f13c8/executeDetail", "commitMsg": "联调中", "extInfo": "allowNullBootsVersion:true"}, {"bizName": "MagicEcsTimelineSubscribe", "url": "https://devops.woa.com/console/pipeline/brand-ecs/p-bf083617f8184022b1bbe1503be8c030/detail/b-0c4d1316c97e440ab4e44a2a8464d110/executeDetail", "commitMsg": "联调中", "extInfo": "allowNullBootsVersion:true"}, {"bizName": "MagicEcsPublicService", "url": "https://devops.woa.com/console/pipeline/brand-ecs/p-d054d3cf94dc45e7a3af9992557732f6/detail/b-6feaf760da93425fa53f2664d81d2e61/executeDetail", "commitMsg": "联调中", "extInfo": "allowNullBootsVersion:true"}]