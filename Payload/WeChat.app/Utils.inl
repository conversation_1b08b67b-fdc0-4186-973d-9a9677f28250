//========================================================================
//
//  Utils.h: Utilities for XSummary
//
//  Copyright (c) 2024-present, All Rights Reserved
//
//  Author: Reginald Lu
//
//  NOTE: DO NOT include this file unless you know what you are doing
//
//========================================================================

#include <zlib.h>
#include <string>
#include <vector>

#if XSUMMARY_VERBOSE
#include "XNet/XLogging.h"
#endif
#include "XNet/XProperty.h"
#include "XNet/XSmartPointer.h"
#include "XNet/XUnicode.h"

#include "XNet/DNN/XDnnInferenceC.h"

#include "XNet/Utils/PCRE2/UPcre2Match.h"

namespace xsdk {

// -----------------------------------String Operations------------------------------------

inline bool IsZeroWidth(char32_t value) {
    return (value >= 0x200Bu && value <= 0x200Du) || value == 0xFEFFu;
}

inline void TrimLeft(std::u32string &s) {
    s.erase(s.begin(), std::find_if(s.begin(), s.end(), [](int c) { return !std::isspace(c); }));
}

inline void TrimRight(std::u32string &s) {
    s.erase(std::find_if(s.rbegin(), s.rend(), [](int c) { return !std::isspace(c); }).base(), s.end());
}

void Trim(std::u32string &s) {
    TrimLeft(s);
    TrimRight(s);
}

inline bool StartsWith(const std::u32string &s, const std::u32string &prefix) {
    return s.size() >= prefix.size() && std::equal(prefix.begin(), prefix.end(), s.begin());
}

inline bool EndsWith(const std::u32string &s, const std::u32string &suffix) {
    return s.size() >= suffix.size() && std::equal(suffix.rbegin(), suffix.rend(), s.rbegin());
}

template<typename StringType> StringType Replace(const StringType &str, const StringType &from, const StringType &to) {
    StringType s = str;

    if (from.empty())
        return s;

    size_t start_pos = s.find(from);

    for (; start_pos != StringType::npos; start_pos = s.find(from, start_pos)) {
        s.replace(start_pos, from.length(), to);
        start_pos += to.length();
    }

    return s;
}

template<typename StringType> StringType Join(const std::vector<StringType> &v, const StringType &delim) {
    StringType s;

    for (size_t i = 0; i < v.size(); ++i) {
        if (i > 0)
            s += delim;

        s += v[i];
    }

    return s;
}

// Copy from XNet/Include/XNet/XUnicode.h to avoid inline
// UTF-8 -> UTF-32
bool Utf8ToUtf32(std::u32string &dst32, xnet::StringView src8) {
    xnet::Clear(dst32);

    const char *pSrc = src8.Data();
    xMInt srcLength = src8.Length();

    if (pSrc != nullptr) {
        xnet::CodePoint codePoint;

        while (srcLength > 0) {
            const xMInt numConsume = codePoint.FromUtf8(pSrc, srcLength);
            if (XNET_UNLIKELY(numConsume <= 0))
                return false;

            if (XNET_UNLIKELY(!xnet::Append(dst32, codePoint.value)))
                return false;

            pSrc += numConsume;
            srcLength -= numConsume;
        }

        if (XNET_UNLIKELY(srcLength != 0))
            return false;
    }

    // NOTE: We ALWAYS return true if src8 is empty
    return true;
}

// UTF-32 -> UTF-8
bool Utf32ToUtf8(std::string &dst8, xnet::U32StringView src32) {
    xnet::Clear(dst8);

    const char32_t *pSrc = src32.Data();
    const xMInt srcLength = src32.Length();

    xMInt dstLength = 0;

    if (pSrc != nullptr) {
        char workspace[4] = { '\0', '\0', '\0', '\0' };

        for (xMInt i = 0; i < srcLength; ++i) {
            const xMInt numProduce = xnet::CodePoint(pSrc[i]).ToUtf8(workspace, 4);
            if (XNET_UNLIKELY(numProduce <= 0 || numProduce > 4))
                return false;

            if (XNET_UNLIKELY(!xnet::Append(dst8, workspace, numProduce)))
                return false;

            dstLength += numProduce;
        } // i

        if (XNET_UNLIKELY(xnet::LengthOf(dst8) != dstLength))
            return false;
    }

    // NOTE: We ALWAYS return true if src32 is empty
    return true;
}

#if XSUMMARY_VERBOSE
// NOTE: DO NOT use this function as it does not handle the errors
inline std::string Utf32ToUtf8(const std::u32string &src32) {
    std::string dst8;

    Utf32ToUtf8(dst8, xnet::U32StringView(src32));

    return dst8;
}
#endif

// --------------------------------------------------------------------------------------

// ----------------------------------JSON Operations--------------------------------------

std::string GetU8String(const xnet::PropertySheet *pPropertySheet, const std::string &key) {
    const std::string *pStr = pPropertySheet->GetStringPtr(key);
    if (!pStr) {
#if XSUMMARY_VERBOSE
        XLogErrorString("Failed to get string value");
#endif
        return "";
    }

    return *pStr;
}

std::u32string GetU32String(const xnet::PropertySheet *pPropertySheet, const std::string &key) {
    std::string strU8 = GetU8String(pPropertySheet, key);

    std::u32string str;

    if (!xnet::Utf8ToUtf32(str, xnet::StringView(strU8))) {
        return U"";
    }

    return str;
}

xUInt64 GetUInt64(const xnet::PropertySheet *pPropertySheet, const std::string &key) {
    xUInt64 number = 0;

    while (true) {
        const xInt64 *pInt64 = pPropertySheet->GetInt64Ptr(key);
        if (pInt64) {
            number = static_cast<xUInt64>(*pInt64);
            break;
        }

        const xInt32 *pInt32 = pPropertySheet->GetInt32Ptr(key);
        if (pInt32) {
            number = static_cast<xUInt64>(*pInt32);
            break;
        }

        const xUInt64 *pUInt64 = pPropertySheet->GetUInt64Ptr(key);
        if (pUInt64) {
            number = *pUInt64;
            break;
        }

        const xUInt32 *pUInt32 = pPropertySheet->GetUInt32Ptr(key);
        if (pUInt32) {
            number = static_cast<xUInt64>(*pUInt32);
            break;
        }

#if XSUMMARY_VERBOSE
        XLogErrorString("Failed to get integer value");
#endif

        return 0;
    }

    return number;
}

xFloat32 GetFloat32(const xnet::PropertySheet *pPropertySheet, const std::string &key) {
    xFloat32 number = 0.0f;

    while (true) {
        const xFloat32 *pFloat32 = pPropertySheet->GetFloatPtr(key);
        if (pFloat32) {
            number = *pFloat32;
            break;
        }

        const xFloat64 *pFloat64 = pPropertySheet->GetDoublePtr(key);
        if (pFloat64) {
            number = static_cast<xFloat32>(*pFloat64);
            break;
        }

#if XSUMMARY_VERBOSE
        XLogErrorString("Failed to get float value");
#endif

        return 0.0f;
    }

    return number;
}

// --------------------------------------------------------------------------------------

// ----------------------------------Pcre2 Wrapper---------------------------------------

class XPcre2 {
public:
    XPcre2() : mRegex() {}

    bool Compile(const char *pattern) noexcept {
        std::u32string pattern32;
        if (!Utf8ToUtf32(pattern32, xnet::StringView(pattern))) {
#if XSUMMARY_VERBOSE
            XLogErrorString("Failed to convert pattern to UTF-32");
#endif
            return false;
        }

        if (!mRegex.CompileFrom(pattern32)) {
#if XSUMMARY_VERBOSE
            XLogErrorString("Failed to compile pattern");
#endif
            return false;
        }

        return true;
    }

    bool Match(const std::u32string &text) const noexcept {
        xnet::upcre::Pcre2MatchData<char32_t> matchData;
        if (!matchData.CreateFrom(mRegex)) {
            return false;
        }

        return xnet::upcre::Pcre2NextMatch(matchData, xnet::BasicStringView<char32_t>(text), mRegex, 0);
    }

    bool FindAll(const std::u32string &text, std::vector<std::u32string> &matches) const noexcept {
        matches.clear();

        xnet::upcre::Pcre2MatchData<char32_t> matchData;
        if (!matchData.CreateFrom(mRegex)) {
            return false;
        }

        size_t lastOffset = 0;

        while (lastOffset < text.size()) {
            if (xnet::upcre::Pcre2NextMatch(matchData, xnet::BasicStringView<char32_t>(text), mRegex, lastOffset)) {
                size_t matchedSize = matchData.GetOvectorSize();

                if (matchedSize == 1) {
                    xnet::Pair<size_t, size_t> offsets;

                    if (!matchData.GetOvector(0, offsets)) {
                        return false;
                    }

                    matches.push_back(text.substr(offsets.first, offsets.second - offsets.first));

                    lastOffset = offsets.second;
                }

                for (size_t i = 1; i < matchedSize; ++i) {
                    xnet::Pair<size_t, size_t> offsets;

                    if (!matchData.GetOvector(i, offsets)) {
                        return false;
                    }

                    matches.push_back(text.substr(offsets.first, offsets.second - offsets.first));

                    lastOffset = offsets.second;
                }
            } else {
                break;
            }
        }
        return true;
    }

    bool ReplaceAll(std::u32string &text, const std::u32string &replacement) const noexcept {
        std::u32string result;

        xnet::upcre::Pcre2MatchData<char32_t> matchData;
        if (!matchData.CreateFrom(mRegex)) {
            return false;
        }

        size_t lastOffset = 0;

        while (lastOffset < text.size()) {
            if (xnet::upcre::Pcre2NextMatch(matchData, xnet::BasicStringView<char32_t>(text), mRegex, lastOffset)) {
                size_t matchedSize = matchData.GetOvectorSize();

                if (matchedSize > 0) {
                    xnet::Pair<size_t, size_t> offsets;

                    if (!matchData.GetOvector(0, offsets)) {
                        return false;
                    }

                    result += text.substr(lastOffset, offsets.first - lastOffset);
                    result += replacement;

                    lastOffset = offsets.second;
                }
            } else {
                result += text.substr(lastOffset);
                break;
            }
        }

        text = result;

        return true;
    }

    bool Split(const std::u32string &text, std::vector<std::u32string> &parts) const noexcept {
        parts.clear();

        xnet::upcre::Pcre2MatchData<char32_t> matchData;
        if (!matchData.CreateFrom(mRegex)) {
            return false;
        }

        size_t lastOffset = 0;

        while (lastOffset < text.size()) {
            if (xnet::upcre::Pcre2NextMatch(matchData, xnet::BasicStringView<char32_t>(text), mRegex, lastOffset)) {
                size_t matchedSize = matchData.GetOvectorSize();

                for (size_t i = 0; i < matchedSize; ++i) {
                    xnet::Pair<size_t, size_t> offsets;

                    if (!matchData.GetOvector(i, offsets)) {
                        return false;
                    }

                    parts.push_back(text.substr(lastOffset, offsets.first - lastOffset));

                    lastOffset = offsets.second;
                }
            } else {
                parts.push_back(text.substr(lastOffset));
                break;
            }
        }

        return true;
    }

private:
    xnet::upcre::Pcre2Regex<char32_t> mRegex;
};

// --------------------------------------------------------------------------------------

// ----------------------------------XNet Wrapper----------------------------------------

class XDnnModel {
public:
    XDnnModel() : mpImpl(nullptr) {}

    ~XDnnModel() {
        if (mpImpl)
            XNetDnnModelCpuDestroy(mpImpl);
    }

public:
    XNetDnnModelCpu *mpImpl;
};

typedef xnet::SharedPtr<XDnnModel> XDnnModelRef;

class XDnnTensor {
public:
    XDnnTensor() : mpImpl(nullptr) {}

    ~XDnnTensor() {
        if (mpImpl)
            XNetDnnTensorCpuDestroy(mpImpl);
    }

    XNET_ALWAYS_INLINE bool Create(size_t dim, size_t *shape, XNetDnnDataKind dataKind) noexcept {
        mpImpl = XNetDnnTensorCpuCreate(dim, shape, dataKind);

        return (mpImpl != nullptr);
    }

    XNET_ALWAYS_INLINE size_t Dim() const noexcept { return XNetDnnTensorCpuGetDim(mpImpl); }

    XNET_ALWAYS_INLINE size_t Shape(size_t dimIdx) const noexcept { return XNetDnnTensorCpuGetShape(mpImpl, dimIdx); }

    size_t ProdShape() const noexcept {
        if (Dim() == 0)
            return 0;

        size_t prod = 1;

        for (size_t i = 0; i < Dim(); ++i)
            prod *= Shape(i);

        return prod;
    }

    template<typename T> XNET_ALWAYS_INLINE T *Lock() noexcept { return static_cast<T *>(XNetDnnTensorCpuLock(mpImpl)); }

    XNET_ALWAYS_INLINE void Unlock() noexcept { XNetDnnTensorCpuUnLock(mpImpl); }

public:
    XNetDnnTensorCpu *mpImpl;
};

typedef xnet::SharedPtr<XDnnTensor> XDnnTensorRef;

class XDnnSession {
public:
    XDnnSession() : mpImpl(nullptr) {}

    ~XDnnSession() {
        if (mpImpl)
            XNetDnnSessionCpuDestroy(mpImpl);
    }

    XNET_ALWAYS_INLINE bool SetInput(const char *name, const XDnnTensorRef &tensor) noexcept {
        return (XNetDnnSessionCpuSetInput(mpImpl, name, tensor->mpImpl) == 0);
    }

    XNET_ALWAYS_INLINE XDnnTensorRef GetOutput(const char *name) noexcept {
        XDnnTensorRef tensor(new XDnnTensor());

        tensor->mpImpl = XNetDnnSessionCpuGetOutput(mpImpl, name);

        return tensor;
    }

    XNET_ALWAYS_INLINE bool Run() noexcept { return (XNetDnnSessionCpuRun(mpImpl, nullptr) == 0); }

public:
    XNetDnnSessionCpu *mpImpl;
};

typedef xnet::SharedPtr<XDnnSession> XDnnSessionRef;

// --------------------------------------------------------------------------------------

// ----------------------------------Zip Wrapper-----------------------------------------

bool Unzip(void *pZipData, size_t zipSizeInBytes, void *pUnzipData, size_t unzipSizeInBytes) {
    z_stream stream;

    /* NOTE: According to the zlib manual, the following fields must be initialized
             before calling inflateInit:

             avail_in : The number of bytes available at next_in
             next_in  : A pointer to the input data
             zalloc   : A pointer to the allocation function
             zfree    : A pointer to the free function
             opaque   : A pointer to the memory allocation structure

             Reference: https://zlib.net/manual.html (inflateInit)
    */
    stream.avail_in = static_cast<uInt>(zipSizeInBytes);
    stream.next_in = reinterpret_cast<Bytef *>(pZipData);
    stream.zalloc = Z_NULL;
    stream.zfree = Z_NULL;
    stream.opaque = Z_NULL;

    if (inflateInit(&stream) != Z_OK) {
#if XSUMMARY_VERBOSE
        XLogErrorString("Failed to initialize zlib");
#endif
        return false;
    }

#if XSUMMARY_VERBOSE
    XLogInfo() << "Unzip: " << zipSizeInBytes << " -> " << unzipSizeInBytes;
#endif

    stream.avail_out = static_cast<uInt>(unzipSizeInBytes);
    stream.next_out = reinterpret_cast<Bytef *>(pUnzipData);

    if (inflate(&stream, Z_FINISH) != Z_STREAM_END) {
#if XSUMMARY_VERBOSE
        XLogErrorString("Failed to inflate zlib");
#endif
        return false;
    }

    if (inflateEnd(&stream) != Z_OK) {
#if XSUMMARY_VERBOSE
        XLogErrorString("Failed to end zlib");
#endif
        return false;
    }

    return true;
}

// --------------------------------------------------------------------------------------

} // namespace xsdk
