stickers = {}
SELF_ID = 0

local StickerCreator = {id = 0}
StickerCreator.__index = StickerCreator

function StickerCreator:new(path, type)
    local self = {}
    setmetatable(self, StickerCreator)
    local ret = EffectContext.sdk:AddEffectMaterial({path = path, type = type})
    self.id = ret.id
    self.config = ret.config

    SELF_ID = EffectContext.getEnvVar("SELF_ID")

    return self
end

Funcs = {
    onInit = function()
        stickers["bgColor"] = StickerCreator:new("./bgColor")
        EffectContext.sdk:SetParam({ id = stickers["bgColor"].id,
            param = { enable = 0 }
        })
    end,
}

return Funcs;
