<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="Content-Language" content="en" charset="utf-8">
	<meta charset="UTF-8">
	<meta id="viewport" name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1; user-scalable=no;" />
	<style> html,p,h1,ul,li{margin:0px;padding:0px;} ul{list-style-type:none;} body{color:#000;font:14px/1.5 微软雅黑,Helvetica,&quot;Helvetica Neue&quot;,&quot;segoe UI Light&quot;,&quot;Kozuka Gothic Pro&quot;;} h1{font-size:16px; padding-bottom:12px;margin-bottom:5px;} .articleTitle{margin-bottom:6px;} .sec_body { margin:25px 15px;} p{margin-bottom:6px;} .bold{font-weight:bold;} .article{margin-bottom:32px;} .sec_body .content { padding:10px;border-width:0px; } @media (prefers-color-scheme: dark) { body { background-color: #232323; color: rgba(255, 255, 255, .8); } } </style>
	<title>インターネットに接続されていません</title>
</head>
<body class="sec_body">
	<div class="container">
		<h1>デバイスがインターネットに接続されていません</h1>
		<div class="article">
			<p class="articleTitle">インターネットに接続するには、次の方法をお試しください。</p>
			<ul>
				<li>デバイスで、「<strong>設定</strong>」-「<strong>Wi-Fi</strong>」に移動し、利用可能なWi-Fiネットワークに参加します。</li>
				<li>デバイスで、「<strong>設定</strong>」-「<strong>セルラーモバイル通信</strong>」に移動し、「<strong>モバイルデータ通信</strong>」を有効にします。（データ使用量については、サービスプロバイダーから請求される場合があります。）</li>
			</ul>
		</div>
		<div class="article">
			<p class="articleTitle">デバイスをApple Watchとペアリングした場合：</p>
			<ul>
				<li>「<strong>Watch</strong>」アプリ-「<strong>モバイルデータ通信</strong>」-「<strong>WeChat</strong>」を開き、WeChatがデータにアクセスすることを許可します。</li>
			</ul>
		</div>
		<div class="article">
			<p class="articleTitle">Wi-Fiネットワークに接続している場合：</p>
			<ul>
				<li>Wi-Fiホットスポットがインターネットに接続されているかどうか、またはデバイスのホットスポットへのアクセスが許可されているかどうかを確認します。</li>
			</ul>
		</div>
	</div>
</body>
</html>

