const { addEventListener, removeEventListener, postMessage, dispatchEvent, callbackWrapper } = (function() {
  var eventMap = {};
  var weixinContext = __weixincontext;
  var uniqueId = 1;
    
  delete __weixincontext;
  return {
    addEventListener : function (eventType, listener) {
      if (eventMap.hasOwnProperty(eventType)) {
        var eventDispatcher = eventMap[eventType];
        var index = eventDispatcher.indexOf(listener);
        if (index == -1) {
          eventDispatcher.push(listener);
        }
      } else {
        eventMap[eventType] = [listener];
      }
    },
    removeEventListener : function (eventType, listener) {
      if (!eventMap.hasOwnProperty(eventType)) { return; }
      var eventDispatcher = eventMap[eventType];
      var index = eventDispatcher.indexOf(listener);
      if (index != -1) {
        eventDispatcher.splice(index, 1);
      }
    },
    postMessage : function(message, targetOrigin) {
      weixinContext.postMessage(message, targetOrigin);
    },
    dispatchEvent : function(eventType, event) {
      if (!eventMap.hasOwnProperty(eventType)) { return; }
      var eventDispatcher = eventMap[eventType];
      for (var i = 0; i < eventDispatcher.length; i++) {
        eventDispatcher[i](event);
      }
    },
    callbackWrapper : function(callback) {
      if (typeof callback !== 'function') {
        return ""
      }
      var callbackId = 'cb_'+(uniqueId++)+'_'+new Date().getTime();
      var callbackWrapper = function() {
        removeEventListener(callbackId, callbackWrapper);
        callback.apply(null, Array.from(arguments));
      };
      addEventListener(callbackId, callbackWrapper);
      return callbackId;
    }
  }
})();
