﻿<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="content-type" content="text/html;charset=utf-8"/>
        <meta id="viewport" name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1; user-scalable=no;" />
        <style>
            html,p,h1,ul,li{margin:0px;padding:0px;}
			ul{list-style-type:none;}
			body{color:#000;font:14px/1.5 微软雅黑,Helvetica,"Helvetica Neue","segoe UI Light","Kozuka Gothic Pro";}
			h1{font-size:16px; padding-bottom:12px;margin-bottom:5px;}
			.articleTitle{margin-bottom:6px;}
			.sec_body { margin:25px 15px;}
			p{margin-bottom:6px;}
			.bold{font-weight:bold;}
			.article{margin-bottom:32px;}
			.sec_body .content { padding:10px;border-width:0px; }
            @media (prefers-color-scheme: dark) {
              body {
                  background-color: #232323;
                  color: rgba(255, 255, 255, .8);
              }
            }
        </style>
        <title>未能連接網路</title>
    </head>
    <body class="sec_body">
        <div class="container">
			<h1>未能連接網路</h1>
			<p>你的裝置未開啟行動網路或 Wi-Fi</p>
			<div class="article">
				<p class="articleTitle">如需要連接到網路，可以參照以下指引：</p>
				<ul>
					<li>在裝置的 「<strong>設定</strong>」 - 「<strong>Wi-Fi</strong>」 頁面中加入一個可用的 Wi-Fi。</li>
					<li>在裝置的 「<strong>設定</strong>」- 「<strong>行動服務</strong>」中開啟<strong>行動數據</strong> (啟用後電信業者可能會收取數據使用費用)。</li>
				</ul>
			</div>
          <div class="article">
				<p class="articleTitle">如果你有配對支持行動網路的 Apple Watch：</p>
				<ul>
					<li>在裝置的「<strong>Watch</strong>」應用程式- 「<strong>行動數據</strong>」- 「<strong>WeChat</strong>」 設定頁中允許 WeChat 使用資料。</li>
				</ul>
			</div>
			<div class="article">
				<p class="articleTitle">如果你已連上 Wi-Fi：</p>
				<ul>
					<li>請檢查你所連接的熱點是否已連上網路，或該熱點是否已允許你的裝置使用網路。</li>
				</ul>
            </div>
        </div>
    </body>
</html>

