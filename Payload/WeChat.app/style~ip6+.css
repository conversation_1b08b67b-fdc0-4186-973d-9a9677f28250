/*公众号会话单文章视图*/
#reader_node_view {
    
    reader_view_width: 386;
    /*  原来150  */
    cover_image_height: 216;
    reader_view_max_height: 580;
    
}

/* 支付和安全相关 */
#attributed_reader_message_node_view  {
    reader_view_width: 384;
}


/*公众号多文章视图子按钮*/
#read_item_view {
    
    reader_top_new_height: 216;
    reader_view_width: 384;
    reader_cover_height: 55 "dynamic";
    
}

/*公众号会话文本消息卡片视图*/
#reader_text_node_view {
    
    text_content_max_height: 216 "dynamic";  /* 单文本消息内容最大高度(除详情cell高度)*/
}

/*公众号会话语音消息卡片视图*/
#reader_voice_node_view {
    
    voice_top_item_height: 160;
    title_left_padding: 20;
    
    play_btn_width: 45;
    play_normal_btn_width: 30;
    
    play_btn_right_padding: 20;
    title_btn_spacing: 20;
}

/*公众号会话视频消息卡片视图*/
#reader_image_node_view {
    image_top_item_height: 216;
}

/*公众号会话图片消息卡片视图*/
#reader_video_node_view {
    video_top_item_height: 216;
    
    title_left_padding: 20;
    
    play_btn_width: 40;
    play_normal_btn_width: 30;
    
    play_btn_right_padding: 20;
    title_btn_spacing: 20;
    
}

#hardward_message_node_view {
    node_view_width: 380;
    node_view_height: 171;
    
    notify_node_width: 380;
    notify_node_height: 73;
}

#WCListHeaderView {
    /*朋友圈顶部nickname*/
    nick_label_font_size: 21;
}
#WCTimeLineViewController{
    timeline_cell_bottom_margin : 11;
    timeline_abtest_cell_bottom_margin : 5;
    input_view_textview_height : 40;
}
/* 朋友圈 边距由原来的9变成20 +94-22=+72 */
#WCTimelineCellView {
    /*正文长度*/
    text_line_width : 322;
    /*正文文本高度*/
    default_lable_height : 15.5;
    /*头像大小*/
    headimage_view_len: 43;
    
    more_action_floatView_width : 370;
    
    ad_card_title_font_size : 18;
}
/* 个人相册 */
#WCListViewRowContentView {
    content_view_width : 303;
    default_text_font_size: 16.5;
    default_text_line_height : 20;
}

/*朋友圈详情*/
#WCComentDetailViewControllerFB {
    comment_view_width : 390;
    /*  详情页每行点赞头像个数  */
    comment_head_count_perline : 8;
}

/* 朋友圈的图片分享相关 */
#WCTimeLineCellMediaContentView {
    multi_image_len : 86;
    image_gird_step_len : 5;
    image_size_max_len : 180;
    image_size_superwide_max_len : 268;
    image_size_superheight_max_len : 89;
    image_size_superheight_min_len : 32;
    sight_size_max_len : 225;
    /*template news*/
    link_title_font_size: 14.5;
    
    hotvideo_title_font_size : 15.5;
    
    link_area_width : 322;
    /* 朋友圈分享页面的media cell， 320下是290  */
    forward_link_area_width : 380;
}

/* 新晒一晒 */
#WCContentItemViewTemplateNewSight {
    /*  此处按比例缩放的 原来200  */
    cover_image_width: 246;
    /*  此处按比例缩放的 原来150  */
    cover_image_height: 184;
    moment_edit_player_width : 120;
}

/* 朋友圈评论 */
#WCTimeLineCommentCellView {
    /*评论宽度（包括了上面的评论左右内间距）*/
    comment_view_width: 322;
    /*赞块的上下内间距*/
    like_content_margin: 4.5;
    /*赞块的左右内间距*/
    like_content_left_margin: 9;
    comment_content_margin_left: 9;
}
/* 消息节点 */
#message_node_view
{
	/*message_node_bkg_width : 260;
    message_node_bkg_shorter_width: 240;*/
	message_node_bkg_width: 56% width;
	message_node_bkg_shorter_width: 56% width;

    /*  mail节点单独计算  */
    mail_message_node_width : 344;
    /*  原来是120 等比放大  */
    message_node_imageNode_maxSizeLimit : 155;
    
    title_label_font: 17 dynamic;
}

/* 聊天界面背景图选择界面 */
#ChatBackgroundView {
    cell_img_width : 129;
    cell_img_height : 129;
}

#message_load_count{
    first_page_msg_count : 15 ;
}

#AppNoteNodeView {
    image_max_count: 4;
}

#RecordDetailView {
    image_width : 316;
    subtitle_max_height : 370;
}

#WCCommitViewController {
    gridimg_stepWidth : 5;
    gridimg_size:110.6 110.6;
    gridimg_single_sizeWidth:300;
}

#message_node_view {
    text_message_node_width : 64% width;
    message_node_voiceNode_max_width : 200;
}

/* 输入框附件栏 */
#input_tool_view_attachment {
    attachment_view_margin : 3A;
    text_attachment_down_line_alpha: 0.12;

}

#MMTipsView {
    background_image_width : 320;
}

/*手势密码*/
#pattern_lock_view {
    circle_radius : 36;
    circle_gap : 40;
    
    tipsView_y : 50;
    tipsView_bottom_offset : 55;
}

#login_common
{
    textfield_leftview_width: 93;
    newHeaderViewTitle_topMargin: 73;
    agree_checkbox_topMargin: 48;
    nextbtn_margin_agree_checkbox: 16;
    nextbtn_topMargin: 30;
    title_bottomMargin: 40;

    users_title_logo_margin: 74;
    users_title_line_margin: 39;
}

#brand_contact_profile {
    intro_left_label_margin : 15;
}

#VoipView
{
    hangupButtonDesOffsetY : 8;
    voiceButtonDesOffsetY : 1;
    footerActionButtonWidth : 72;
    footerActionButtonHeight : 72;
    voiceActionButtonWidth : 35;
    voiceActionButtonHeight : 35;
    toastWindowMarginBottom : 250;
    toastWindowMarginBottomAdpatAudioMode : 240;
    toastWindowMarginBottomAdpatVideoReceiverWaitingMode : 253;
    leftButtonMarginLeft : 80;
    rightButtonMarginRight : 80;
    footerButtonMarginButtom : 32;
    secondFooterButtonMarginBottom : 155;
    timerLabelMarginBottom : 145 dynamic;
    hangup_button_margin_center : 139.5;
    warningTips_top_margin : 436;
    warningTips_left_margin : 38.5;
    warningTips_left_span : 19.5;
    warningTips_font_size : 16.5;
    warningTips_width : 337;
    warningTips_height : 38.5;
    warningTips_fill_color : "#E64340";
    warningTips_font_color : "#FFFFFF";
}


#device_rank_view {
    tableheader_height : 377;
    header_headImage_size : 48;
}

#device_profile_view {
    tableheader_height : 377;
    
    step_content_height : 285;
    step_values_height : 200;
    steps_time_font_size : 12;
}

/* 企业红包收取页面样式 */

#EnterpriseLuckyMoneyBaseView
{
    frame_width : 337;
    frame_height : 470;
}

#EnterpriseLuckyMoneyIconView
{
    view_width : 60;
    view_height : 60;
    top_margin : 47;
}

#EnterpriseLuckyMoneyNickNameView
{
    top_margin : 13;
    font_width : 20;
}

#EnterpriseLuckyMoneyStatusMessView
{
    top_margin : 14;
    font_width : 16;
}

#EnterpriseLuckyMoneyWishView
{
    top_margin : 30;
    left_margin : 46;
    font_width : 20;
}

#EnterpriseLuckyMoneyOpenView
{
    top_margin : 290;
    view_width : 110;
    view_height : 115;
    font_width : 20;
}

#EnterpriseLuckyMoneyPacketView
{
    top_margin : 289;
}

#EnterpriseLuckyMoneyMaskView
{
    view_height : 289;
}

/* 企业红包收取完成页面样式 */
#EnterpriseLuckyMoneyShareNickNameView
{
    top_margin : 25;
    font_width : 17;
}

#EnterpriseLuckyMoneyShareFeeView
{
    top_margin : 20;
    font_width : 80;
}

#EnterpriseLuckyMoneyShareHintView
{
    top_margin : 340;
    font_width : 14;
}

#EnterpriseLuckyMoneyShareButtonView
{
    top_margin : 10;
    left_margin : 40;
    view_height : 44;
}

#EnterpriseLuckyMoneyYenView
{
    left_margin : 8;
    top_margin : 230;
}

#EnterpriseLuckyMoneyAppFollowView
{
    bottom_margin : 23;
    checkbox_width : 18;
    font_width : 16;
    between_margin : 4;
}

/* 企业红包分享页面样式 */
#EnterpriseLuckyMoneyShareIconView
{
    top_margin : 90;
    view_width : 50;
    view_height : 60;
}

#EnterpriseLuckyMoneyShareNickNameView
{
    bottom_margin : 20;
    font_width : 17;
}

#EnterpriseLuckyMoneyHintTextView
{
    top_margin : 193;
    font_width : 24;
    top_margin_1 : 247;
    font_width_1 : 18;
    left_margin : 6;
}

#EnterpriseLuckyMoneyShareBtnView
{
    top_margin : 323;
    left_margin : 30;
    view_height : 45;
}

#EnterpriseLuckyMoneyBalanceTextView
{
    top_margin : 3;
}

/* 节日红包显示样式 */
#FestivalLuckyMoneyRate
{
    x_rate : 1;/*1.462;*/
    y_rate : 1;/*1.432;*/
}

#FestivalLuckyMoneyMoneyView
{
    top_margin : 199;/*201*/;
    font_width : 67;
    top_margin_1 : 217;
    top_margin_noscroll : 213;
    top_margin_noscroll_1 : 211;
    
    width: 319;
    height : 528;
    
    emoticon_margin_top : 51;
    emoticon_width : 240;
    emoticon_height : 240;
    
    emoticon_image_view_margin_top : 40;
    emoticon_image_view_margin_top_sender_show : 40;
    send_button_margin_bottom : 40;
    greeting_label_margin_top : 304;
    greeting_label_margin_top_receive : 335;
    greeting_label_margin_top_hk : 226;
    greeting_label_margin_top_with_emoticon_hk : 226;
    num_scroll_label_margin_top_hk : 0;
    change_num_label_margin_top_hk : 9;
    
    num_scroll_label_margin_top : 8;
    num_scroll_label_font_size : 60;
    
    num_scroll_unit_label_y : 37;
    
    change_num_label_margin_top : 16;
    change_num_label_font_size : 12;
    
    send_btn_margin_left : 24;
    send_btn_margin_bottom : 54;
    
    over_time_status_font_size : 20;
    
    save_balance_margin_top : 8;
    
    status_message_margin_bottom : 117;
    status_message_margin_bottom_with_emoticon : 117;
    
    add_emoticon_btn_width : 250;
    add_emoticon_btn_height : 100;
    
    add_emoticon_btn_label_offsetY : 4;
    add_emoticon_btn_image_offsetY : 0;
    
    add_emoticon_btn_margin_top : 160;
    
    close_btn_margin_top : 40;
    adjust_offsetY : 20;
    
    send_btn_bottom_padding: 30;
    image_box_left_padding: 30;
    title_image_top_padding: 40;
    greeting_tip_padding: 30;
    send_btn_left_padding: 30;
    
    emoticon_layer_corner_radius : 14;
    
    bgimageview_top_margin : 0;
    bgimageview_height : 210;
    bgimageview_bottom_margin: 10.2;
    bgimageview_bottom_margin_over_time : 96;
    
    sender_view_bottom : 22.3;
    
/*    greeting_label_margin_top_with_emoticon : 35.9;*/
    greeting_label_margin_top_with_emoticon : 226;
    loading_gif_width : 42;
    loading_gif_height : 42;
    
    unit_lable_font_size : 15;
    
    topMaskViewHeight : 9;
    bottomMaskViewHeight : 9;
    
    snow_gif_height : 233;
    
    fail_retry_btn_width : 101;
    
    camera_btn_extra_width : 30;
}

#FestivalLuckyMoneyMoneyView_896 {
    adjust_offsetY : 40;
}

#FestivalLuckyMoneyC2CNodeView
{
    nian_top_margin : 72;
    word_top_margin : 159;
    left_margin_1 : 29;
    right_margin_1 : 17;
    left_margin_2 : 21;
    right_margin_2 : 22;
    bottom_line_width : 19;
    bottom_line_left_padding_1 : 4;
    bottom_line_left_padding_2 : -3;
}

/* Festival lucky money sender view. */

/*#ShakeCardDetailView*/
/*{*/
/*    background_view_width : 325;*/
/*    title_font : 24;*/
/*}*/

#input_tool_view_tool {
    /* 其他情况都是2.5，6p下会模糊，改为2 */
    record_btn_topMargin : 2;
}

#WCRedEnvelopesReceiveHomeView { /* #WCRedEnvelopesReceiveHomeView */
    
    no_skin_has_emotioncon_title_marign_y : 108;
    no_skin_no_emotioncon_title_marign_y : 118;
    
    bg_image_view_width : 319;
    bg_image_view_height : 528;
    bg_image_mask_height : 422;
    
    head_bottom_image_view_height : 64.5;
    
    head_image_view_x : 16;
    head_image_view_width : 48;
    head_image_view_height : 48;
    head_Image_radius : 4.8;
    
    nick_name_title_margin_left : 15;
    nick_name_title_font : 17;
    
    desc_label_margin_top : 12;
    
    status_tips_title_y_margin : 6;
    status_tips_title_font : 14;
    
    wishing_tips_y_margin : 12;
    wishing_tips_title_font : 24;
    wishing_tips_bottom_margin : 25;
    
    bg_mask_image_height : 361.5;
    
    top_curve_image_height : 431;
    
    open_button_y_margin : 286.8;
    open_button_width : 110;
    open_button_height : 115;
    
    open_desc_title_font : 20;
    
    open_list_bottom_margin : 15;
    open_list_font : 15;
    
    money_icon_margin_bottom : 13;
    
    open_red_envelope_button_size : 106;
    open_red_envelope_button_font_size : 40;
    open_red_envelope_button_margin_bottom : 56;
    
    close_button_size : 13.2;
    close_button_margin_top : 4.5A;
    close_button_margin_right : 16.7;
    
    not_wish_laebl_left_margin : 30;
    not_wish_lable_font_size : 17;
    
    open_red_envelope_button_font : 25;
    
    adjust_offsetY : 20;
    close_button_margin_top_hasBtn : 28;
    float_send_btn_margin_top : 28;
}

#WCRedEnvelopesReceiveHomeView_896 {
    adjust_offsetY : 8
}

#WCRedEnvelopesReceiveHomeView_736
{
    bg_image_view_width : 311;
    bg_image_view_height : 515;
    bg_image_mask_height : 422;
    
    head_bottom_image_view_height : 62.91;
    
    head_image_view_x : 16;
    head_image_view_width : 48;
    head_image_view_height : 48;
    head_Image_radius : 4.8;
    
    nick_name_title_margin_left : 15;
    nick_name_title_font : 17;
    
    desc_label_margin_top : 12;
    
    status_tips_title_y_margin : 6;
    status_tips_title_font : 14;
    
    wishing_tips_y_margin : 12;
    wishing_tips_title_font : 24;
    wishing_tips_bottom_margin : 25;
    
    bg_mask_image_height : 352.60;
    
    top_curve_image_height : 420;
    
    open_button_y_margin : 286.8;
    open_button_width : 110;
    open_button_height : 115;
    
    open_desc_title_font : 20;
    
    open_list_bottom_margin : 14.63;
    open_list_font : 15;
    
    money_icon_margin_bottom : 13;
    
    open_red_envelope_button_size : 106;
    open_red_envelope_button_font_size : 39;
    open_red_envelope_button_margin_bottom : 52;
    
    close_button_size : 13.2;
    close_button_margin_top : 16;
    close_button_margin_right : 16.7;
    
    not_wish_laebl_left_margin : 30;
    not_wish_lable_font_size : 17;
    
    open_red_envelope_button_font : 25;
    adjust_offsetY : 0;
}

#WCRedEnvelopesRedEnvelopesDetailViewController
{
    nickname_label_margin_y_new : 24;
    nickname_label_margin_y_new_hasEmoticon : 24;
    topCoverImg_topMargin : 65;
    topCoverImg_topMargin_receiver : 65;
    thx_emoticon_view_topMargin : 28;
    thx_emoticon_view_noSelect_topMargin : 28;
    money_label_margin_y : 16;
    thx_emoticon_view_topMargin : 26;
    thx_emoticon_view_noSelect_topMargin : 26;
    emoticon_view_loading_topMargin : 50;
    emoticonBoardView_cutMargin : 50;
    nickname_label_leftRight_maxMargin : 40;
}

#WCRedEnvelopesWelcomePageViewController
{
    randomButtonMarginTop2017_1 : 450;
    randomButtonMarginTop2017_2 : 450;
    
    normalButtonMarginTop2017_1 : 516;
    normalButtonMarginTop2017_2 : 516;
    
    middleSceneImageMarginBottom2017_1 : 308;
    middleSceneImageMarginBottom2017_2 : 308;
    
    topImageMarginTop2017_1 : 183;
    topImageMarginTop2017_2 : 183;
    
    middleSceneImageMarginBottom2017_1 : 285;
    middleSceneImageMarginBottom2017_2 : 285;
    
    bottomSceneImageMarginBottom2017_1 : 27;
    bottomSceneImageMarginBottom2017_2 : 27;
    
}

#WCRedEnvelopesMakeRedEnvelopesViewController
{
    countSelectViewMoveOffset : 69;
    countSelectViewWithBannerMoveOffset : 69;
    
    textViewMoveOffset : 94;
    textViewMoveWithBannerOffset : 94;
    
    moneyInputTextFont : 17;
    
    wishLabelMarginTop: 10;
    
    makebuttonMarginMoney2017 : 19;
    moneyLabelMarginTop2017 : 8A;
    unitLabelMarginTop2017 : 28;
    
    bottomSceneImageMarginBottom2017 : 120;
}

#WCRedEnvelopesRedEnvelopesHistoryListViewController
{
    tableViewHeaderHeight : 366;
    yearSelectorMarginTop : 25;
    headImageMarginTop : 60;
    descriptionTextMarginTop : 24;
    receiveCountCenterMarginTop : 115;
    statusTitleMarginTop : 100;
}

#ExtraDeviceLogin
{
    tipsLabelYOffset : 20;
    cancel_button_offset : 36;
    confirm_button_offset : 16;
    device_tips_font_size : 17;
    device_cancel_button_font_size : 14;
}

#fts_common {
    searchbar_tag_max_width : 180;
}

#search_guide {
    sg_barbutton_top_margin : 50;
    sg_verts_title_top_margin : 37;
    sg_verts_title_bottom_margin_for_single_row : 34;
    sg_verts_title_bottom_margin_for_multi_row : 28.3;
    sg_verts_button_width : 110;
    sg_verts_button_intv: 33;
    
    sg_verts_title_font_size : 15.3 dynamic;
    sg_verts_title_font_size_for_discovery : 14.3 dynamic;
    sg_verts_button_title_font_size : 17.3 dynamic;
    sg_verts_button_title_font_size_for_discovery : 17.3 dynamic;
    sg_verts_button_title_small_font_size : 15.3 dynamic;
    sg_verts_button_title_small_font_size_for_discovery : 15.3 dynamic;
    
    sg_msg_verts_button_width : 110;
    sg_verts_button_title_small_font_size_for_discovery : 13.3 dynamic;
    
    sg_weapp_content_width : 300;
    sg_weapp_title_bottom_margin : 30;
    sg_weapp_title_top_margin : 50;
}

#search_discovery {
    discovery_search_text_font_size : 17;
}

#text_actionSheet
{
    cancel_btn_top_offset : 7 dynamic;
}

#PayTouchIDConfirmView
{
    bg_width : 270;
    
    title_font : 15;
    subtitle_font : 12;
    btn_font : 15;
    
    img_top : 17;
    title_top : 11;
    subtitle_top : 10;
    btn_top : 27;
    btn_height : 43;
}

#CardItemView
{
    card_left : 10.0;
	top_height : 10.0;
	normal_height : 136.0;
	tooth_height : 3.0;
	bottom_mask_height : 34.0;
	title_font : 13.0;
	title_2_sub : 8.0;
	sub_title_font : 22.0;
	bottom_font : 13.0;
	logo_width : 42.0;
	logo_left : 15.0;
	logo_top : 20.0;
    head_width: 54.0;
    head_left : 14.0;
    entry_title_font : 20.0;
    entry_sub_title_font : 16.0;
    invoice_title_left: 20;
}

#CardCodeView
{
    begin_y : 60.0;
    bottom_margin : 150.0;
    dashline_margin : 35.0;
    checkbox_fit_y  : 35.0;
    tips_fit_y : 100.0;
    qrcode_img_height : 168.0;
    subTitle_line_offset : 59.5;
    field_button_font : 20.0;
}

#CardConsumedView
{
    doneLabel_bottom : 82;
}

#GiftCardView
{
    WCGiftcardBackWidth : 140;
    WCGiftCardBackHeight: 84;
    WCGiftcardBacCardTitleNormalFont:26.7;
    WCGiftCardLineHeight: 0.33;
    WCGiftCardShadowHeight: 29.3;
    WCGiftCardBackBorderWidth : 0.33;
    WCGiftcardEnvelopseOffset : 26;
    WCGiftcardBackCardCorner : 5;
    WCGiftcardEdgeWidth:30;
    WCGiftcardGiftItemEdgeWidth:60;
}

#BizMainHeaderView
{
    height : 175;
    padding-top :50;
    titleFontSize : 17.6;
    descFontSize : 14.4;
    margin : 15;
}

#BizMainSectionHeaderView
{
    height : 63;
    fontSize : 15;
}

#WCMallActivityView
{
    activityLabelFontSize:15;
    activityFunctionLabelFontSize:13;
    margin : 10;
}

#WCFacingReceivePayerView { /* #WCRedEnvelopesReceiveHomeView */
    
    total_money_height : 50;
    total_money_tips_font : 14;
    total_money_money_font : 15;
    
    header_nickname_rightmargin : 10;
    
    status_font : 17;
    money_font : 25;
    
    payer_name_font:14;
    
    actionbar_top_margin : 15;
}

#chargeMobileView
{
    input_phone_view_height : 87;
    input_phone_view_select_address_top_margin : 19;
    footer_button_itern_margin : 10;
    function_button_mainTitle_font : 17;
    function_button_attrTitle_font : 11;
}

#EmoticonCamera {
    emoticon_record_button_horizonal_margin: 56;
    emoticon_record_tips_bottom_margin: 32;
    emoticon_preview_vertical_margin: 64;
    emoticon_preview_tool_top_margin: 72;
    emoticon_colorBar_unselectedPadding: 28;
    emoticon_comfirmbutton_right_margin: 56;
    emoticon_comfirmbutton_bottom_margin: 64;
    emoticon_lens_top_margin: 40;
}

#WCOutView {
    /* dial pad */
    padBtnDiameter : 118;
    dialPadVerticalPadding : 1;
    dialPadHorizontalPadding : 1;
    callBtnMarginKeyPad: 44;
    
    /* dial view */
    inputArea_marginTop: 54;
    inputArea_phoneNumber_marginBottom: 8;
    inputArea_phoneNumber_fontsize : 36;
    inputArea_name_fontsize : 20;

    /*calling view*/
    headImageMarginTop: 75;
    userLabelMarginTopWhenNoHeadImage: 57;
    
    /*account  view*/
    headerViewHeight: 136;
    balanceFontSize: 18;
    remainingTimeFontSize :40;
    balanceTopMargin : 6;
    remainingTimeTopMargin : 24;
    productBtnTitleSize:26;
    faqBtnTopMargin:140;
}

#WCOutActivityView {
    title_margin_bottom : 158;
    title_margin_Left_right : 64;
    title_line_hight : 50;
    desc_margin_top : 24;
    pic_margin_top : 70;
    img_height_width : 150;
    try_btn_margin_bottom : 90;
    
    title_font_size : 40;
    desc_font_size : 20;
    
}

#WCOutPackageView {
    price_font_size : 40;
}

#NewMultiSelectViewController
{
    select_display_view_height : 136;
    
    select_display_item_topOffset1 : 28;
    select_display_item_topOffset2 : 10;
    select_display_item_leftOffset1 : 16;
    select_display_item_leftOffset2 : 25;
    select_display_item_interSpacing1 : 20;
    select_display_item_interSpacing2 : 25;
    select_display_item_lineSpacing1 : 100;
    select_display_item_lineSpacing2 : 10;
    
    select_viewController_rightBarButton_margin : -14;
}

#MultiTalkContactCell
{
    head_image_size : 80;
}

#MultiTalkTalkingOperateView
{
    title_margin_top : 30;
    title_margin_left : 30;
    title_height : 30;
    title_font_size : 24;
    title_text_color : "#ffffff";
    
    timer_margin_left : 30;
    timer_font_size : 15;
    timer_text_color : "#ffffff";
    
    minimize_button_center_margin_left : 60;
    minimize_button_center_margin_bottom : 125;
    
    hangup_button_center_margin_bottom : 76;
    hangup_button_height : 75;
    
    righttop_button_center_margin_center : 110;
    righttop_button_center_margin_bottom : 150;
    righttop_button_width : 60;
    righttop_button_height : 78;
    righttop_title_margin_top : -19;
    
    middletop_button_center_margin_left : 60;
    middletop_button_center_margin_bottom : 150;
    middletop_button_width : 60;
    middletop_button_height : 78;
    middletop_title_margin_top : -19;
    
    lefttop_button_center_margin_center : 110;
    lefttop_button_center_margin_bottom : 150;
    lefttop_button_width : 60;
    lefttop_button_height : 78;
    lefttop_title_margin_top : -19;
}

#MultiTalkBottomOperatePanel
{
    description_button_width : 64;
    description_button_height : 83.2;
    description_button_title_offset : -19;
    description_button_left_right_margin : 64;
    description_button_top_margin : 40;

    hangup_button_size : 72;
    hangup_button_bottom_margin : 32;
    hangup_button_folded_size : 44;
    hangup_button_folded_bottom_margin : 24;

    arrow_button_size : 32;
    arrow_button_left_margin : 40;
    
    flip_camera_button_size : 64;
    flip_camera_button_right_margin : 64;
    flip_camera_button_bottom_margin : 36;

    panel_corner_radius : 16;
    panel_bottom_extra_height : 20;
    panel_height : 272;
    panel_folded_height : 92;
    
    mute_button_image_name : "multitalkMuteMode.png";
    mute_button_hl_image_name : "multitalkMuteModeHL.png";
    mute_button_selected_image_name : "multitalkMuteModeOn.png";
    
    speaker_button_image_name : "multitalkSpeakerMode.png";
    speaker_button_hl_image_name : "multitalkSpeakerModeHL.png";
    speaker_button_selected_image_name : "multitalkSpeakerModeOn.png";
    
    video_button_image_name : "multitalkVideo.png";
    video_button_hl_image_name : "multitalkVideoHL.png";
    video_button_selected_image_name : "multitalkVideoOn.png";
}

#MultiTalkBeforeTalkingOperateView
{
    hangup_button_margin_center : 50;
    hangup_button_center_margin_bottom : 76;
    hangup_button_height : 75;
}

#MusicPlayerMainViewController
{
    bottom_bg_height : 200;
    shade_Gradient_height : 285;
    music_title_view_height : 58;
    single_lyric_height_between_cover:60;
    first_page_cover_height : 500;
    second_page_cover_height : 180;
    /*cover_image_offset = (first_page_cover_height-second_page_cover_height)/2 */
    cover_image_offset : 160;
    
    table_view_height_to_top : 260;
    lyrics_table_view_height : 315;
    max_table_view_height : 315;
    
}

#NewYearSnsFeedView
{
    imageview_width : 180;
    imageview_height : 135;
}

/*WCFestivalRedEnvReceiveHomeView*/
#WCFestivalRedEnvReceiveHomeView
{
    bg_frame_height : 400;
    bg_frame_width : 300;
    
    logo_width : 42;
    logo_height : 42;
    
    nickname_font : 15;
    nickname_top_margin : 6;
    
    descTitle_font : 23;
    descTitle_top_margin : 24;
    
    linkLabel_font_size : 16;
    
    hb_cover_remain_height : 56;
}

#WCNewYearEnterpriseHBDetailView
{
    default_padding : 15;
    bgView_top_margin : 74;
    bgView_left_margin : 28;
    
    coverFrame_top_margin : 40;
    coverFrame_left_margin : 16;
    
    headImage_top_margin : 24;
    headImage_size : 41;
    
    nameLabel_font_size : 16;
    nameLabel_top_margin : 14;
    
    numView_font_size : 50;
    numView_top_margin : 44;
    
    moneyLabel_font_size : 12;
    
    tipsLabel_font_size : 15;
    tipsLabel_top_margin : 50;
    
    fixBar_width : 18;
}

#WCPayFetchView
{
    top_padding_1: 20;
    top_padding_2: 20;
    top_padding_3: 15;
    top_padding_4: 9;
    cardInfo_leftMargin : 24;
    card_logo_len : 16;
    
    money_textfield_font : 56;
}

#WCPayPaidOrderVCActivityView
{
    marginView_height : 15;
    activityView_height : 110;
    iconView_left_margin : 15;
    iconView_size : 47;
    
    solgnLabel_left_margin : 10;
    titleLabel_top_margin : 8;
    titleLabel_font_size : 22;
    activityBtn_title_margin : 15;
    
    firstFlower_x : 196;
    firstFlower_y : 9;
    secondFlower_x : 55;
    secondFlower_bottom_margin : 10;
    thirdFlower_activityBtn_left_margin : 24;
    thirdFlower_activityBtn_y : 43;
}

#ShareCardMemberCardView
{
    img_left_margin : 32;
    img_top_margin_to_bar : 12;
    img_top_margin_to_title : 40;
    accept_top_margin_to_card : 130;
    secondary_field_top_padding : 26;
}

#ShareCardMemberCell
{
    member_cell_height : 96;
    member_icon_topOffset : 19;
    home_member_icon_topOffset : 20;
}

#WCPayOverseaTransferViewController
{
    head_image_view_top_margin : 15;
    display_name_label_top_margin : 10;
    panel_view_left_margin : 15;
    panel_view_top_margin : 15;
    panel_view_bottom_margin : 15;
    input_title_label_top_margin : 20;
    input_title_label_left_margin : 20;
    currency_input_view_left_margin : 20;
    currency_input_view_top_margin : 15;
    currency_input_view_height : 55;
    line_view_left_margin : 20;
    line_view_top_margin : 5;
    line_view_height : 0.5;
    comment_text_view_left_margin : 20;
    comment_text_view_top_margin : 10;
    comment_text_view_height : 16;
    confirm_button_left_margin : 30;
    confirm_button_top_margin : 30;
    hkMoneyLabelFont : 40;
}

#WCPayCurrencyInputView
{
    currency_label_font_size : 50;
    number_text_field_font_size : 55;
    number_text_field_height : 55;
}

/* 全屏提示界面 */
#online_device_info {
    /*  上边的设备图片的上边距  */
    controlButtonsMargin : 33;
    controlButtonsYOffset : 143;
    tipsLabelYOffset : 20;
    deviceViewY : 168;
    logOutBtnWidth : 167;
    logOutBtnMarginDeviceViewTop: 426;
    logOutBtnMarginBottom: 96;
    tips_label_font_size : 17;
    control_button_marginTop : 10;
    control_button_title_font : 12;
    containerView_YOffset: 60;
}

#WCPayOverseaMainWalletCollectionView
{
    banner_height : 175;
}

#WAHomeListView
{
    row_num_perpage : 4;
    row_count : 4;
    
    cell_width : 175.3;
    cell_height : 150;
    section_hon_margin : 20;
    section_ver_margin : 10;
    
    narrow_header_top_margin : 30;
    narrow_header_bottom_margin : 12;
    wide_header_top_margin : 45;
}

#WCBackupEntryView
{
    icon_image_content_y : 180;
    icon_image_height : 100;
    icon_image_width : 160;
    icon_label_gap : 52;
    tip_max_content_x : 25;
    tip_tip_gap : 14;
    green_button_height : 35;
    grey_button_height : 17;
    button_button_gap : 66;
    grey_button_bottom : 32;
    left_corner_button_y : 20;
    left_corner_button_x : 16;
}

#WCPayOfflineCheckTipsView
{
    tipsContentTopMargin : 24;
    confirmBtnBottomMargin : 64;
    confirmBtnBottomMargin_hasContent : 105;
    
    tipContentLabelFontSize : 17;
}

#MultitalkBannerView
{
    banner_height : 44;
    fold_tips_label_height : 16;
    unfold_tips_label_height : 16;
    unfold_title_label_height : 38;
    unfold_button_height : 50;
    unfold_title_margin_top  : 20;
    unfold_label_margin_left : 33;
    arrow_icon_margin_right : 17;
    
    fold_tips_label_font : 15.0;
    unfold_title_label_font : 17.0;
    unfold_tips_label_font : 14.0;
    banner_button_font : 17.0;
    
    headimg_size : 33;
    headimg_title_space : 10;
    headimg_button_space : 20;
    headimg_max_space : 11;
    
    unfold_button_headimg_space : 22;
    
    invite_nick_max_len : 16;
}

#WCPayTransferMoneyViewController
{
    currencyLabelFont : 30;
    moneyLabelFont : 50;
    hkMoneyLabelFont : 42;
    moneyLabelHeight : 55;
    moneyLabelTopMargin : 5;
    transferBtnTopMargin : 30;
}

#WCPayTransferMoneyPaidSuccessViewV2_736
{
    receiverLabel_margin_top : 64;
}

#WCPayTransferMoneyPaidSuccessViewV2
{
    WeChatPayIcon_margin_top : 8.5A;
    WeChatPayIcon_height : 28;
    WeChatPayIcon_width : 32;
    
    successLabel_margin_top : 146;
    successLabel_font_size : 17;
    
    tipLabel_margin_top : 66;
    tipLabel_font_size : 17;
    tipLabel_font_color : "#353535";
    
    feeLabel_margin_top : 186;
    feeLabel_margin_top_on_transfer : 274;
    feeLabel_font_size : 30;
    feeLabel_font_color : "#353535";
    
    moneyLabel_margin_top : 50;
    moneyLabel_margin_top_on_transfer : 14;
    moneyLabel_font_size : 48;
    moneyLabel_font_color : "#353535";
    
    receiverLabel_margin_top : 92;
    dataLabel_margin_up : 13;
    dataLabel_margin_left : 3A;
    
    dataLabel_font_size : 14;
    dataLabel_font_color : "#888888";
    
    doneButton_margin_bottom : 96;
    doneButton_width : 180;
    
    head_image_size : 28;
    head_image_margin_right : 8;
    
    line_view_margin_top : 18;
    line_view_margin_top_on_transfer : 81;
    line_view_margin_left : 3A;
    line_view_height : 0.5;
    line_view_color : "#C7C7C7";
    
    chargefeeLabel_margin_up : 20;
    
    firstDataLabel_margin_up : 16;
    receiverLabel_margin_up_no_lineView : 80;
    head_image_margin_up_no_lineView : 28;

}

#OfflinePayView
{
    actionbar_top_margin : 15;
    cardInfo_view_height : 80;
    actionbar_content_height : 64;
    
    bottom_button_content_height : 67;
    bottom_button_elem_title_font : 17;
    bottom_button_elem_subTitle_font : 17;
    
    codeview_noticecontent_margin : 30;
    codeview_barcode_container_height : 136;
    codeview_barcode_height : 102;
    codeview_qrcode_width : 147;
    codeview_qrcode_bottom_margin : 30;
    codeview_tips_font : 14;
    
    change_card_view_bankName_font : 17;
    change_card_view_bankDesc_font : 14;
    change_card_view_forbidDesc_font : 14;
}

#VoiceInputViewController
{
    fullScreenTextViewHeight: 320;
    cancel_Button_Left_Margin: 45;
    halfScreenPadHeight: 330;
    textViewDefaultHeight: 160;
}

#FaceHB
{
    get_AvatarWidth : 60;
    get_OpenedAvatarWidth : 70;
    get_OpenViewWidth : 325;
    get_OpenViewHeight : 430;
    get_mask_image_height : 245;
    get_OpenBtn_topMargin : 82;
    
    pay_QR_Width : 190;
    pay_QR_Top_Margin : 65;
    pay_HB_SmallWidth : 128;
    pay_HB_BigWidth : 300;
    pay_HB_Name_Font : 18;
    pay_HB_Yuan_Font : 18;
    pay_HB_Amount_Font : 43;
    pay_HB_SelectY : 35;
    
    pay_Receive_Top_Margin : 40;
    pay_Receive_HeadWidth : 50;
    pay_Receive_MaxShowCount : 5;
}

#PaidNewDetailView {
    logo_top_margin : 26;
    logo_top_margin_min : 6;
    logo_image_width : 32;
    logo_image_height : 28;
    brandLabel_top_margin : 12;
    sellerLabel_top_margin : 96;
    sellerLabel_top_margin_min : 48;
    moneyLabel_top_margin : 14;
    moneyLabel_fee_margin : 5;
    
    discount_content_top_margin : 6A;
    
    tinyApp_content_height : 80;
    tinyApp_line_leftright_margin : 22;
    tinyApp_bg_left_margin : 35;
    tinyApp_logo_len : 50;
    tinyApp_small_logo_y : 1.5;
    tinyApp_small_logo_len : 15;
    
    finish_button_width : 180;
    finish_button_bottom_margin_normal : 96;
    finish_button_bottom_margin_with_activity : 70;
    finish_button_bottom_margin_with_activity_subscribe : 100;
    finish_button_top_margin_min : 70;
    subscribe_content_top_margin : 30;
    
    activity_content_height : 86;
    activity_content_leftright_margin : 15;
    activity_content_bottom_margin : 15;
    activity_logo_left_margin : 20;
    activity_button_innerMargin : 30;
    
    brandLabel_font : 17;
    fee_type_font : 30;
    money_font : 48;
    discount_content_font : 13;
    tinyApp_name_font : 13;
    tinyApp_desc_font : 20;
}

#MobileChargeHistoryView {
    history_cell_height : 70;
    history_cell_phone_font : 19;
    history_cell_username_font : 13;
    history_cell_content_margin : 4;
    history_cell_padding_margin : 34;
    history_cell_maxrow : 3;
}

#UploadIDTipsViewController {
    icon_top_margin : 48;
    title_label_top_margin : 24;
    content_view_width : 343;
    content_view_height : 604;
    separate_line_margin : 24;
    
    title_font : 17;
    content_leftRight_margin : 24;
}

#FaceReco {
    icon_top : 104;
    icon_tip_margin : 40;
    info_footer_margin : 40;
    info_leftview_width : 113;
    title_top : 64;
    title_frame_margin : 0;
    frame_width : 300;
    frame_height : 400;
    big_tips_title_left_margin : 20;
    err_tips_left_margin : 20;
    num_font : 110;
    
    confirm_icon_left_margin : 27;
    confirm_icon_top_margin : 63;
    confirm_icon_name_gap : 11;
    confirm_slogan_font : 22;
    rules_info_screen_bottom_gap : 26;
    rules_tip_info_gap: 12;
    
    prepare_readnumber_y : 220;
    start_button_width : 144.3;
    start_button_height : 40;
    start_button_prepareLabel_gap : 200;
    
    result_close_btn_screen_gap : 110;
    
    guide_step_font : 20;
    guide_step_title_y : 90;
    guide_step_title_numbericon_gap : 60;
    guide_numberIcon_left_margin : 90;
    guide_numbericon_width : 24;
    guide_step_content_numbericon_gap : 10;
    guide_step_content_title_gap : 20;
    guide_step_image_content_gap : 25;
    guide_step_face_image_width : 80;
    guide_step_number_image_width : 120;
    guide_step_number_image_height : 35;
    guide_numbericons_gap : 180;
    guide_finish_btn_bottom_gap : 110;
    guide_finish_btn_width : 144.3;
    guide_finish_btn_height : 40;
    guide_title_left_margin : 10;
}

#FaceRecoLight {
    
    frame_width : 224;
    frame_top : 153;
    title_frame_margin : 46;
    loading_frame_margin : 30;
    
    
    guide_top : 120;
    guide_title_margin : 24;
    guide_detail_margin : 11;
}

#FaceRecoRamPose {
    
    create_icon_top : 100;
    setting_icon_top : 60;
    setting_btn_margin : 169;
    setting_cell_height : 53;
    
    icon_top : 176;
    
    frame_width : 300;
    frame_top : 133;
    frame_circle_margin : 10;
    
    result_icon_top : 171;
    
    title_font : 22;
    big_tips_title_left_margin : 5;
    
    err_tips_left_margin : 20;
    
    start_button_width : 144.3;
    start_button_height : 40;
    start_button_prepareLabel_gap : 200;
    
    result_close_btn_screen_gap : 140;
}

#WCPayOfflineAddNewCardTipsView
{
    tipsContentFontSize : 17;
    tipsTitleTopMargin : 30;
    addNewCardBtnLeftMargin : 80;
    addNewCardBtnTopMargin : 40;
    viewPayCardBtnBottomMargin : 35;
    iconImgViewTopMargin : 48;
}

#WCLabsSettingViewController {
    header_blank_height : 25;
    footer_tip_toppadding : 35;
    header_icon_title_padding : 25;
    header_desc_padding_supplement : 0;
}

#WCLabsSettingViewControllerNew {
    header_right_icon_top: 76;
    header_height: 261;
}

#OfflinePayPreConfirmView
{
    confirmview_icon_topMargin : 110;
    confirmview_content_fontsize : 18;
    confirmview_content_topMargin : 30;
    confirmView_content_leftRight_margin : 170;
    confirmview_btn_topMargin : 30;
    confirmview_btn_width : 190;
}

#MMTableViewIndex
{
    indexViewItemHeight : 16;
    indexRoundImageViewWidth : 14;
    indexViewFontSize : 10;
}

#LQTDetailView {
    header_logo_topMargin : 8;
    header_bankName_topMargin : 8;
    header_bankName_fontSize : 17;
    footer_fontsize : 14;
    header_view_bottomMargin : 24;
    
    content_view_height : 468;
    content_view_height_with_activity : 504;
    activity_btn_height : 56;
    activity_btn_bottom_margin : 32;
    content_title_fontSize : 14;
    content_title_topMargin : 40;
    content_uint_fontSize : 25;
    content_money_topMargin : 12;
    content_money_bottomMargin : 40;
    content_button_topMargin : 40;
    content_sepBar_height : 1;
    
    enterContent_view_height : 56;
    enterContent_fontsize : 17;
    
    tinyappContent_view_height : 70;
    tinyappContent_title_font : 13;
    tinyappContent_desc_font : 15;
    tinyappContent_name_font : 10;
    tinyappContent_topBottom_margin : 15;
    
    footer_btn_margin : 20;
    lct_ope_padding_top : 19;
}

#LQTMoneyView {
    inputContent_cell_height : 255;
    btn_cell_height : 106;
    btn_with_ptotocol_cell_height : 160;
    
    inputContent_topbar_topMargin : 29;
    inputContent_cell_topMargin : 16;
    
    inputContent_leftRight_margin : 32;
    inputContent_title_fontSize : 14;
    inputContent_title_topMargin : 18;
    inputContent_uint_fontSize : 30;
    inputContent_money_fontSize : 56;
    inputContent_money_height : 60;
    inputContent_cardInfo_leftMargin : 15;
    
    btn_cell_checkbox_len : 15;
    btn_cell_protocol_topMargin : 30;
    btn_cell_btn_topMargin : 30;
    btn_cell_btn_topMargin_withProtocol : 15;
    btn_cell_btn_height : 47;
    
    inputContent_topbar_tips_fontSize : 14;
    
    redeemMarginTop : 24;
    redeemTypeHeight : 64;
}

#WCPayLQTRedeemTypeCell {
    paddingLeft : 32;
    titleMarginUp : 12;
}

#LQTTransView {
    trans_succ_icon_topMargin : 50;
    trans_tips_topMargin : 25;
    trans_unit_fontSize : 30;
    trans_money_topMargin : 16;
    trans_button_topMargin : 100;
}

#WCWebSearchViewControllerNewH5{
    TextField_Top_Margin : 40;
}


#HoneyPayHomeViewController {
    
    welcome_icon_top : 50;
    welcome_font_size : 17;
    welcome1_top_margin : 48;
    welcome2_top_margin : 18;
    welcome_add_button_top_margin : 88;
    
}

#WCPayBizF2FViewController {
    leftRightMargin : 15;
    contentLeftRightMargin : 30;
    
    topBarMargin : 30;
    topBarHeight : 90;
    
    mch_image_len : 40;
    
    mch_name_font : 15;
    nick_name_font : 13;
    
    amountTitleTopMargin : 20;
    amountTitle_font : 14;
    unitLabel_font : 30;
    amountTextField_font : 56;
    amountTextField_height : 55;
    
    descLabelTopMargin : 24;
    fixAmountContainerViewTopMargin : 18;
    
    transferBtnTopMargin : 30;
    
    commentTextViewTopMargin : 30;
    contentViewBottomMargin : 23;
}

#WCPayDigitalCertManageViewController
{
    cert_cell_title_margin_left : 20;
    cert_cell_desc_margin_left : 20;
}

#WCPaySecuritySettingViewController
{
    head_title_font_size : 24;
    head_desc_font_size : 14;
    head_title_top_margin : 54;
    head_title_left_margin : 90;
    head_title_right_margin : 55;
}

#WCPayRewardViewController {
    headImage_len : 62;
    headImage_topMargin : 50;
    
    view_container_cornerRadius : 3;
    view_leftRightMargin : 20;
    
    payer_detail_nickname_font : 17;
    payer_detail_desc_font : 24;
    payer_detail_amount_leftrightMargin : 17;
    payer_detail_amount_innerMargin : 10;
    payer_detail_amount_font : 21;
    
    editmoney_amount_container_topMargin : 23;
    editmoney_desc_container_topMargin : 20;
    editmoney_button_TopMargin : 177;
    editmoney_amount_container_height : 60;
    editmoney_amount_font : 17;
    
    confirm_money_title_font : 17;
    confirm_money_uint_font : 33;
    confirm_money_font : 48;
    confirm_money_margin : 7;
    confirm_money_continer_topMargin : 10;
    confirm_money_btn_topMargin : 177;
    
    intro_view_icon_topMargin : 96;
    intro_view_tips_leftrightMargin : 82;
    intro_view_btn_width : 180;
    intro_view_btn_topMargin : 40;
    intro_view_height : 444;
    
    setup_view_headImg_topMargin : 50;
    setup_view_amount_font : 20;
    setup_view_confirm_btn_topMargin : 36;
    setup_view_desc_container_height : 70;
    setup_view_tips_font : 17;
    setup_view_amount_container_height : 64;
    setup_view_bottom_tips_font : 13;
    
    codeView_len : 220;
    receiver_detail_codeview_topMargin : 100;
    receiver_detail_desc_font : 21;
    receiver_detail_saveBtn_topMargin : 120;
    receiver_detail_btn_innerMargin : 70;
    
    invalid_tipsView_height : 36;
    
    keyboard_topMargin : 30;
}

#WCPayTrasnferToBankCardViewController
{
    kBankIconViewTopMargin : 15;
    kBankInfoLabelTopMargin : 12;
    kCurrencyInputViewTopMargin : 15;
    kCurrencyInputViewHeight : 55;
    kLineViewTopMargin : 5;
    kConfirmButtonTopMargin : 30;
    kBottomDescLabelTopMargin : 20;
    kConfirmBottonMarginTop : 30;
    kConfirmBottonMarginBottom : 40;
}

#WCPayTransferToBankCardPaidSuccessViewController {
    kIconMarginTop : 114;
    kBriefViewHeight : 183;
    kBriefViewTitleFontSize : 18;
    kDetailLabelFontSize : 15;
    kDetailLabelMarginGap : 16;
}

#WCPayNewPwdViewController {
    kTitleTopMargin : 80;
    kPasswordTextFieldGap : 80;
    kPwdCrtlLabelFont : 15;
    kPwdCrtlLabelGap : 28;
    kBiotricBtnBottomGap : 150;
    kBiotricIconScale : 1;
    kOldPasswordTextFieldGap : 96;
}

#WCPayPaidSuccessStatusViewController {
    kIconMarginTop : 114;
    kBriefViewHeight : 183;
    kBriefViewTitleFontSize : 17;
    kDetailLabelFontSize : 15;
    kDetailLabelMarginGap : 8;
    kDetailLabelTipsFontSize : 14;
}

#WCPayBindCardSuccessViewController
{
    icon_size : 80;
    icon_margin_up : 53;
    icon_not_button_margin_up : 88;
    
    title_font_size : 20;
    title_color : "#353535";
    title_margin_up : 28;
    
    desc_font_size : 14;
    desc_color : "#888888";
    desc_margin_up : 15;
    
    topLine_color : "#E5E5E5";
    topLine_margin_left : 35;
    topLine_margin_up : 152;
    topLine_height : 0.5;
    
    bankIcon_size : 46;
    bankIcon_margin_left : 35;
    bankIcon_margin_up : 20;
    
    bankTitle_font_size : 13;
    bankTitle_color : "#B2B2B2";
    bankTitle_margin_left : 14.5;
    bankTitle_margin_up : 21;
    
    bankDesc_font_size : 20;
    bankDesc_color : "#353535";
    bankDesc_margin_left : 14.5;
    bankDesc_margin_up : 3;
    
    bankButton_width : 60;
    bankButton_margin_right : 35;
    bankButton_margin_up : 30;
    
    bottomLine_color : "#E5E5E5";
    bottomLine_margin_left : 35;
    bottomLine_margin_up : 21;
    bottomLine_height : 0.5;
    
    doneButton_width : 180;
    doneButton_margin_up : 535;
    doneButton_no_button_margin_up : 520;
}

#WAGameActionSheet
{
    landscape_icon_size : 66;
    portrait_icon_size : 60;
    
    landscape_width : 342;
    
    landscape_menuitem_font : 12;
    portrait_menuitem_font : 11;
    
    landscape_menu_to_title : 9;
    portrait_menu_to_title : 8;
    
    landscape_btn_heihgt : 44;
    portrait_btn_heihgt : 50;
    
    landscape_icon_margin : 22;
    portrait_icon_margin : 30;
    
    menu_heihgt : 118;
    btn_icon_size : 25;
    btn_font : 17;
    head_honrizon_margin : 20;
    single_icon_size : 55;
    single_icon_font : 16;
}

#multi_select_tool_view {
    btn_margin_border : 66;
}

#WCPayAutoDeductVC {
    productNameFont : 17;
    productNameTopMargin : 20;
    sellerNameFont : 14;
    feeFont : 42;
    feeLabelTopMargin : 24;
    leftRightMargin : 20;
    switchContentTopMargin : 45;
    switchContentInnerMargin : 15;
    infoDescFont : 14;
    footerButtonTopMargin : 30;
}

#WCPayF2FMiddleViewController
{
    headWordingView_fontSize : 14;
    keyLabel_fontSize : 14;
    valueLabel_fontSize : 14;
}

#WCPayBalanceDetailUI
{
    mainLogoTopMargin : 48;
    mainContentViewBottomWhiteMargin : 40;
    
    footerButton_bottomMargin : 64;
    
    lqt_action_font : 16;
}

#WebMinimizationView
{
    webCircle_radius : 136;
    webCircle_bottomMargin : 16;
    webCircle_sideMargin : 24;
    webCircle_iconSize : 48;
    
    webCircle_corner_radius : 160;
    webCircle_corner_bottomMargin : 48;
    webCircle_corner_sideMargin : 32;
    webCircle_corner_iconSize : 48;
    
}

#ResetPwdViewController
{
    fillCredInfo_header_height : 110;
    fillCredInfo_headerTitle_topMargin : 56;
    fillCredInfo_headerTitle_font : 22;
    fillCredInfo_font : 17;
    fillCredInfo_itemLeftMargin : 30;
    fillCredInfo_leftRightMargin : 30;
    fillCredInfo_cell_Height : 60;
    fillCredInfo_button_width : 190;
    fillCredInfo_button_topMargin : 230;
    
    verifyWay_leftRightMargin : 24;
    verifyWay_arrowRightMargin : 15;
    verifyWay_titleFont : 20;
    verifyWay_descFont : 14;
}

#WCPayECardBankCardListViewController {
    title_topMargin : 48;
    bank_arrow_rightMargin : 48;
    leftRight_margin : 32;
}

#WCPayLQTDepositEntryViewController
{
    Confirm_Button_Bottom_Margin : 130;
}

#WCPayLQTDepositNewPlanViewController
{
    kTableViewMarginLeft : 32;
    kDepositAmountFontSize : 14;
    kDepositAmountMarginTop : 40;
    kTextFieldMoneyFontSize : 48;
    kDepositErrorTipsFontSize : 14;
    kDepositErrorTipsColor : "#FA5151";
    kTextFieldAmountMargin : 16;
    kTextFieldHeight : 55;
    kTextFieldLineViewMargin : 8;
    kTipsLabelMarginBottom : 24;
    kCellSeparatorMargin : 20;
    kCellHeight : 56;
    kItemTitleRightMargin : 10;
    kAgreeBtn_LinkText_Margin : 4;
    kTableView_ScrollOffset : 33;
    protocolView_scrollOffset : 60;
    protocolView_scrollOffset_marign : 24;
    productWordLabel_font : 24;
    productWordLabel_mriginUp : 56;
    marketingWordLabel_font : 17;
    marketingWordLabel_marginUp : 16;
    protocol_view_bottomMargin : 58;
    keyboard_offset_margin : 0;
}


#WCRedEnvelopesSelectSkinCellView {
    /*    cell_left_margin : 1.5A;*/
    /*    cell_right_margin : 0.5A;*/
    
    cell_top_margin : 1.5A;
    
    cell_height : 424;
    cell_width : 256;
    /*    collection_view_header : 3A;*/
    /*    collection_view_line_spacing : 1A;*/
    corp_name_font_size : 17;
    descript_label_font_size : 14;
    corp_name_label_bottom_margin : 4;
    over_time_label_bottom_magin : 14;
    /*    select_image_size : 3A;*/
    /*    select_image_right_margin : 1.5A;*/
    /*    select_image_top_margin : 1A;*/
    /*    corp_label_bottom_margin : 9;*/
    selected_image_view_size : 20;
    selected_image_right_margin : 24;
    selected_image_bottom_margin : 24;
    
    border_width : 2;
    over_time_label_font_size : 12;
    select_label_font_size : 14;
    /*    over_time_label_top_margin : 9;*/
    select_label_left_margin : 4;
    
    promotion_cell_top_margin : 12A;
    promotion_cell_height : 238;
    promotion_cell_width : 18A;
    promotion_cell_corp_name_font_size : 17;
    promotion_cell_wording_font_size : 14;
    promotion_cell_wording_top_margin : 4A;
}

#WCRedEnvelopesSelectSkinFlowLayout_896 {
    cell_top_offset : -50;
}

#WCRedEnvelopesSelectSkinFlowLayout {
    cell_width : 280;
    cell_height : 496;
    cell_margin : 10;
    cell_top_margin : 5A;
    exipre_desc_margin_top : 16;
}

#WCPayLQTRedeemTypeCell {
    titleFontSize : 15;
}

#WCPayLQTPanelView {
    content_leftRight_margin : 32;
    
    title_topMargin : 24;
    
    button_height : 48;
    
    scroll_bottomMargin : 0;
    scroll_item_img_leftMargin : 32;
    scroll_item_img_rightMargin : 24;
    scroll_item_img_len : 48;
    scroll_item_font : 17;
}

#WCPayOrderPayConfirmView
{
    touchIDButton_marginLeft : 68;
    background_width : 320;
}

#WCStoryBubbleView {
    bubble_view_max_width : 288;
}

#PayMoneyLogic
{
    modal_background_width : 320;
}

#RecordView
{
    operate_button_radius : 12A;
    operate_button_center_margin_bottom : 259;
    recordtip_bg_center_y :  45% height;
    record_trans_tip_wording_margin_top : 22;
    record_trans_offset : 16A;
}

#WCPaySelectVerifyTypeViewV2
{
    header_padding_leftRight : 32;
    button_marginBottom : 64;
}

#WCPayRealnameInfoViewV2
{
    button_marginBottom : 96;
}

#WCPayLQTChargeSetting {
    cell_leftRight_margin : 32;
    
    header_mainTitle_font : 22 dynamic;
    header_subTitle_font : 17 dynamic;
    header_mainTitle_topMargin : 80;
    header_sepLine_topMargin : 48;
    
    charge_time_font : 17 dynamic;
    cell_height : 64 dynamic;
}

#WCPayBalanceSaveView
{
    view_leftMargin : 32;
    
    card_view_topMargin : 32;
    card_view_title_font : 15;
    card_view_logo_leftMargin : 24;
    
    money_view_money_font : 56;
    money_view_top_margin : 16;
    money_view_textfield_height : 60;
    
    chargeEntry_font : 17;
    chargeEntry_subFont : 14;
    chargeEntry_margin : 18;
    chargeEntry_topMargin : 48;
}

#WCPayOverseaMainWalletGrayCell
{
    description_font_size: 16;
    extra_font_size: 13;
}

#WCPayCardDetailView
{
    bank_no_font : 32;
}

#WCPayAddressItem
{
    labelWidth : 210;
}


#verifyTouchLockView {
    icon_topMargin : 128;
}

#room_live
{
    unit_length: 8; /*_A*/
    startLive_BottomGradientLayerHeight: 40.75;
    startLive_ControlPanelViewLeftRightMargin: 1.5;
    startLive_ControlPanelViewButtonWidth: 6.5;
    startLive_ContentLeftRightMargin: 3;
    
    finder_circle_button_size_portrait: 5;
    finder_circle_button_size_landscape: 5;
    
    finder_bottom_action_button_distance_portrait: 1.5;
    finder_bottom_action_button_distance_landscape: 1.5;
    finder_comment_button_width_portrait: 14;
    finder_comment_button_width_landscape: 12;
    finder_anchor_assistant_comment_button_width_portrait: 14;
    finder_anchor_assistant_comment_button_width_landscape: 12;
    finder_pause_view_content_center_y_ratio: 0.29;
}

#WCRedEnvelopesStoryViewController {
    redskinitemview_width: 264;
    redskinitemview_height: 437;
}

#WCRedEnvelopesStoryViewController_896 {
    redskinitemview_width: 291;
    redskinitemview_height: 482;
    bottom_mask_height: 198.6;
}

#WCPayQRCoverPageView
{
    topbar_height : 57;
    topLeftIcon_marginLeft : 20;
    topLeftTitle_fontSize : 16;
}

#WCPayCoinView
{
    width : 120;
    coin_amount_text_size : 24;
}

#WCCoinMoneyInputView
{
    inputContent_cell_height : 255;
    btn_cell_height : 106;
    btn_with_ptotocol_cell_height : 160;
    
    inputContent_topbar_topMargin : 29;
    inputContent_cell_topMargin : 29;
    
    inputContent_leftRight_margin : 32;
    inputContent_title_fontSize : 14;
    inputContent_title_topMargin : 18;
    inputContent_uint_fontSize : 30;
    inputContent_money_fontSize : 56;
    inputContent_money_height : 60;
    inputContent_cardInfo_leftMargin : 15;
    
    btn_cell_checkbox_len : 15;
    btn_cell_protocol_topMargin : 30;
    btn_cell_btn_topMargin : 30;
    btn_cell_btn_topMargin_withProtocol : 15;
    btn_cell_btn_height : 47;
    
    inputContent_topbar_tips_fontSize : 15;
    
    redeemMarginTop : 24;
    redeemTypeHeight : 64;
}

#WCRedEnvelopesSelectSkinViewController_736 {
    Use_Cover_Button_Margin_Bottom : 5A;
}

#WCPayBalanceSelectCardView
{
    content_size : 390;
}

#WCRedEnvelopesSelectSkinViewController_896 {
    Use_Cover_Button_Margin_Bottom : 8A;
}

#WCRedEnvelopesSelectSkinViewController_926 {
    Use_Cover_Button_Margin_Bottom : 8A;
}

#RemarkPageSheet {
    bottomView_topMargin : 7A;
    bottomView_bottomMargin : 8A;
}
