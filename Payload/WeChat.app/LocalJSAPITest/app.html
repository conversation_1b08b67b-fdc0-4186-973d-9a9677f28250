<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta id="viewport" name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"/>
  <title>模板 JSAPI 测试</title>
  <link rel="stylesheet" type="text/css" href="https://res.wx.qq.com/open/libs/weui/2.4.0/weui.min.css"/>
  <style type="text/css">
    body {
      word-wrap: break-word;
    }
    .weui-form {
      padding-top: 24px;
    }
    .weui-form__title {
      font-weight: 500;
    }
    .weui-form__control-area {
      margin: 24px 0;
    }
    .weui-form__opr-area {
      display: flex;
      margin-bottom: 24px;
    }
    .weui-btn {
      margin: 0 12px !important;
    }
    .weui-textarea {
      font-size: 12px;
    }
    .error-panel {
      padding: 0 32px;
      color: var(--weui-RED);
    }
  </style>
</head>
<body ontouchstart>
<div class="weui-form">
  <div class="weui-form__text-area">
    <h2 class="weui-form__title">模板 JSAPI 测试</h2>
  </div>
  <div class="weui-form__control-area">
    <div class="weui-cells__group weui-cells__group_form">
      <div class="weui-cells__title">存档</div>
      <div class="weui-cells weui-cells_form">
        <div class="weui-cell weui-cell_active weui-cell_select">
          <div class="weui-cell__bd">
            <select class="weui-select" id="select"></select>
          </div>
        </div>
      </div>
    </div>
    <div class="weui-cells__group weui-cells__group_form">
      <div class="weui-cells__title">设置</div>
      <div class="weui-cells weui-cells_form">
        <div class="weui-cell weui-cell_active">
          <div class="weui-cell__hd"><label class="weui-label">名称</label></div>
          <div class="weui-cell__bd">
            <input id="apiName" class="weui-input" placeholder="如: getSearchData">
          </div>
        </div>
        <div class="weui-cell weui-cell_active">
          <div class="weui-cell__hd"><label class="weui-label">回调事件</label></div>
          <div class="weui-cell__bd">
            <input id="evtName" class="weui-input" placeholder="如: onSearchDataReady">
          </div>
        </div>
      </div>
    </div>
    <div class="weui-cells__group weui-cells__group_form">
      <div class="weui-cells__title">入参</div>
      <div class="weui-cells weui-cells_form">
        <div class="weui-cell ">
          <div class="weui-cell__bd">
            <textarea class="weui-textarea" placeholder="{}" rows="3" id="argsJson"></textarea>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="weui-form__opr-area">
    <a class="weui-btn weui-btn_default" href="javascript:" id="save">保存</a>
    <a class="weui-btn weui-btn_primary" href="javascript:" id="invoke">Invoke</a>
  </div>

  <div id="errorPanel" class="error-panel"></div>
  <div class="weui-form__control-area">
    <div class="weui-cells__group weui-cells__group_form">
      <div class="weui-cells__title">invoke回调数据</div>
      <div class="weui-cells weui-cells_form">
        <div class="weui-cell ">
          <div class="weui-cell__bd">
            <textarea class="weui-textarea" disabled rows="3" id="invokeResult"></textarea>
          </div>
        </div>
      </div>
    </div>
    <div class="weui-cells__group weui-cells__group_form">
      <div class="weui-cells__title">事件回调数据</div>
      <div class="weui-cells weui-cells_form">
        <div class="weui-cell ">
          <div class="weui-cell__bd">
            <textarea class="weui-textarea" disabled rows="10" id="result"></textarea>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<script type="text/javascript">
(function(){
  const SAVE = 'TEST_JSAPI';

  const $btnInvoke = document.getElementById("invoke");
  const $btnSave = document.getElementById("save");
  const $select = document.getElementById("select");
  const $apiName = document.getElementById("apiName");
  const $evtName = document.getElementById("evtName");
  const $argsJson = document.getElementById("argsJson");
  const $errorPanel = document.getElementById("errorPanel");
  const $invokeResult = document.getElementById("invokeResult");
  const $result = document.getElementById("result");

  const androidJSAPI = (function(){
    const key = Object.keys(window).find(key => /JSApi$/.test(key));
    return window[key]
  })();
  const saveData = localStorage.getItem(SAVE) ? JSON.parse(localStorage.getItem(SAVE)): {};

  function invoke(name, obj, callback) {
    if (androidJSAPI) {
      const res = androidJSAPI[name](JSON.stringify(obj || {}));

      if (callback) callback(res ? JSON.parse(res) : undefined);
    } else {
      window.WeixinJSBridge && window.WeixinJSBridge.invoke(name, obj, callback);
    }
  }
  function on(name, callback) {
    if (androidJSAPI) {
      window[name.replace(/:/g, '_')] = callback;
    } else {
      window.WeixinJSBridge && window.WeixinJSBridge.on(name, callback);
    }
  }

  function setSelect(name) {
    let optionsHtml = '';
    Object.keys(saveData).forEach(function(key) {
      optionsHtml += `<option value="${key}" ${key === name ? 'selected': ''}>${key}</option>`
    });
    $select.innerHTML = optionsHtml;
  }
  function onSelectInput() {
    const data = saveData[$select.value];
    $apiName.value = data.apiName;
    $evtName.value = data.evtName;
    $argsJson.value = data.argsJson;
  }
  
  window.onerror = function(msg, url, line) {
    $errorPanel.innerText = msg;
  };
  $btnInvoke.onclick = function(evt) {
    const apiName = $apiName.value;
    const evtName = $evtName.value;
    const args = eval(`(${$argsJson.value})`);

    $errorPanel.innerText = '';
    $result.value = '';
    $invokeResult.value = '';

    if (evtName) {
      on(evtName, function(resp) {
        $result.value = JSON.stringify(resp, null, 2);
      });
    }
    invoke(apiName, args, function(resp) {
      $invokeResult.value = JSON.stringify(resp, null, 2); 
    });
  };
  $btnSave.onclick = function(evt) {
    const apiName = $apiName.value; 
    saveData[apiName] = {
      apiName: apiName,
      evtName: $evtName.value,
      argsJson: $argsJson.value,
    };
    localStorage.setItem(SAVE, JSON.stringify(saveData));
    setSelect(apiName);
  };
  $select.oninput = onSelectInput;
  
  if (Object.keys(saveData).length) {
    setSelect();
    onSelectInput();
  }
})()
</script>
</body>
</html>