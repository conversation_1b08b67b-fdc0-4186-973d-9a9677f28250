<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="Content-Language" content="en" charset="utf-8">
	<meta charset="UTF-8">
	<meta id="viewport" name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1; user-scalable=no;" />
	<style> html,p,h1,ul,li{margin:0px;padding:0px;} ul{list-style-type:none;} body{color:#000;font:14px/1.5 微软雅黑,Helvetica,&quot;Helvetica Neue&quot;,&quot;segoe UI Light&quot;,&quot;Kozuka Gothic Pro&quot;;} h1{font-size:16px; padding-bottom:12px;margin-bottom:5px;} .articleTitle{margin-bottom:6px;} .sec_body { margin:25px 15px;} p{margin-bottom:6px;} .bold{font-weight:bold;} .article{margin-bottom:32px;} .sec_body .content { padding:10px;border-width:0px; } @media (prefers-color-scheme: dark) { body { background-color: #232323; color: rgba(255, 255, 255, .8); } } </style>
	<title>인터넷에 연결되지 않음</title>
</head>
<body class="sec_body">
	<div class="container">
		<h1>장치가 인터넷에 연결되어 있지 않습니다</h1>
		<div class="article">
			<p class="articleTitle">인터넷에 연결하려면 아래 방법을 시도하십시오:</p>
			<ul>
				<li>장치에서 &quot;<strong>설정</strong>&quot; - &quot;<strong>Wi-Fi</strong>&quot;로 이동하여 사용 가능한 Wi-Fi 네트워크에 연결합니다.</li>
				<li>장치에서 &quot;<strong>설정</strong>&quot; - &quot;<strong>셀룰러</strong>&quot;로 이동하여 &quot;<strong>셀룰러 데이터</strong>&quot;를 사용으로 설정합니다(데이터 사용에 대해 서비스 제공업체에서 비용 청구서를 받을 수 있음).</li>
			</ul>
		</div>
		<div class="article">
			<p class="articleTitle">장치를 Apple Watch와 페어링한 경우:</p>
			<ul>
				<li>&quot;<strong>Watch</strong>&quot; 앱 - &quot;<strong>셀룰러 데이터</strong>&quot; - &quot;<strong>WeChat</strong>&quot;을 열고 WeChat이 데이터에 액세스할 수 있도록 허용합니다.</li>
			</ul>
		</div>
		<div class="article">
			<p class="articleTitle">Wi-Fi 네트워크에 연결한 경우:</p>
			<ul>
				<li>Wi-Fi 핫스팟이 인터넷에 연결되었는지 또는 장치가 핫스팟에 액세스할 수 있는지 확인합니다.</li>
			</ul>
		</div>
	</div>
</body>
</html>

