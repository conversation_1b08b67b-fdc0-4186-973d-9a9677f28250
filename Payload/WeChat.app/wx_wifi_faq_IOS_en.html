<!DOCTYPE HTML>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>FAQ</title>
    <meta name="viewport" content=" initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no">
    <style>
        ul{
            list-style-type: none;
            margin: 0;
            padding: 0;
        }
        .ui-mt-medium{
            margin-top: 10px;
        }
        .ui-dis-no{
            display: none;
        }
        .mod-faq{
            min-height: 100%;
            color: #000;
            background-color: #ffffff;
            padding: 0 12px;
            font: 14px/1.5em "微软雅黑","Lucida Grande", "Lucida Sans Unicode", Helvetica, Arial, Verdana, sans-serif;
        }
        .mod-faq__header{
            padding: 41px 0 3px;
            font-weight: bold;
            font-size: 15px;
            border-bottom: 1px solid #b0bebe;
            margin: 0;
        }
        .mod-faq__content{
            padding: 26px 0;
        }
        .mod-faq__brief{
            margin-bottom: 26px;
        }
        .mod-faq__list-item{
            margin-bottom: 26px;
        }
        .mod-faq__authority{
            text-align: right;
            padding-bottom: 26px;
        }
        .mod-faq__quest-list{
            line-height: 18px;
        }
        .mod-faq__quest{
            font-weight: bold;
            margin-bottom: 6px;
        }
        .mod-faq__answer{
            position: relative;
            color: #5e5e5e;
            margin-left: 0;
            padding-left: 24px;
            margin-bottom: 26px;
        }
        .mod-faq__tail{
            position: absolute;
            left: 0;
            top: 0;
            font-style: normal;
        }
    </style>
</head>
<body>
<div class="mod-faq">
    <h1 class="mod-faq__header">Wi-Fi via WeChat</h1>
    <div class="mod-faq__content">
        <dl class="mod-faq__quest-list">
            <dt class="mod-faq__quest">Q：What is Wi-Fi via WeChat？</dt>
            <dd class="mod-faq__answer"><i class="mod-faq__tail">A：</i>Wi-Fi via WeChat is a convenient way to connect to the Internet via  WeChat and access Wi-Fi services provided by vendors and third-party network service providers.</dd>
            <dt class="mod-faq__quest">Q：Is Wi-Fi via WeChat secure？</dt>
            <dd class="mod-faq__answer">
                <i class="mod-faq__tail">A：</i>WeChat provides a convenient networking mode; however, be cautious when using public Wi-Fi. When you successfully connect to the Internet via WeChat, the connection status is displayed on the top of the WeChat session list. Otherwise, you may have connected to a pseudo wireless access point.
                <div class="ui-mt-medium">
                    <img src="ios-en_1.jpg" width="200">
                </div>
                <div>
                    Please don't:<br>
                    <ul>
                        <li>1) Open unknown links;</li>
                        <li>2) Download unknown applications;</li>
                        <li>3) Enter your password for WeChat or QQ accounts on untrusted websites;</li>
                        <li>4) Disclose your bank account information.</li>
                    </ul>
                </div>
            </dd>
            <dt class="mod-faq__quest">Q：How do I connect to the Internet via WeChat？</dt>
            <dd class="mod-faq__answer">
                <i class="mod-faq__tail">A：</i>iOS users should select a network on "Settings"-"Wi-Fi", and then scan the vendor's Wi-Fi QR code on "DISCOVER"-"Scan QR Code".
                <div>
                    <img src="ios7-en_2.jpg" class="ui-mt-medium ui-dis-no" width="200" id="img2">
                    <img src="ios6-en_2.jpg" class="ui-mt-medium ui-dis-no" width="200" id="ios6_img2">
                </div>
                <div><img src="en-2v.jpg" class="ui-mt-medium" width="200"></div>
            </dd>
            <dt class="mod-faq__quest">Q：How do I quit Wi-Fi via WeChat？</dt>
            <dd class="mod-faq__answer">
                <i class="mod-faq__tail">A：</i>Switch to other networks on "Settings"-"Wi-Fi" on your device or disable the Wi-Fi function.
                <div>
                    <img src="ios7-en_3.jpg" class="ui-mt-medium ui-dis-no" width="200" id="img3">
                    <img src="ios6-en_3.jpg" class="ui-mt-medium ui-dis-no" width="200" id="ios6_img3">
                </div>
            </dd>
            <dt class="mod-faq__quest">Q: What do I do if I cannot use Wi-Fi via WeChat to connect?</dt>
            <dd class="mod-faq__answer"><i class="mod-faq__tail">A：</i>This may be caused by a network problem. We suggest that you connect to the Internet again. If the problem persists, please consult on-site staff or call your ISP's customer service hotline.</dd>
        </dl>
    </div>
</div>

<script type="text/javascript">
    var version = Boolean(navigator.userAgent.match(/OS [7-9]_\d[_\d]* like Mac OS X/i));
    var img2 = document.getElementById('img2'),
            ios6_img2 = document.getElementById('ios6_img2'),
            img3 = document.getElementById('img3'),
            ios6_img3 = document.getElementById('ios6_img3');

    if((navigator.userAgent.match(/iPhone/i) || navigator.userAgent.match(/iPod/i))) {
       if(version){
            img2.style.display="block";
            img3.style.display="block";
        }else{
            ios6_img2.style.display="block";
            ios6_img3.style.display="block";
        }
    } else {
        ios6_img2.style.display="block";
        ios6_img3.style.display="block";
    }
</script>
</body>
</html>
