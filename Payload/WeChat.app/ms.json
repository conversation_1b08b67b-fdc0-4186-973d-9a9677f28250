{"BindCard_Gender_Female": "Perempuan", "BindCard_Gender_Male": "<PERSON><PERSON><PERSON>", "Choose_Deposit_Time": "Tetapkan Masa", "Choose_Payment": "<PERSON><PERSON><PERSON>", "Continue_Pay": "Lanjut<PERSON>", "Day": "{}", "Each_Day_In_Month_Deposit": "{} set<PERSON>p bulan", "Each_WeekDay_Deposit": "Setiap {}", "ExposureInfo_Waiting_Wording": "Sila tunggu...", "Fetch_Balance": "Keluarkan <PERSON>", "Fetch_Balance_Bank_Proccessing": "Bank sedang memproses", "Fetch_Balance_Open_Order": "<PERSON><PERSON><PERSON>", "Fetch_Balance_Success": "<PERSON><PERSON><PERSON><PERSON> tiba", "FillCard_Info_ErrorTips_Format": "{} ({} ralat semuanya)", "FillCard_Number_Default_Mobile_Modify_Tips": "Jika nombor mudah alih itu tidak betul, ketik untuk menyunting.", "FillCard_Number_Reg_Hint": "Nombor kad bank anda", "FillCard_Number_Unreg_Hint": "Nombor kad bank pemilik akaun ___<BRAND>___", "Friday": "Juma<PERSON>", "Give_Up": "<PERSON><PERSON><PERSON><PERSON>", "HHC_Check_PWD_To_Add_Plan": "<PERSON><PERSON><PERSON><PERSON> kata laluan pembayaran untuk memulakan <PERSON> & Jimat anda", "HHC_Check_PWD_To_Edit_Plan": "Ma<PERSON>kkan kata laluan pembayaran untuk mengubah suai Belanja & Jimat", "HHC_Check_PWD_To_Pause_Plan": "Ma<PERSON>kkan kata laluan pembayaran untuk menggantung Belanja & Jimat", "HHC_Check_PWD_To_Start_Plan": "<PERSON><PERSON>kkan kata laluan pembayaran untuk mendayakan Belanja & Jimat", "HHC_Choose_Payment": "<PERSON><PERSON><PERSON> kad", "HHC_Deposit_Plan": "<PERSON><PERSON><PERSON>", "HHC_Did_Modify": "<PERSON><PERSON><PERSON>", "HHC_Did_Open": "<PERSON><PERSON><PERSON><PERSON>", "HHC_Did_Pause": "Digantung", "HHC_Name": "Belanja & Jimat", "HHC_Plan_Check_Amount": "<PERSON><PERSON><PERSON> tidak sah. Semak dan cuba sekali lagi.", "HHC_Plan_Set_Bank_Card_Tip": "Untuk menggunakan Belanja & Jimat, pilih kad deposit terlebih dahulu.", "LQT_Fixed_Deposit": "<PERSON><PERSON><PERSON><PERSON>", "LQT_Fixed_Deposit_Check_PWD_To_Add_Plan": "Masukkan kata laluan pembayaran untuk mendayakan deposit berkala", "LQT_Fixed_Deposit_Check_PWD_To_Delete_Plan": "<PERSON><PERSON><PERSON>n kata laluan pembayaran untuk memadamkan jadual.", "LQT_Fixed_Deposit_Check_PWD_To_Pause_Plan": "Ma<PERSON>kkan kata laluan pembayaran untuk menggantung jadual", "LQT_Fixed_Deposit_Check_PWD_To_Start_Plan": "<PERSON><PERSON>kkan kata laluan pembayaran untuk mendayakan jadual.", "LQT_Fixed_Deposit_Did_Delete": "<PERSON><PERSON><PERSON><PERSON>", "LQT_Fixed_Deposit_Did_Modify": "Telah dikemas kini", "LQT_Fixed_Deposit_Did_Open": "<PERSON><PERSON><PERSON>", "LQT_Fixed_Deposit_Did_Pause": "Digantung", "LQT_Fixed_Deposit_No_Plan": "Tiada jadual deposit", "LQT_Fixed_Deposit_Plan": "<PERSON><PERSON><PERSON>", "LQT_Fixed_Deposit_Plan_Set_Bank_Card_Tip": "Untuk menetapkan deposit berkala, pilih kad untuk memotong dana terlebih dahulu.", "LQT_Fixed_Deposit_Plan_Set_Time_Tip": "Untuk menetapkan deposit berkala, pilih masa deposit terlebih dahulu.", "LQT_Fixed_Deposit_Plan_Should_Input": "Untuk menetapkan deposit berkala, masukkan jumlah deposit terlebih dahulu.", "ModifyPwdUseCase_ModifyPwd_Desc": "Ma<PERSON>kkan kata laluan pembayaran untuk mengesahkan identiti", "ModifyPwdUseCase_ModifyPwd_GiveUp_Title": "Batalkan perubahan kata laluan pembayaran anda?", "ModifyPwdUseCase_ModifyPwd_Success": "<PERSON>a la<PERSON>an telah di<PERSON>h", "ModifyPwdUseCase_ModifyPwd_Title": "<PERSON><PERSON>", "Monday": "<PERSON>in", "Monthly": "<PERSON><PERSON><PERSON> bulan", "OfflinePay_CreateOfflinePay_ConfirmBtn_Title": "<PERSON><PERSON><PERSON> ", "OfflinePay_CreateOfflinePay_Euro_Tips": "Bayar Pantas tidak didayakan. <PERSON><PERSON> didayakan, anda boleh menu<PERSON>n kod untuk membayar pembekal dengan segera. (Hanya menyokong transaksi CNY)", "OfflinePay_CreateOfflinePay_Tips": "Bayar Pantas tidak didayakan. <PERSON><PERSON> didayakan, anda boleh menu<PERSON>n kod untuk membayar pembekal dengan segera.", "OfflinePay_ReCreateOfflinePay_ConfirmBtn_Title": "<PERSON><PERSON><PERSON>", "OfflinePay_ReCreateOfflinePay_Euro_Tips": "Bayar Pantas tidak didayakan. Dayakannya untuk membayar pedagang dengan cepat dan mudah - cuma tunjukkan kod dan pergi. Hanya transaksi CNY disokong.", "OfflinePay_ReCreateOfflinePay_Tips": "Bayar Pantas tidak didayakan. Dayakannya untuk membayar pedagang dengan cepat dan mudah - cuma tunjukkan kod dan pergi.", "Saturday": "Sabtu", "Sunday": "<PERSON><PERSON>", "Thursday": "<PERSON><PERSON><PERSON>", "Tuesday": "<PERSON><PERSON><PERSON>", "WCPay_BankCardBindTo0_0_5D_Detail": "$ 0.05 akan dikenakan untuk mengesahkan akaun", "WCPay_BankCardBindTo0_0_5D_Detail_back": "$ 0.05 akan dikenakan untuk mengesahkan akaun dan dikembalikan selepas pengesahan.", "WCPay_BankCardBindTo1B_Detail": "¥ 0.01 akan dikenakan untuk mengesahkan akaun dan dikembalikan selepas pengesahan.", "WCPay_BankCardBindTo1B_NotReturnDetail": "¥ 0.01 akan dikenakan ke akaun anda untuk mengesahkan jika ia boleh digunakan.", "WCPay_CountryCode_Title": "Negara/Rantau", "WCPay_FaceID_Auth_Tip": "<PERSON><PERSON><PERSON><PERSON> muka anda untuk membayar", "WCPay_GiveUpReset_Title": "<PERSON><PERSON><PERSON><PERSON> men<PERSON> semula kata laluan pembayaran?", "WCPay_NeedChangeCard_Error_Btn": "<PERSON><PERSON> ka<PERSON>h p<PERSON>", "WCPay_TouchID_Auth_Tip": "Sahkan cap jari sedia ada untuk membayar", "WCPay_TouchID_Confirm_Alert_Cancel": "<PERSON><PERSON>", "WCPay_TouchID_Confirm_Alert_Content": "Cap jari disahkan. <PERSON>h<PERSON> bayaran?", "WCPay_TouchID_Confirm_Alert_OK": "<PERSON><PERSON><PERSON>", "WCPay_TouchID_Confirm_Alert_Title": "Bayar", "WeChatPay_Name": "___<BRAND_Pay>___", "Wednesday": "<PERSON><PERSON>", "Weekly": "<PERSON><PERSON><PERSON> minggu", "address_item_key": "<PERSON><PERSON><PERSON>", "address_item_place_holder": "<PERSON><PERSON><PERSON><PERSON>", "agree": "<PERSON><PERSON><PERSON>", "agree_user_protocal": "<PERSON><PERSON>", "agreement_alert": "<PERSON>la lihat dan be<PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON><PERSON>\" da<PERSON><PERSON>.", "alertChargeFee": "<PERSON><PERSON>", "area_item_key": "<PERSON><PERSON><PERSON>", "ask_verify_fingerprint": "Sahkan cap jari", "assign_pay_dialog_content": "Maklumat identiti yang dimasukkan berbeza berbanding dengan yang dimasukkan dengan akaun ___<BRAND>___ semasa anda. <PERSON>akan akaun ___<BRAND>___ yang dipautkan dengan kad bank anda dan sahkan semula.", "balance": "Baki", "bank_card_item_key": "Kad bank", "bank_select_item_key": "Bank", "bind_new_card": "Tambah kad bank", "bind_new_card_section_footer": "<PERSON>as sebab k<PERSON>n, kad yang dipautkan pada masa ini akan dinyah<PERSON>kan.", "bind_new_card_section_header": "Pautkan kad baharu untuk mendapatkan kembali akaun", "bind_new_card_to_pay_tip": "Ma<PERSON>kkan kata laluan pembayaran untuk mengesahkan identiti", "bind_new_card_to_reset_mobile_desc": "Pautkan nombor mudah alih dengan memautkan kad baharu.", "binded_card_list_page_title": "<PERSON><PERSON><PERSON> mudah alih untuk pengesahan SMS", "birth_date_item_key": "<PERSON><PERSON><PERSON>", "can_not_bind_more_card": "<PERSON><PERSON><PERSON><PERSON> kad yang dipautkan dicapai.", "can_not_get_sms_with_question_mard_word": "Kod pengesahan tidak diterima?", "can_not_get_sms_word": "<PERSON>d pengesahan tidak diterima", "cancel_time": "<PERSON>sa <PERSON>: {}", "cannot_receive_sms_code_content": "Kod SMS telah dihantar kepada mudah alih anda yang didaftar dengan bank anda. Pastikan anda kini menggunakan mudah alih ini dan kod SMS tidak disekat oleh sebarang aplikasi keselamatan. Jika anda tidak menggunakan nombor ini lagi, hubungi bank anda. Untuk lebih bantuan, panggil ___<OfficialEntity_Service>___ di +86-0755-95017.", "cannot_receive_sms_code_title": "Tidak Dapat Menerima <PERSON>", "card_holder_dialog_content": "1. Untuk memastikan keselamatan dana, ID ___<BRAND>___ hanya akan memautkan kad bank yang mempunyai nama yang sama.\n\n2. Untuk memautkan kad atas nama yang berbeza, anda hendaklah mengemas kini maklumat nama sebenar anda.\n\n3. Selepas mengubah maklumat nama sebenar, maklumat pemegang kad lama akan dipadamkan dan anda hanya akan dapat menambah kad atas nama pemegang kad baharu.", "card_holder_dialog_title": "Nama pada kad", "card_holder_item_key": "<PERSON><PERSON>egang kad", "card_holder_section_header": "Masukkan maklumat yang anda berikan kepada bank anda sebelum ini. Hanya kad bank yang dipegang atas nama ini boleh ditambah pada masa hadapan.", "card_info_section_header": "<PERSON><PERSON><PERSON><PERSON> mak<PERSON>at kad", "card_num_item_key": "Nombor kad", "card_number_input_tips_title": "Tidak diperlukan bayaran atau perbankan atas talian", "card_select_item_key": "<PERSON><PERSON>", "card_type_section_header": "<PERSON><PERSON><PERSON> jeni<PERSON> kad", "change_realname_word": "<PERSON><PERSON>", "change_to_face_id": "<PERSON><PERSON><PERSON><PERSON>", "change_to_pwd": "<PERSON><PERSON>", "change_to_touch_id": "Gunakan <PERSON>", "check_pay_pwd_page_desc": "<PERSON><PERSON>kkan kata laluan pembayaran anda untuk mengesahkan identiti anda", "check_pay_pwd_page_title": "Pengesahan Identiti", "check_sms_desc": "<PERSON>d pengesahan telah dihantar ke nombor mudah alih yang anda daftarkan pada bank anda.\n\n1. <PERSON><PERSON><PERSON> bahawa ini merupakan nombor mudah alih semasa anda yang didaftarkan pada bank anda.\n\n2. <PERSON>mak sama ada SMS telah disekat oleh aplikasi keselamatan pada mudah alih anda.\n\n3. <PERSON><PERSON> anda tidak boleh mengakses nombor ini sekarang, sila hubungi bank anda.\n\n4. <PERSON><PERSON><PERSON> ban<PERSON><PERSON> tambahan, hubungi Khidmat Pelanggan di 95017.", "check_sms_page_desc": "Memautkan kad bank memerlukan pengesahan SMS. Kod pengesahan dihantar kepada telefon {}. <PERSON><PERSON><PERSON> a<PERSON>an.", "check_sms_page_favor": "<PERSON><PERSON> akan membayar {}{:.2f} ({}{:.2f} dijima<PERSON><PERSON>)", "check_sms_page_title": "Sahkan nombor mudah alih", "common_back": "Kembali", "common_cancel_word": "<PERSON><PERSON>", "common_close": "<PERSON><PERSON><PERSON>", "common_done_word": "Se<PERSON><PERSON>", "common_drop_out_word": "<PERSON><PERSON><PERSON><PERSON>", "common_i_know_word": "<PERSON><PERSON><PERSON>", "common_more": "<PERSON><PERSON>", "common_next_word": "Seterusnya", "common_question_word": "Soalan Lazim", "common_select": "<PERSON><PERSON><PERSON>", "common_tip_word": "Peringatan", "confirm_mobile_no": "<PERSON><PERSON><PERSON>", "confirm_pay": "<PERSON><PERSON>", "confirm_to_receive": "<PERSON><PERSON><PERSON>", "confrim_pay_and_open_deduct_word": "Bayar & Dayakan", "confrim_pay_word": "Bayar", "coupon_change_should_change_payment": "<PERSON><PERSON><PERSON> bayaran telah berubah. <PERSON>la pilih semula.", "coupon_component_need_bank_pay_tips": "Diskaun untuk Ka<PERSON> Ditetapkan", "cre_id_item_key": "Nombor ID", "cre_id_item_place_holder": "Masukkan nombor ID", "cre_type_item_key": "<PERSON><PERSON>", "cre_type_item_place_holder": "<PERSON><PERSON><PERSON> jeni<PERSON>", "cvv_dialog_content": "CVV ialah kod keselamatan 3 atau 4 digit sama ada pada belakang atau hadapan kad anda.", "cvv_dialog_title": "Apakah CVV kad?", "cvv_item_key": "CVV ", "cvv_item_place_holder": "3 atau 4 digit <PERSON><PERSON><PERSON> di belakang kad", "default_delay_transfer_confirm_desc": "<PERSON><PERSON><PERSON> akan tiba dalam {} jam", "email_item_key": "<PERSON><PERSON>t e-mel", "email_item_place_holder": "Masukkan e-mel", "error_detail_title": "<PERSON><PERSON><PERSON>", "face_hongbao": "<PERSON><PERSON>", "fast_bind_card_support_bank_title_text": "Pada masa ini menyokong bank berikut", "fill_card_and_user_info_title": "Masukkan kad bank dan maklumat identiti", "fill_card_info_card_holder_assign_pay_header": "<PERSON><PERSON> pemegang kad yang ditetapkan yang boleh membayar.", "fill_card_info_page_favor_desc": "Gunakan kad bank jenis ini untuk menjimat {}{:.2f} tambahan.", "fill_card_info_page_realname_cre_not_support": "Tidak boleh menggunakan {} untuk memautkan kad ini", "fill_card_info_page_title": "<PERSON><PERSON><PERSON><PERSON> mak<PERSON>at kad", "fill_card_num_format_error": "Nombor kad tidak sah", "fill_card_num_of_card_holder_section_header": "Masukkan nombor kad bank pemegang kad", "fill_card_num_page_desc": "Pautkan kad bank", "fill_card_num_page_favor_dialog_title": "Jimat Lebih Banyak Dengan Kad Ini", "fill_card_num_page_realname_desc": "Anda perlu menambahkan kad bank anda untuk melengkapkan pengesahan nama sebenar.", "fill_card_num_page_sns_input_hint": "Hanya menyokong kad debit", "fill_card_num_page_title": "Tambah kad bank", "fill_card_number_assign_pay": "Gunakan kad bank {} untuk membayar", "fill_card_number_more_favor": "Gunakan kad bank yang ditetapkan untuk menikmati diskaun", "fill_complete_name": "<PERSON><PERSON><PERSON><PERSON> nama penuh", "fill_id_format_error": "Format nombor ID tidak betul.", "fill_in_sms_key": "<PERSON><PERSON>", "fill_in_sms_word": "<PERSON>sukka<PERSON> kod pengesahan", "fill_phone_num_format_error": "Format nombor mudah alih tidak betul.", "finger_print_err_tips": "Cuba semula", "first_name_item_key": "<PERSON>a pertama", "first_name_item_place_holder": "<PERSON><PERSON><PERSON><PERSON> nama anda", "float_paydesk_modal_no_select_favor": "Diskaun Tidak Digunakan", "foreign_mobile_header": "<PERSON><PERSON><PERSON><PERSON> mudah alih baharu", "forget_pay_pwd_title": "Terlupa Kat<PERSON>", "get_sms_with_count_down_word": "Dapatkan kod pengesahan \n({})", "get_sms_word": "Dapatkan kod pengesahan", "give_up_on_new_card": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>kan kad?", "give_up_this_order_or_not": "<PERSON><PERSON>ikan transaksi ini?", "group_aa": "Bahagikan Bil", "has_send_sms": "<PERSON><PERSON><PERSON>", "has_send_sms_with_count": "({}) di<PERSON>tar", "hongbao_refund_way_header_title": "Paket Me<PERSON> yang tidak dibuka dalam 24 jam selepas dihantar akan dibayar balik dalam kaedah di bawah.", "hongbao_refund_way_title": "Bayar Balik Paket Merah Kepada", "id_card_name": "Kad ID", "install_cert_error": "<PERSON><PERSON> memasang sijil", "last_name_item_key": "<PERSON><PERSON> k<PERSON>", "last_name_item_place_holder": "<PERSON><PERSON><PERSON><PERSON> nama keluarga", "loading_title": "Memuatkan...", "lottery_network_error": "<PERSON><PERSON>, semoga berjaya pada lain kali!", "lqt": "Dana <PERSON>", "lqt_reset_mobile_desc": "Pilih kad bank. Sahkan Dana Mini anda menggunakan nombor mudah alih anda yang dipegang oleh bank kad itu.", "mobile_dialog_content": "Nombor mudah alih yang didaftarkan dalam akaun bank anda ialah nombor yang anda berikan apabila anda membuka akaun tersebut. Jika anda tidak memberikan nombor mudah alih kepada bank, anda terlupa nombor tersebut atau ia tidak lagi boleh diakses, hubungi bank anda dan kemas kini nombor mudah alih.", "mobile_dialog_title": "Nombor mudah alih", "mobile_item_key": "Nombor mudah alih", "mobile_item_place_holder": "Masukkan nombor mudah alih yang dipegang oleh bank anda", "name_item_key": "<PERSON><PERSON>", "name_item_place_holder": "<PERSON><PERSON><PERSON>n nama pada kad", "nationality_item_key": "Negara/Rantau", "nationality_place_holder": "Masukkan negara/rantau", "new_mobile_item_key": "Nombor mudah alih baharu", "new_mobile_item_place_holder": "Masukkan nombor mudah alih yang dipegang oleh bank anda", "new_user_card_num_input_safety_desc": "<PERSON><PERSON><PERSON> kad yang dipunyai oleh pemilik akaun ___<BRAND>___", "new_user_card_num_input_safety_desc_v2": "<PERSON><PERSON><PERSON> kad yang dipunyai oleh pemilik akaun ___<BRAND>___", "no": "Tidak", "offline_choose_payment": "<PERSON><PERSON><PERSON>", "offline_choose_payment_fail": "<PERSON><PERSON> kaedah pembayaran lalai anda tidak berjay<PERSON>, kaedah lain akan dicuba untuk menyelesaikan pembayaran.", "offline_click_view_code": "Ketik untuk melihat nombor kod pembayaran", "offline_pay_modify_limit": "<PERSON><PERSON>", "offline_pay_only_pay_title": "Bayar", "offline_pay_select_card_invalid": "Tidak boleh menggunakan kaedah pembayaran yang terpilih pada masa ini. Cuba kaedah pembayaran yang lain.", "offline_pay_title": "<PERSON>", "offline_pay_to_merchant": "Bayar Pedagang", "offline_prefer_payment": "<PERSON><PERSON><PERSON> p<PERSON> lalai", "offline_show_code_warning": "<PERSON>ya menggunakan nombor kod pembayaran untuk ditunjukkan kepada juruwang semasa membayar. <PERSON><PERSON> kong<PERSON> ia dengan orang lain untuk keselamatan anda.", "offline_view_code_warning": "<PERSON>ya menggunakan nombor kod pembayaran untuk ditunjukkan kepada juruwang semasa membayar. <PERSON><PERSON> kong<PERSON> ia dengan orang lain untuk keselamatan anda.", "ok": "OK", "order_address_section_header": "<PERSON><PERSON><PERSON>", "pay_card_detail_contact_user_info": "Hubu<PERSON><PERSON> K<PERSON>d<PERSON>: ", "pay_card_detail_tel_number": "95017", "pay_power_by_tenpay_word": "Disediakan o<PERSON>", "pay_success": "<PERSON><PERSON><PERSON><PERSON> berjaya", "paydesk_coupon_page_title": "<PERSON><PERSON>", "paydesk_float_page_title": "<PERSON><PERSON>", "paydesk_main_page_more_favor": "<PERSON><PERSON> disk<PERSON>n", "paydesk_main_page_title": "<PERSON><PERSON>", "paydesk_payment_page_title": "<PERSON><PERSON><PERSON>", "paydesk_sub_page_title": "<PERSON><PERSON><PERSON>", "payee_remark_title": "Catatan Pembayar", "paying_alert_tips": "Bayaran sudah diserahkan. Tunggu mesej keputusan pembayaran untuk menyemak sama ada anda perlu menyerahkan semula bayaran.", "payment_method": "<PERSON><PERSON><PERSON>", "phone_number_item_key": "Telefon", "phone_number_item_place_holder": "Masukkan nombor telefon", "profession_item_key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pure_bind_card_succ_tips": "<PERSON><PERSON><PERSON><PERSON>", "pwd_repeat_error_tip": "<PERSON>a la<PERSON>an tidak sepadan", "rebind_bank_card_section_header": "Pautkan kad bank semula untuk mendapatkan kembali akaun anda", "receipt": "<PERSON><PERSON>", "receive_done": "Diterima", "receive_remark_title": "Catatan Resit", "receive_time": "<PERSON><PERSON>: {}", "receiver_remark_title": "Catatan Penerima", "refund_doing_tip": "Memproses bayaran balik. <PERSON>aun akan dibayar balik kepada kad anda dalam 1-3 hari per<PERSON>an.", "refund_done": "<PERSON><PERSON><PERSON> balik", "refund_done_and_expired": "<PERSON><PERSON><PERSON><PERSON><PERSON> (tamat tempoh)", "refund_time": "<PERSON><PERSON>: {}", "refund_transfer_or_not": "<PERSON><PERSON><PERSON><PERSON> pindahan daripada {}?", "refund_word": "<PERSON><PERSON><PERSON><PERSON>", "renewal_time_item_key": "<PERSON><PERSON>", "resend_message_or_not": "<PERSON><PERSON> semula mesej ini?", "resend_sms": "<PERSON><PERSON> semula", "resend_sms_with_count": "<PERSON><PERSON> semula ({})", "resend_success_tip": "<PERSON><PERSON><PERSON> semula", "resend_word": "<PERSON><PERSON> semula", "reset_ban_mobile_fill_card_info_credit_tip_header": "Masukkan maklumat kad bank untuk semakan", "reset_ban_mobile_fill_card_num_tip_header": "Tambah kad bank baharu dan gunakan nombor mudah alih yang dipegang oleh bank anda untuk mengesahkan pembayaran Baki.", "reset_cvv_and_valid_date_tip": "Anda sedang mengemas kini maklumat kad terpaut dan membuat bayaran pada masa yang sama. Jika anda tidak pasti akan apa-apa, telefon khidmat pelanggan bank anda: ", "reset_cvv_title": "Tukar CVV", "reset_lqt_mobile_fill_card_num_tip_header": "Tambah kad baharu dan gunakan nombor mudah alih berdaftar kad tersebut untuk melengkapkan pengesahan SMS untuk Dana Mini.", "reset_mobile_bank_card_number": "Kad", "reset_mobile_card_desc_format": "{}{} ({}) Berdaftar mudah alih", "reset_mobile_card_desc_with_update_format": "{}{} ({}) Berdaftar mudah alih. ", "reset_mobile_new_mobile_info_btn": "<PERSON><PERSON><PERSON>", "reset_mobile_new_mobile_number": "Nombor Baharu", "reset_mobile_phone_page_title": "Edit nombor mudah alih", "reset_phone_tip": "Anda boleh bayar setelah identiti disahkan. Untuk mengesahkan nombor telefon yang dipegang oleh bank anda, telefon ", "reset_pwd_fill_rebind_card_info_page_title": "Masukkan maklumat kad bank", "reward": "<PERSON><PERSON>", "safety_dialog_content": "Langkah keselamatan: <PERSON><PERSON><PERSON><PERSON> akaun, p<PERSON><PERSON><PERSON> masa sebenar, pem<PERSON><PERSON> kecema<PERSON>. \n\nPengesahan dua langkah: Kata laluan pembayaran anda diperlukan untuk setiap pembayaran. Pengesahan SMS diperlukan untuk pembayaran besar. \n\nPerlindungan privasi: Penyulitan data yang kukuh digunakan untuk melindungi data pengguna. \n\nInsurans pembayaran: Pembayaran diinsuranskan oleh PICC.", "safety_dialog_title": "Perlindungan keselamatan", "scan_card_num_title": "<PERSON><PERSON><PERSON>", "select_payment": "<PERSON><PERSON><PERSON> ka<PERSON>h pem<PERSON>", "select_payment_card": "<PERSON><PERSON><PERSON>", "send_verify_code_btn_wording": "Hantar", "send_verify_code_switch_btn_wording": "<PERSON><PERSON> ka<PERSON>h pen<PERSON>", "send_verify_code_tips_format": "<PERSON><PERSON> pen<PERSON>ahan SMS akan dihantar ke: \n{}", "set_pay_pwd_confirm_page_title": "<PERSON><PERSON><PERSON><PERSON> semula untuk menges<PERSON>kan", "set_pay_pwd_page_desc": "Tetapkan kata laluan ___<BRAND_Pay>___ untuk mengesahkan pembayaran anda", "set_pay_pwd_page_title": "Tetapkan Kata Lal<PERSON>", "set_pwd_success": "Tetapan dikemas kini", "succ_page_open_biometric_cancel_btn_title": "<PERSON><PERSON><PERSON>", "succ_page_open_biometric_dialog_content": "<PERSON><PERSON> boleh mendayakan pembayaran muka atau cap jari untuk membuat pembayaran dengan lebih cepat.", "succ_page_open_biometric_faceid_btn_title": "<PERSON><PERSON><PERSON><PERSON>", "succ_page_open_biometric_touchid_btn_title": "Pembayaran Cap Jari", "succ_page_open_face_id_dialog_content": "Dayakan P<PERSON>aran Muka untuk menggunakan pengecaman muka bagi melengkapkan pembayaran dengan cepat dan selamat.", "succ_page_open_face_id_right_btn_title": "<PERSON><PERSON><PERSON>", "succ_page_open_touch_id_dialog_content": "Dayakan Pembayaran Sentuh untuk menggunakan pengecaman cap jari bagi melengkapkan pembayaran dengan cepat dan selamat.", "succ_page_open_touch_id_left_btn_title": "<PERSON><PERSON><PERSON>", "succ_page_open_touch_id_right_btn_title": "<PERSON><PERSON><PERSON>", "to_be_confirm_receive": "Resit tidak disa<PERSON>kan", "transfer": "Pindah", "transfer_account": "<PERSON><PERSON><PERSON>", "transfer_amount_input_invalid_hint": "Amaun yang dimasukkan tidak betul", "transfer_bank": "Pindah ke Kad Bank", "transfer_explain": "<PERSON><PERSON> nota pindahan", "transfer_modify_explain": "<PERSON><PERSON>", "transfer_second_left_button": "<PERSON><PERSON>", "transfer_second_right_button": "Teruskan", "transfer_second_title": "<PERSON><PERSON><PERSON> pin<PERSON>an", "transfer_time": "<PERSON><PERSON> pin<PERSON>: {}", "transfer_ui_title": "Pindahkan kepada Rakan", "understand_safety": "Perlindungan keselamatan", "update_word": "<PERSON><PERSON> kini", "user_card_type_select_placeholder_v2": "Pilih kad bank dan jenis kad", "user_info_section_header": "Ma<PERSON>kkan maklumat peribadi", "user_protocal": "\"<PERSON><PERSON><PERSON><PERSON>\"", "valid_date_item_key": "Tamat Tempoh", "verify_cre_tip": "Masukkan 4 digit terakhir {} {} untuk mengesahkan identiti", "verify_fingerprint_fail": "Pengesahan cap jari gagal", "verify_id_ui_true_name_tips": "{} (<PERSON><PERSON><PERSON><PERSON> nama penuh)", "wechat_bank_agreement": "Perjanjian Bank", "wechat_mobile_phone_word": "<PERSON><PERSON> alih yang dipautkan dengan ___<BRAND>___", "wechat_user_agreement": "<PERSON><PERSON><PERSON><PERSON> ___<BRAND_Pay>___", "wxp_common_cancel": "<PERSON><PERSON>", "wxp_common_confirm": "OK", "wxp_common_i_know": "<PERSON><PERSON><PERSON>", "wxp_common_remind": "Peringatan", "wxp_network_error": "Sistem sibuk. Cuba lagi kemudian.", "wxp_payment_network_error": "Transaksi diserahkan. <PERSON>a akan menerima pemberitahuan status pembayaran daripada A<PERSON>un Rasmi ___<BRAND_Pay>___. Sahkan status pembayaran sebelum menyerahkan semula pembayaran jika perlu.", "wxp_system_error": "Sistem sibuk. Cuba lagi kemudian.", "wxp_wcpay_system_error": "Sistem sibuk. Cuba lagi kemudian.", "yes": "Ya", "zip_item_key": "Poskod", "zip_item_place_holder": "Masukkan poskod", "common_confirm_word": "<PERSON><PERSON><PERSON>", "ModifyPwdUseCase_ModifyPwd_GiveUp_Yes": "<PERSON><PERSON>", "ModifyPwdUseCase_ModifyPwd_GiveUp_No": "Teruskan", "disagree": "Tidak Bersetuju", "card_user_agreement": "<PERSON><PERSON><PERSON><PERSON>", "card_bank_agreement": "Perjanjian Bank", "Card_UserAgreement_Title": "<PERSON>a henda<PERSON>h bersetuju dengan perjan<PERSON>an di bawah untuk menambah kad bank.", "pay_settings_delay_transfer_page_title": "<PERSON><PERSON>", "pay_settings_delay_transfer_page_desc": "<PERSON><PERSON><PERSON>, dana itu akan didepositkan ke Baki pengguna lain selewat-lewatnya pada masa berikut. Pindahan tidak boleh dipanggil balik selepas dihantar, o<PERSON><PERSON> itu, sila periksa maklumat penerima dengan teliti sebelum membuat pemindahan.", "pay_settings_delay_transfer_no_delay": "<PERSON><PERSON> segera", "pay_settings_delay_transfer_two_hour": "<PERSON><PERSON> masa 2 jam", "pay_settings_delay_transfer_one_day": "<PERSON><PERSON> masa 24 jam", "pay_settings_biometric_pay_enabled": "<PERSON><PERSON><PERSON>", "pay_settings_biometric_pay_disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pay_settings_biometric_pay_multi_support_title": "Pembayaran Mu<PERSON>/Cap Jari", "pay_settings_biometric_pay_faceid_enabled": "<PERSON><PERSON><PERSON><PERSON> muka did<PERSON>kan", "pay_settings_biometric_pay_touchid_enabled": "Pembayaran cap jari did<PERSON>kan", "pay_settings_biometric_pay_multi_support_desc": "<PERSON><PERSON><PERSON><PERSON>, anda boleh menggunakan pengesahan muka atau cap jari untuk membuat pembayaran dengan lebih cepat.", "f2f_pay_extrabuy_detail_modal_original_price": "(Harga asal ¥{:.2f})", "common_button": "<PERSON><PERSON>", "Accessibility_Type_SwitchView_Selected": "{}, bertukar butang, dayakan", "Accessibility_Type_SwitchView_UnSelected": "{}, bert<PERSON><PERSON> but<PERSON>, n<PERSON><PERSON><PERSON>n", "YunShanFu_Loading_Wording": "Sedang Membuka QuickPass...", "YunShanFu_Uninstalled_Error": "Anda belum memasang Quick<PERSON>ass. Sila pasang terlebih dahulu dan cuba lagi, atau teruskan pembayaran dengan ___<BRAND_Pay>___.", "OfflinePay_Setting_CloseOfflinePay_AlertMessage_ShowFeaturePassword": "<PERSON><PERSON><PERSON><PERSON> kunci keselamatan didayakan, kaedah buka kunci yang dipilih diperlukan untuk mengakses Bayar Pantas dan membantu untuk memastikan pembayaran anda selamat. Nyahdayakan?", "OfflinePay_Setting_CloseOfflinePay_AlertMessage": "Nyahdayakan Bayar Pantas?", "OfflinePay_Setting_CloseOfflinePay_BtnTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "OfflinePay_Setting_CloseOfflinePay_Cancel": "Teruskan", "OfflinePay_Setting_CloseOfflinePay_OpenWalletLock": "Tetapkan Kunci <PERSON>", "Wallet_Mix_Paid_UnKnown_Error": "Permintaan transaksi diserahkan. <PERSON>a akan menerima pemberitahuan status daripada Akaun Rasmi ___<BRAND_Pay>___ Hong Kong. <PERSON>an buat bayaran lagi sehingga status pembayaran telah disahkan.", "bank_card_info": "<PERSON>a", "HHC_Did_Add": "Ditambah", "VoiceOver_OfflinePay_barCode": "<PERSON><PERSON>, boleh ditun<PERSON>kkan kepada juruwang. Ketik dua kali untuk menun<PERSON>kkan kod bayaran skrin penuh.", "VoiceOver_OfflinePay_barCode_short": "<PERSON><PERSON>", "VoiceOver_OfflinePay_Qrcode": "Kod QR Bayaran", "VoiceOver_OfflinePay_barcode_clickHint": "Ketik dua kali untuk kembali", "VoiceOver_OfflinePay_Qrcode_clickHint": "Ketik dua kali untuk menu<PERSON>n dalam skrin penuh", "common_link": "<PERSON><PERSON><PERSON>", "Accessibility_Collapse_Header_Collapsed": "{}, dirunt<PERSON><PERSON>", "Accessibility_Collapse_Header_Showed": "{}, dikembangkan", "Pay_Android_Fingerprint_Prompt_Title": "Sahkan cap jari", "Pay_Android_Fingerprint_Prompt_SubTitle": "untuk melengkapkan bayaran.", "Pay_Android_Fingerprint_Prompt_Button": "<PERSON><PERSON><PERSON>", "Accessibility_Collapse_Header_Collapsed({}": "Sembunyikan", "Accessibility_Collapse_Header_Showed({}": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Accessibility_State_Disabled": "<PERSON><PERSON><PERSON> gelap", "Accessibility_String_PwdInputView": "Password box, 6 digits in total, {} digits entered", "Accessibility_Type_Button": "{}, button", "Accessibility_Type_CheckBox_Selected": "Check box, selected, {}, button", "Accessibility_Type_CheckBox_UnSelected": "Check box, unselected, {}, button", "Accessibility_Type_Selected": "Selected, {}, button", "Accessibility_Type_UnSelected": "Unselected, {}, button", "common_check_box_check": "Check box, selected", "common_check_box_uncheck": "Check box, unselected", "OfflinePay_ChangePayment_UnConnectedNetwork_Tips": "Rangkaian tidak tersedia. Tidak dapat memilih kaedah pembayaran.", "OfflinePay_EnablePage_UnConnectedNetwork_Tips": "Rangkaian tidak tersedia. Cuba lagi kemudian.", "Fetch_Balance_To_Bank": "Dikeluarkan ke", "Fetch_Balance_Amount": "<PERSON><PERSON><PERSON>", "Fetch_Balance_Amount_Tips": "Baki: ¥{}.", "Fetch_Balance_Amount_Exceed": "<PERSON><PERSON><PERSON> yang dimasukkan melebihi Baki yang ada", "Fetch_Balance_Fetch_All": "<PERSON><PERSON><PERSON><PERSON>", "HoneyPay_CheckPwd_Unbind_Title": "Nyahpaut Ka<PERSON>", "HoneyPay_Modify_CreditLimit_Title": "Edit had b<PERSON>nan", "HoneyPay_Modify_CreditLimit_Desc": "<PERSON>", "HoneyPay_Modify_CreditLimit_Max_Alert": "<PERSON><PERSON><PERSON> tidak boleh melebihi ¥{:.2f}", "balance_entry_balnce_title": "<PERSON><PERSON>", "balance_entry_balnce_detail": "Transaksi", "balance_entry_powered_by_tenpay": "Disediakan o<PERSON>", "balance_recharge_page_title": "Tambah Nilai", "balance_recharge_card_info_title": "<PERSON><PERSON><PERSON>", "balance_recharge_payment_new_card": "Tambah Kad Baharu", "HoneyPay_Add_Card": "<PERSON><PERSON><PERSON>", "HoneyPay_Select_Contact_Title": "<PERSON><PERSON><PERSON>", "HoneyPay_Modify_Comment": "<PERSON>", "HoneyPay_MoneyInput_Hint": "<PERSON><PERSON><PERSON><PERSON> jum<PERSON>", "HoneyPay_CreateCard_Btn": "Hantar", "HoneyPay_Max_Amount_Notice": "<PERSON><PERSON><PERSON> tidak boleh melebihi ¥{:.2f}", "HoneyPay_Modify_Credit": "<PERSON>", "HoneyPay_Main_Title": "<PERSON><PERSON>", "hbrefund_info_tips": "Catatan", "hbrefund_set_button": "Tetapan", "hbrefund_time_title": "<PERSON><PERSON>ar Balik Paket Merah", "hbrefund_forbid_way": "<PERSON><PERSON><PERSON> bayaran balik ini tidak lagi disokong.", "hbrefund_had_set": "<PERSON><PERSON><PERSON><PERSON>", "hbrefund_origin_desc": "Paket Me<PERSON> yang tidak dibuka dalam masa 24 jam selepas dihantar akan dibayar balik melalui kaedah pembayaran asal.", "hbrefund_set_tips": "Se<PERSON><PERSON> dite<PERSON><PERSON><PERSON>, wang yang tidak diterima akan dibayar balik kepada kaedah pembayaran asal. Ini tidak boleh ditukar kepada \"Bayar Balik kepada Baki\". Teruskan?", "TeenagerPayDetailUIPage_NotSet": "Tiada", "TeenagerPayDetailUIPage_LimitedAmount": "¥{:.{}f}", "TeenagerPaySetLimitModal_AmountTitle": "<PERSON><PERSON><PERSON>", "TeenagerPaySetLimitModal_MaxAmount": "Sehingga 7 digit dibenarkan", "TeenagerPayGetDetailUseCase_LimitOn": "<PERSON> be<PERSON><PERSON><PERSON> di<PERSON>", "TeenagerPayGetDetailUseCase_LimitOff": "<PERSON><PERSON><PERSON> had", "TeenagerPayUseCase_Set_Ok": "<PERSON><PERSON><PERSON><PERSON>", "TeenagerPayUseCase_Close_Ok": "<PERSON> j<PERSON><PERSON>", "TeenagerPayUseCase_Limit_Max": "Had bayaran bagi setiap transaksi tidak boleh mele<PERSON>hi had bayaran harian.", "TeenagerPayUseCase_Limit_Min": "Had bayaran harian tidak boleh kurang daripada had bayaran bagi setiap transaksi.", "Dcep_Loading_Wording": "Memuatkan...", "Dcep_Uninstalled_Error": "Anda belum memasang E-CNY. Sila pasang terlebih dahulu dan cuba lagi, atau teruskan pembayaran dengan ___<BRAND_Pay>___.", "TeenagerPayUseCase_Input_Accesibility": "Palang teks", "bankcard_detail": "{} Nombor siri {}", "bankcard_qmf_detail": "{} <PERSON><PERSON><PERSON>, {}", "FaceCheck_Agreement_title": "<PERSON><PERSON><PERSON>", "FaceCheck_Success_title": "<PERSON><PERSON><PERSON><PERSON>", "FaceCheck_Result_Retry": "Cuba sekali lagi", "TabBar_NewBadge": "<PERSON><PERSON><PERSON>", "common_delete_alert_title": "<PERSON><PERSON><PERSON> pemadaman?", "common_delete": "Padam", "transfer_to_bank_name_input_placeholder": "<PERSON><PERSON>", "transfer_to_bank_card_input_placeholder": "No. Kad Bank Penerima Bayaran", "transfer_to_bank_bank_select_placeholder": "Pilih bank", "transfer_to_bank_arrival_time_select_title": "<PERSON><PERSON>", "transfer_to_bank_arrival_time_modal_title": "<PERSON><PERSON><PERSON>", "transfer_to_bank_arrival_time_modal_desc": "<PERSON><PERSON><PERSON> anda meminta pindahan wang, dana akan didepositkan dalam akaun penerima bayaran pada masa berikut.", "transfer_to_bank_history_page_title": "<PERSON><PERSON><PERSON>", "transfer_to_bank_history_page_empty_prompt": "Tiada penerima bayaran sebelum ini", "transfer_to_bank_history_me_section_title": "<PERSON><PERSON>", "transfer_to_bank_history_others_section_title": "Penerima <PERSON>i", "transfer_to_bank_history_modify_remark_action": "<PERSON>a", "transfer_to_bank_history_set_remark_title": "Tambah Nota", "transfer_to_bank_history_delete_action": "Padam", "transfer_to_bank_bank_unavailable_alert": "Bank sedang diselenggarakan. Pindahan tidak tersedia pada masa ini.", "transfer_to_bank_money_input_title": "<PERSON><PERSON><PERSON>", "transfer_to_bank_info_receiver_format": "<PERSON><PERSON><PERSON>: {}", "transfer_to_bank_info_charge_fee": "<PERSON><PERSON>", "transfer_to_bank_info_charge_fee_rate_format": "(kadar: {:.2f})", "transfer_to_bank_info_total_amount": "<PERSON><PERSON><PERSON>", "transfer_to_bank_info_transfer_explain": "Catatan", "transfer_to_bank_info_transfer_explain_edit_hint_format": "<PERSON><PERSON><PERSON> dilihat oleh kedua-dua pembayar dan penerima bayaran. Maksimum {} aksara.", "transfer_to_bank_info_add_transfer_explain": "Tambah Nota", "transfer_to_bank_info_detail_title": "<PERSON><PERSON><PERSON>", "transfer_to_bank_info_detail_current_state": "Status", "transfer_to_bank_info_detail_paid_success": "<PERSON><PERSON><PERSON>", "transfer_to_bank_info_detail_withdrawn_success": "<PERSON><PERSON><PERSON><PERSON>", "HoneyPay_PrepareCardUI_Title": "Tetapkan Ka<PERSON>", "none": "Tiada", "mobile_item_key_bank": "Nombor mudah alih didaftarkan dengan bank", "mobile_item_place_holder_short": "Masukkan nombor telefon mudah alih", "FillCard_Number_Default_Mobile_Modify_Tips_New": "Nombor mudah alih yang dipautkan dahulu telah diisikan secara automatik. <PERSON><PERSON> boleh mengubahsuainya jika perlu.", "HoneyPay_MoneyInput_Hint_New": "<PERSON><PERSON><PERSON><PERSON> jum<PERSON>", "AddPayCard_No_Card_Bind_Card_Title": "Pautkan tanpa memasukkan nombor kad", "AddPayCard_Manual_Bind_Card_Title": "Pautkan dengan memasukkan nombor kad", "FillCard_Number_Reg_Hint_V3": "Masukkan nombor kad bank {}", "FastBindCardSelectBankUIV2_Title": "Memautkan tanpa memasukkan nombor kad", "FastBindCardSelectBankUIV2_Search_Hint": "Cari {} Bank", "qrcode_collection_settings": "<PERSON><PERSON><PERSON>", "qrcode_collection_amount": "<PERSON><PERSON><PERSON>", "qrcode_collection_remark": "<PERSON><PERSON> un<PERSON><PERSON>", "OfflinePay_Banner_Use_Tips": "<PERSON><PERSON>", "OfflinePay_Banner_Expand_Tips": "Kembangkan", "OfflinePay_Banner_Collapse_Tips": "Sembunyikan", "OfflinePay_Close_WalletLock_HalfDialog_Title": "<PERSON>yah<PERSON><PERSON><PERSON>", "OfflinePay_Close_WalletLock_HalfDialog_Content": "Demi keselamatan ketika menggunakan kod bayaran, anda boleh menetapkan kunci keselamatan. Selepas meneta<PERSON>, pengesahan keselamatan akan diperlukan semasa anda menggunakan kod bayaran.", "FillCardNumberV2_CountryCode_Hint": "Masukkan kod negara/rantau", "FillCardNumberV2_CountryCodeView_Hint": "<PERSON><PERSON>", "paydesk_main_page_not_use_favor": "<PERSON><PERSON> gunakan ta<PERSON>n.", "paysecurity_digital_cert_not_install": "<PERSON><PERSON><PERSON>", "WCPay_Digital_Cert_Desc_Not_Install": "Sijil digital tidak didayakan", "WCPay_Digital_Cert_Desc_Already_Install": "Sijil digital didayakan", "WCPay_Digital_Cert_Manage_Content_Desc": "Memasang sijil digital di peranti anda:", "WCPay_Digital_Cert_Manage_Content_Desc_1": "• Membuat pembayaran dari peranti anda dengan lebih selamat", "WCPay_Digital_Cert_Manage_Content_Desc_2": "• Menaikkan had harian anda untuk pembayaran menggunakan Baki", "WCPay_Digital_Cert_Install_Button_Title": "<PERSON><PERSON><PERSON>", "WCPay_Digital_Cert_Delete_Button_Title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "WCPay_Digital_Cert_Install_List_desc": "<PERSON><PERSON> yang mempunyai sijil did<PERSON>kan", "WCPay_Digital_Cert_Delete_Confirm_Content": "<PERSON><PERSON>h anda pasti anda mahu menyahdayakan sijil digital untuk ___<BRAND_ID>___semasa di peranti ini?", "WCPay_Digital_Delete_Confirm_Btn_Title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "WCPay_Digital_Cert_Install_Action_Title": "Sahkan identiti", "WCPay_Digital_Cert_Install_Action_Desc": "Pengesahan identiti yang diperlukan sebelum memasang sijil", "WCPay_Digital_Cert_Install_Input_Title_default": "Kad ID", "WCPay_Digital_Cert_Install_Input_Desc_default": "Masukkan nombor kad ID", "WCPay_Digital_Cert_Install_Input_Desc": "Masukkan nombor kad ID {}", "WCPay_Digital_Cert_Verify_Button_Title": "<PERSON><PERSON><PERSON>", "WCPay_Digital_Cert_Install_Sccuess": "<PERSON><PERSON><PERSON><PERSON>", "WCPay_Digital_Cert_Delete_Succ_Toast": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LQT_Purchase_Page_Title": "<PERSON><PERSON><PERSON>", "LQT_Purchase_Card_Info_Title": "<PERSON><PERSON><PERSON>", "LQT_MonetInputOutOfRange_Tips": "Baki tidak mencukupi. Tambah nilai dan cuba lagi.", "LQT_Limit_Cashier_Modal_Balance_Desc": "<PERSON><PERSON><PERSON>", "LQT_Limit_Cashier_Modal_LQT_Desc": "Dana <PERSON>", "LQT_Limit_Cashier_Modal_Transfer_In_Tips": "<PERSON><PERSON><PERSON>", "LQT_Limit_Cashier_Modal_Confirm_Button_Title": "<PERSON><PERSON><PERSON>", "LQT_SaveAmountLargeThanBankAvaible_Tips": "<PERSON><PERSON><PERSON> yang anda masukkan me<PERSON><PERSON> had bayaran bank", "LQT_Redeem_Card_Info_Title": "Keluarkan Ke", "LQT_Redeem_Page_Title": "<PERSON><PERSON><PERSON><PERSON>", "LQT_Redeem_Confirm_View_Desc": "<PERSON><PERSON><PERSON><PERSON>", "LQT_Redeem_Balance_Amount": "<PERSON>ki Dana Mini ¥{:.2f}", "LQT_Redeem_Balance_Insufficient": "Baki Dana Mini Tidak Mencukupi", "LQT_Redeem_Balance_Fetch_All": "<PERSON><PERSON><PERSON><PERSON>", "LQT_Loading_Card_Data": "Mendapatkan senarai kad bank", "LQT_Loading_LQT_Amount": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "LQT_Loading_LQ_Amount": "Mendapatkan jumlah dalam <PERSON>", "LQT_PerRedeem_Invalid_Default_Tips": "<PERSON><PERSON><PERSON> tung<PERSON> me<PERSON> had", "LQT_Redeem_Fetch_Amount_Large_Than_Bank_Avaible": "<PERSON><PERSON><PERSON> maksimum setiap transaksi ialah is ¥{:.2f}. <PERSON><PERSON> boleh membahagikan<PERSON> kepada beberapa transaksi.", "LQT_Redeem_Fetch_Amount_Large_Than_Max_Amount_Tips": "<PERSON><PERSON><PERSON>", "HoneyPay_Record_Receive_Title": "<PERSON><PERSON> kepada saya", "HoneyPay_Record_Donate_Title": "<PERSON><PERSON>ada saya", "LQTDetail_balance_Accessibility": "Baki: ¥{:.2f}", "WCPay_LQT_OnClickPurchase_Fail_Network_Error": "<PERSON><PERSON>. Tidak dapat memperoleh senarai kad bank anda. Cuba lagi kemudian.", "WCPay_LQT_OnClickRedeem_Fail_Network_Error": "<PERSON><PERSON>. Tidak dapat memperoleh senarai kad bank anda. Cuba lagi kemudian.", "WCPay_LQT_PurchaseFund_Fail_Network_Error": "Tidak dapat membeli. Cuba lagi kemudian.", "WCPay_LQT_QryPurchaseResult_Fail_Network_Error": "Tidak dapat menyemak hasil pembelian", "WCPay_LQT_PreRedeemFund_Fail_Network_Error": "Tidak dapat membuat pesanan penebusan. Cuba lagi kemudian.", "WCPay_LQT_RedeemFund_Fail_Network_Error": "Tidak dapat menebus dana. Cuba lagi kemudian.", "WCPay_LQT_MoneyVC_Save_TextFieldTips": "<PERSON><PERSON><PERSON>", "WCPay_LQT_MoneyVC_Fetch_TextFieldTips": "<PERSON><PERSON><PERSON>", "LQT_Purchase_Keyboard_Confirm_Title": "<PERSON><PERSON><PERSON><PERSON>", "LQT_Redeem_Keyboard_Confirm_Title": "<PERSON><PERSON><PERSON><PERSON>", "Wallet_Lock_Default_Title": "<PERSON><PERSON><PERSON>", "Wallet_Lock_FaceLock": "<PERSON><PERSON>", "Wallet_Lock_TouchLock": "Buka Kunci Cap Jari", "Wallet_Lock_BioLock": "<PERSON><PERSON>/Cap Jari", "Wallet_Lock_PatternLock": "<PERSON><PERSON>", "Wallet_Lock_PatternLock_Modify_Verify_Title": "<PERSON><PERSON><PERSON><PERSON> kata laluan corak lama", "Wallet_Lock_PatternLock_Modify": "<PERSON><PERSON>", "Wallet_Lock_PatternLock_Modify_SubTltle": "Tetapkan kata laluan corak baharu", "Wallet_Lock_Close_Tips": "<PERSON><PERSON><PERSON><PERSON> kunci k<PERSON>n din<PERSON>, tiada kaedah buka kunci diperlukan untuk mengakses \"Saya\" > \"Perkhidmatan\".", "Wallet_Lock_TouchLock_Verify_Title": "Sahkan Touch ID untuk meneruskan", "Wallet_Lock_FaceLock_Verify_Title": "Sahkan Face ID untuk meneruskan", "Wallet_Lock_PatternLock_Verify_Title": "<PERSON><PERSON><PERSON>n kata laluan corak", "Wallet_Lock_Verify_byPwd": "Pengesahan Kat<PERSON>", "Wallet_Lock_Verify_Btn_FaceID": "<PERSON><PERSON><PERSON> muka", "Wallet_Lock_Verify_Btn_TouchID": "Sahkan cap jari", "Wallet_Lock_PatternLock_Setup_Title": "Tetapkan kata laluan corak", "Wallet_Lock_PatternLock_Reset_Title": "Lupa Kata Laluan Corak?", "Wallet_Lock_PatternLock_Confirm_Title": "<PERSON><PERSON><PERSON><PERSON> semula untuk pengesahan", "Wallet_Lock_PatternLock_Confirm_Error_Tips": "Kata laluan tidak konsisten. Tetapkan lagi.", "Wallet_Lock_PatternLock_Verify_Error_Tips": "<PERSON>a laluan salah. {} peluang lagi untuk masuk semula.", "Wallet_Lock_PatternLock_Verify_Limit_Tips": "Terlalu banyak percubaan. Cuba sekali lagi selepas {} minit.", "Wallet_Lock_PatternLock_Modify_AfterVerify_Title": "Tetapkan kata laluan corak baharu", "Wallet_Lock_PatternLock_InvalidPattern_Tips": "Sekurang-kurangnya 4 mata diperlukan. Tetapkan lagi.", "Wallet_Lock_PatternLock_Setup_Succ_Tips": "<PERSON>a laluan corak ditetapkan", "Wallet_Lock_PatternLock_Modify_Succ_Tips": "<PERSON><PERSON> la<PERSON>an co<PERSON> di<PERSON>kar", "Wallet_Lock_PatternLock_Setup_Cancel_Tips": "<PERSON>an dayakan kunci corak?", "Wallet_Lock_PatternLock_Setup_Cancel_LeftBtn": "<PERSON><PERSON><PERSON>", "Wallet_Lock_PatternLock_Setup_Cancel_RightBtn": "<PERSON><PERSON><PERSON>", "Wallet_Lock_Not_Support_TouchID_Tip": "Touch ID tidak tersedia pada peranti ini. Tetap semula kunci keselamatan.", "Wallet_Lock_Not_Support_FaceID_Tip": "Face ID tidak tersedia pada peranti ini. Tetap semula kunci keselamatan.", "Wallet_Lock_Close_Wallet_Lock_Tip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Wallet_Lock_Setup_Pattern_Lock_Tip": "Tetapkan Kata <PERSON>", "Wallet_Lock_TouchID_NotEnrolled_Tip": "Touch ID tidak didayakan. Dayakan Touch ID dalam tetapan sistem atau tetapkan semula kunci keselamatan.", "Wallet_Lock_FaceID_NotEnrolled_Tip": "Face ID tidak didayakan. <PERSON><PERSON>n Face ID dalam tetapan sistem atau tetapkan semula kunci keselamatan.", "Wallet_Lock_FingerPrint_NotEnrolled_Tip": "Tiada cap jari dalam sistem. Simpan cap jari anda dan tetapkan kunci keselamatan semula.", "Wallet_Lock_PatternLock_VerifyBlock_Tip": "Terlalu banyak percubaan Sahkan identiti anda dalam \"Tetapkan Semula Kata Laluan Corak\" atau cuba sekali lagi dalam 10 minit.", "Wallet_Lock_PatternLock_VerifyBlock_Reset_Tip": "Tetapkan <PERSON>", "Wallet_Lock_New_FingerPrint_Authen_Tips": "Cap jari baharu dimasukkan. Masukkan kata laluan pembayaran untuk mengesahkan identiti.", "Wallet_Lock_New_TouchID_Authen_Tips": "Maklumat cap jari pada peranti ini telah berubah. Masukkan kata laluan pembayaran untuk mengesahkan identiti anda.", "Wallet_Lock_New_FaceID_Authen_Tips": "Maklumat muka pada peranti ini telah berubah. Ma<PERSON>kkan kata laluan pembayaran untuk mengesahkan identiti anda.", "Wallet_Lock_Forget_Pwd": "<PERSON><PERSON><PERSON><PERSON>", "Wallet_Lock_Retry_Pwd": "Cuba Semula", "LQT_Detail_Operation_More_Product_Title": "<PERSON><PERSON><PERSON>", "pay_settings_biometric_pay_touchid_title": "Pembayaran Cap Jari", "pay_settings_biometric_pay_faceid_title": "<PERSON><PERSON><PERSON><PERSON>", "pay_settings_biometric_pay_multi_title": "Pembayaran Mu<PERSON>/Cap Jari", "pay_settings_biometric_pay_touchid_desc": "<PERSON><PERSON><PERSON><PERSON>, anda boleh menggunakan pengesahan cap jari untuk membuat pembayaran dengan lebih cepat.", "pay_settings_biometric_pay_faceid_desc": "<PERSON><PERSON><PERSON><PERSON>, anda boleh menggunakan pengesahan muka untuk membuat pembayaran dengan lebih cepat.", "pay_settings_biometric_pay_multi_desc": "Aktifkan pembayaran muka atau cap jari untuk membuat pembayaran dengan lebih cepat.", "pay_settings_biometric_pay_enable_faceid": "<PERSON><PERSON><PERSON>", "pay_settings_biometric_pay_enable_touchid": "Aktifkan Pembayaran Cap Jari", "common_reddot_accessibility": "<PERSON>a telah menerima mesej baharu", "common_help": "Bantu", "bind_new_card_input_name": "Masukkan nama yang didaftarkan dengan bank", "paydesk_title_accessibility_selected": "<PERSON><PERSON><PERSON><PERSON>", "RedDot_New": "NEW", "renewal_or_sign_time_item_key": "Pengeluaran/Pembaharuan Permit", "WCPay_Option_Item": "<PERSON><PERSON><PERSON>", "VoiceOver_OfflinePay_Unselected": "Tidak Terpilih", "FillCard_Number_Reg_Hint_Self": "Masukkan nombor kad anda", "common_continue": "teruskan", "WCPay_Risk_Dialog_Title": "Potensi risiko telah dikesan. <PERSON><PERSON> sahkan bahawa anda ingin men<PERSON> proses. <PERSON><PERSON>, identiti anda akan perlu disahkan.", "WCPay_Risk_Not_Support_Dialog_Title": "Potensi risiko telah di<PERSON>an. <PERSON>la selesaikan risiko sebelum meneruskan proses.", "WCPay_Risk_Failed_Dialog_Title": "Pengesahan identiti gagal, operasi perlu ditamatkan. Tutup", "bind_card_agreement_protocal_and_next": "<PERSON><PERSON><PERSON> dan <PERSON>", "wallet": "Dompet", "awaiting_real_name_verification": "Menunggu pengesahan identiti", "five_stars": "*****", "Card_UserAgreement_Read_And_Agreement_Title": "<PERSON><PERSON><PERSON><PERSON><PERSON> diperlukan semasa menambah", "FaceCheck_Common_Error": "Sistem sibuk. Cuba lagi.", "FaceCheck_MP_Request_Use": " <PERSON><PERSON>", "FaceCheck_MP_Confirm_Tip": "___<BRAND>___ menggunakan pengecaman wajah untuk mengesahkan identiti anda. Ini mesti ditetapkan oleh pemegang akaun sahaja.", "FaceCheck_MP_Front_Feedback": "Pusat <PERSON>", "FaceCheck_Recoging": "<PERSON><PERSON><PERSON>...", "waiting_for_real_name_authentication": "Menunggu pengesahan identiti", "collect_sub_title": "<PERSON><PERSON><PERSON>", "collect_main_add_desc_title_simple_change": "<PERSON><PERSON>", "collect_main_add_desc_title": "Tambah Nota", "remittance_amount_lowest_limit": "<PERSON><PERSON><PERSON> pindahan terendah ialah ￥0.01.", "collect_main_fixed": "Tetap<PERSON>", "collect_main_first_enter_tips_title": "<PERSON><PERSON><PERSON><PERSON>", "collect_main_first_enter_tips_new": "<PERSON> yang diterima akan didepositkan dalam ___<BRAND_Balance>___ anda (\"<PERSON><PERSON>\" > \"Perkhidmatan\" > \"<PERSON><PERSON>), yang boleh di<PERSON>an atau dikeluarkan.", "collect_main_close_ring_tone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "collect_main_close_ring_tone_tips": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "collect_main_open_ring_tone": "<PERSON><PERSON><PERSON>", "collect_main_open_ring_tone_tips": "Dayakan. Sila semak kelantangan media anda dihidupkan.", "collect_main_qrcode_usage_other": "Lain-lain", "collect_main_qrcode_usage_other_placeholder": "Tambah maklumat (sehingga 16 aksara)", "collect_main_payer_desc_default_placeholder": "Tambah nota untuk penerima.", "collect_qrcode_save_failed": "<PERSON><PERSON><PERSON><PERSON><PERSON> gagal", "collect_material_guide_save_text_toast": "Disimpan pada Album", "collect_mch_module_title": "Kod <PERSON>gaan Pemilikan Individu", "collect_personal_module_title": "Kod QR Pemilikan Individu", "collect_setting_title": "Tetapan <PERSON>", "collect_main_fixed_cancel": "Kosong<PERSON>", "collect_main_more_function": "Tetapan <PERSON>", "collect_main_save_qrcode": "Simpan <PERSON>", "collect_main_receive_title": "<PERSON><PERSON><PERSON>", "collect_main_paying": "Membayar...", "collect_main_pay_suc": "<PERSON><PERSON><PERSON><PERSON> berjaya", "collect_main_pay_cancel": "Batalkan Pembayaran", "collect_main_loading_title": "Memuatkan kod QR...", "collect_main_ring_not_support": "Tidak disokong pada sistem ini", "WCPay_Transfer_To_Format": "Pindah ke {}", "WCPay_Transfer_Weixin_Alias_Prefix": "___<BRAND_ID>___:", "WCPay_Transfer_InputAmount_Tips": "<PERSON><PERSON><PERSON><PERSON> jumlah te<PERSON><PERSON>h da<PERSON>u", "WCPay_Transfer_Cashier_Desc_Format": "Pindah ke {}", "WCPay_Transfer_Succ_Tips": "<PERSON><PERSON><PERSON> peneri<PERSON> o<PERSON>h {}", "WCPay_Service": "<PERSON><PERSON>", "recognize_and_pay": "Buat pengecaman dan bayar", "bizf2f_input_ui_page_to_person": "Bayar kepada Individu", "bizf2f_input_ui_page_add_remark": "Tambah Nota", "bizf2f_input_ui_page_amount_title": "<PERSON><PERSON><PERSON>", "WCPay_Verify_Password_Get_SMS_Code": "<PERSON><PERSON>", "WCPay_VerifyCode_Header_Description": "Pengesahan SMS diperlukan untuk transaksi ini.", "bizf2f_input_ui_page_pay_action": "Bayar", "bizf2f_input_ui_page_change_remark": "<PERSON><PERSON>", "bizf2f_input_ui_page_pay_title": "Bayar", "bizf2f_favor_title": "<PERSON><PERSON><PERSON>", "bizf2f_favor_total_fee": "<PERSON><PERSON><PERSON>", "bizf2f_favor_calculating": "Mengira...", "bizf2f_favor_select_favor": "<PERSON><PERSON><PERSON>", "UN_BIND_CARD_TITLE": "Nyahpaut Kad Bank", "WCPay_system_version_limitation_tip": "Untuk ciri selanju<PERSON>nya, semak HarmonyOS 4.2 atau lebih lama, atau peranti lain.", "reconfirm_payment_amount_title": "<PERSON><PERSON><PERSON> jumlah pembayaran sekali lagi", "reconfirm_payment_amount_des": "<PERSON><PERSON> k<PERSON>n aset, sahkan jumlah bayaran untuk mengelakkan kesilapan.", "reconfirm_amount_page_tip": "Disebabkan k<PERSON><PERSON><PERSON> kawal selia, pembayaran yang mele<PERSON> had kod QR statik hendaklah dilengkapkan dengan mengimbas kod QR dinamik di bawah. Ketik butang untuk mengesahkan dan melengkapkan bayaran.", "Hongbao_SendUI_NavigationBar_Title": "Hantar Paket Me<PERSON>", "Hongbao_SendUI_Send_Button_Titlle": "Sedia Sampul duit", "Hongbao_SendUI_Count_Title": "<PERSON><PERSON><PERSON>", "Hongbao_SendUI_Amount_Title_Group": "<PERSON><PERSON><PERSON>", "Hongbao_SendUI_Amount_Title_Single": "<PERSON><PERSON><PERSON>", "Hongbao_SendUI_RandomLuckyMode_Title": "<PERSON><PERSON><PERSON>", "Hongbao_SendUI_Count_Tips": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>iti", "Hongbao_SendUI_Amount_Tips": "¥0.00", "Hongbao_SendUI_Default_Wishing": "<PERSON><PERSON>", "Hongbao_Per_Hongbao_Max_Amount_Format": "Hingga {}CNY untuk setiap Paket Merah", "HongBao_SendTips": "<PERSON><PERSON> ya<PERSON>", "HongBao_OpenTips": "<PERSON><PERSON>", "HongBao_AmoutTips": "CNY", "HongBao_MainTitle": "<PERSON><PERSON>", "HongBao_Cashier_Desc": "___<BRAND_hongbao>___", "Hongbao_SendUI_RandomMode_Title": "<PERSON><PERSON><PERSON>", "Hongbao_SendUI_NormalMode_Title": "<PERSON><PERSON><PERSON> ya<PERSON>", "Hongbao_SendUI_ExclusiveMode_Title": "Eksklusif", "Hongbao_SendUI_Select_Count_Suffix": "", "Hongbao_SendUI_Group_Member_Count_Format": "<PERSON><PERSON><PERSON>n ini mempunyai {} ahli", "Hongbao_SendUI_Amount_Title_Group_Normal": "<PERSON><PERSON><PERSON>", "Hongbao_SendUI_Perperson_Maxvalue_Error_Tips": "Hingga {}CNY untuk setiap Paket Merah", "Hongbao_SendUI_Perperson_Minvalue_Error_Tips": "Sekurang-kurangnya {:.2f} untuk setiap Paket Merah", "Hongbao_SendUI_Count_Larger_Than_Group_Mem_Count_Error_Tips": "Bilangan Paket Merah tidak boleh melebihi ahli kumpulan.", "Hongbao_SendUI_Total_Num_Error_Tips": "<PERSON><PERSON><PERSON> maksimum ialah {}.", "Hongbao_SendUI_Select_Count_Data_Invalid_Error_Tips": "\"Kuantiti\" tidak dimasukkan", "Hongbao_SendUI_Amount_Data_Invalid_Error_Tips": "“Jumlah” tidak dimasukkan", "Hongbao_SendUI_Select_Count_Zero_Error_Tips": "<PERSON><PERSON><PERSON>.", "Hongbao_SendUI_Amount_Larger_Than_Max_Amount_Error_Tips": "<PERSON><PERSON><PERSON> k<PERSON>han tidak boleh melebihi {}CNY.", "Hongbao_ReceiveModal_Detail_Link": "<PERSON><PERSON>", "Hongbao_DetailUI_Load_More_Text": "Klik untuk memuatkan lagi", "TransferPhone_Entry_Title": "<PERSON><PERSON><PERSON>", "TransferPhone_To_Bank_Title": "Pindah ke Kad Bank", "TransferPhone_To_Bank_Desc": "Masukkan kad bank penerima untuk membuat pindahan ke akaun bank mereka.", "TransferPhone_To_Phone_Title": "Pindah ke Nombor Mudah Alih", "TransferPhone_To_Phone_Desc": "Masukkan nombor mudah alih penerima bayaran untuk membuat pindahan ke ___<BRAND_Balance>___ mereka.", "TransferPhone_To_PaySetting": "<PERSON><PERSON><PERSON>", "WCPay_ThirdParty_Tips_Title": "<PERSON><PERSON><PERSON>", "WCPay_Service_Manage": "<PERSON><PERSON>", "identify_and_pay": "Buat pengecaman dan bayar", "Hongbao_SendUI_Exclusive_Mode_Send_To_Who": "Hantar kepada", "Hongbao_SendUI_Exclusive_Mode_Send_To_Who_Error_Tips": "“Hantar kepada” tidak dipilih", "Hongbao_SendUI_Amount_Data_Invalid_Error_Tips_2": "“Jumlah” tidak tidak dimasukkan", "MerchantPay_Input_Remark_Hint_Format": "<PERSON><PERSON><PERSON> dilihat oleh penerima. Maksimum {} aksara.", "MerchantPay_Input_Remark_Title": "Tambah Nota", "MerchantPay_Transfer_To_Format": "Bayar kepada {}", "Greeting_Hongbao_Random_Change_Amount": "<PERSON><PERSON>", "Greeting_Hongbao_Who_Receive_Hongbao_Format": "<PERSON><PERSON><PERSON> o<PERSON> {}", "set_amount": "Tetap<PERSON>"}