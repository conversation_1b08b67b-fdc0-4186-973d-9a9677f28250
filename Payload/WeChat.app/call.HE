{"Metadata": {"Created": "2021-12-23", "Description": "call.m4a.202112232230", "M2V_VER": 106, "Version": 2}, "PatternList": [{"AbsoluteTime": 36, "Pattern": [{"Event": {"Duration": 142, "Index": 0, "Parameters": {"Curve": [{"Frequency": -30, "Intensity": 0, "Time": 0}, {"Frequency": -31, "Intensity": 1, "Time": 1}, {"Frequency": -29, "Intensity": 1, "Time": 100}, {"Frequency": -30, "Intensity": 0, "Time": 142}], "Frequency": 30, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 152, "Type": "continuous"}}]}, {"AbsoluteTime": 352, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 168, "Type": "continuous"}}]}, {"AbsoluteTime": 688, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 164, "Type": "transient"}}]}, {"AbsoluteTime": 1020, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 168, "Type": "transient"}}]}, {"AbsoluteTime": 1352, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 100, "Intensity": 85}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 168, "Type": "transient"}}]}, {"AbsoluteTime": 1688, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 164, "Type": "transient"}}]}, {"AbsoluteTime": 2640, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 168, "Type": "continuous"}}]}, {"AbsoluteTime": 2972, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 168, "Type": "continuous"}}]}, {"AbsoluteTime": 3308, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 164, "Type": "continuous"}}]}, {"AbsoluteTime": 3640, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 168, "Type": "continuous"}}]}, {"AbsoluteTime": 3972, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 100, "Intensity": 85}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 168, "Type": "transient"}}]}, {"AbsoluteTime": 4308, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 168, "Type": "transient"}}]}, {"AbsoluteTime": 5592, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 168, "Type": "continuous"}}]}, {"AbsoluteTime": 5924, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 168, "Type": "continuous"}}]}, {"AbsoluteTime": 6260, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 164, "Type": "transient"}}]}, {"AbsoluteTime": 6592, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 168, "Type": "transient"}}]}, {"AbsoluteTime": 6928, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 100, "Intensity": 85}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 164, "Type": "transient"}}]}, {"AbsoluteTime": 7260, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 168, "Type": "transient"}}]}, {"AbsoluteTime": 8716, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 167, "Type": "continuous"}}]}, {"AbsoluteTime": 9047, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 169, "Type": "continuous"}}]}, {"AbsoluteTime": 9383, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 164, "Type": "transient"}}]}, {"AbsoluteTime": 9716, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 167, "Type": "transient"}}]}, {"AbsoluteTime": 10047, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 100, "Intensity": 85}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 169, "Type": "transient"}}]}, {"AbsoluteTime": 10383, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 169, "Type": "transient"}}]}, {"AbsoluteTime": 11835, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 169, "Type": "continuous"}}]}, {"AbsoluteTime": 12171, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 169, "Type": "continuous"}}]}, {"AbsoluteTime": 12504, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 167, "Type": "transient"}}]}, {"AbsoluteTime": 12840, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 164, "Type": "transient"}}]}, {"AbsoluteTime": 13171, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 100, "Intensity": 85}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 169, "Type": "transient"}}]}, {"AbsoluteTime": 13504, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 167, "Type": "transient"}}]}, {"AbsoluteTime": 14959, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 168, "Type": "continuous"}}]}, {"AbsoluteTime": 15292, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 167, "Type": "continuous"}}]}, {"AbsoluteTime": 15627, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 169, "Type": "continuous"}}]}, {"AbsoluteTime": 15959, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 169, "Type": "continuous"}}]}, {"AbsoluteTime": 16296, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 100, "Intensity": 85}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 168, "Type": "transient"}}]}, {"AbsoluteTime": 16628, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 168, "Type": "transient"}}]}, {"AbsoluteTime": 18084, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 168, "Type": "continuous"}}]}, {"AbsoluteTime": 18416, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 168, "Type": "continuous"}}]}, {"AbsoluteTime": 18752, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 164, "Type": "transient"}}]}, {"AbsoluteTime": 19084, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 168, "Type": "transient"}}]}, {"AbsoluteTime": 19416, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 100, "Intensity": 85}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 168, "Type": "transient"}}]}, {"AbsoluteTime": 19752, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 164, "Type": "transient"}}]}, {"AbsoluteTime": 21204, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 168, "Type": "continuous"}}]}, {"AbsoluteTime": 21540, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 168, "Type": "continuous"}}]}, {"AbsoluteTime": 21872, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 168, "Type": "transient"}}]}, {"AbsoluteTime": 22208, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 164, "Type": "transient"}}]}, {"AbsoluteTime": 22540, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 100, "Intensity": 85}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 168, "Type": "transient"}}]}, {"AbsoluteTime": 22872, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 168, "Type": "transient"}}]}, {"AbsoluteTime": 24328, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 168, "Type": "continuous"}}]}, {"AbsoluteTime": 24660, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 172, "Type": "continuous"}}]}, {"AbsoluteTime": 24996, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 168, "Type": "transient"}}]}, {"AbsoluteTime": 25328, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 168, "Type": "transient"}}]}, {"AbsoluteTime": 25664, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 100, "Intensity": 85}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 168, "Type": "transient"}}]}, {"AbsoluteTime": 25996, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 168, "Type": "transient"}}]}, {"AbsoluteTime": 27452, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 168, "Type": "continuous"}}]}, {"AbsoluteTime": 27784, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 168, "Type": "continuous"}}]}, {"AbsoluteTime": 28120, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 164, "Type": "transient"}}]}, {"AbsoluteTime": 28452, "Pattern": [{"Event": {"Duration": 149, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 1, "Time": 1}, {"Frequency": -39, "Intensity": 1, "Time": 32}, {"Frequency": -39, "Intensity": 0, "Time": 149}], "Frequency": 50, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 168, "Type": "transient"}}]}, {"AbsoluteTime": 28784, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 100, "Intensity": 85}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 168, "Type": "transient"}}]}, {"AbsoluteTime": 29120, "Pattern": [{"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 0, "Type": "transient"}}, {"Event": {"Index": 0, "Parameters": {"Frequency": 45, "Intensity": 100}, "RelativeTime": 164, "Type": "transient"}}]}, {"AbsoluteTime": 29755, "Pattern": [{"Event": {"Duration": 3, "Index": 0, "Parameters": {"Curve": [{"Frequency": 0, "Intensity": 0, "Time": 0}, {"Frequency": 0, "Intensity": 0.01, "Time": 1}, {"Frequency": 0, "Intensity": 0.01, "Time": 2}, {"Frequency": 0, "Intensity": 0, "Time": 3}], "Frequency": 30, "Intensity": 100}, "RelativeTime": 0, "Type": "continuous"}}]}]}