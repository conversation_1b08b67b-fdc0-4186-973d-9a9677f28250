<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="Content-Language" content="en" charset="utf-8">
	<meta charset="UTF-8">
	<meta id="viewport" name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1; user-scalable=no;" />
	<style> html,p,h1,ul,li{margin:0px;padding:0px;} ul{list-style-type:none;} body{color:#000;font:14px/1.5 微软雅黑,Helvetica,&quot;Helvetica Neue&quot;,&quot;segoe UI Light&quot;,&quot;Kozuka Gothic Pro&quot;;} h1{font-size:16px; padding-bottom:12px;margin-bottom:5px;} .articleTitle{margin-bottom:6px;} .sec_body { margin:25px 15px;} p{margin-bottom:6px;} .bold{font-weight:bold;} .article{margin-bottom:32px;} .sec_body .content { padding:10px;border-width:0px; } @media (prefers-color-scheme: dark) { body { background-color: #232323; color: rgba(255, 255, 255, .8); } } </style>
	<title>ไม่มีการเชื่อมต่ออินเทอร์เน็ต</title>
</head>
<body class="sec_body">
	<div class="container">
		<h1>อุปกรณ์ของคุณไม่ได้เชื่อมต่อกับอินเทอร์เน็ต</h1>
		<div class="article">
			<p class="articleTitle">ในการเชื่อมต่ออินเทอร์เน็ต ลองใช้วิธีการด้านล่าง:</p>
			<ul>
				<li>จากอุปกรณ์ของคุณ ให้ไปที่ &quot;<strong>การตั้งค่า</strong>&quot; - &quot;<strong>Wi-Fi</strong>&quot; และเข้าร่วมเครือข่าย Wi-Fi ที่มีอยู่</li>
				<li>จากอุปกรณ์ของคุณ ให้ไปที่ &quot;<strong>การตั้งค่า</strong>&quot; - &quot;<strong>เซลลูลาร์</strong>&quot; และเปิดใช้งาน &quot;<strong>ข้อมูลเซลลูลาร์</strong>&quot; (คุณอาจถูกเรียกเก็บค่าบริการจากผู้ให้บริการของคุณสำหรับการใช้ข้อมูล)</li>
			</ul>
		</div>
		<div class="article">
			<p class="articleTitle">หากคุณได้จับคู่อุปกรณ์ของคุณกับ Apple Watch：</p>
			<ul>
				<li>เปิดแอป &quot;<strong>Watch</strong>&quot; - &quot;<strong>ข้อมูลเซลลูลาร์</strong>&quot; - &quot;<strong>WeChat</strong>&quot; และอนุญาตให้ WeChat เข้าถึงข้อมูล</li>
			</ul>
		</div>
		<div class="article">
			<p class="articleTitle">หากคุณเชื่อมต่อกับเครือข่าย Wi-Fi:</p>
			<ul>
				<li>ให้ตรวจสอบว่าฮอตสปอต Wi-Fi เชื่อมต่อกับอินเทอร์เน็ตหรือไม่ หรืออุปกรณ์ของคุณได้รับอนุญาตให้เข้าถึงฮอตสปอตหรือไม่</li>
			</ul>
		</div>
	</div>
</body>
</html>

