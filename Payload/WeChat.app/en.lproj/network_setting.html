<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="Content-Language" content="en">
	<meta http-equiv="content-type" content="text/html; charset=utf-8"/>
	<meta id="viewport" name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1; user-scalable=no;" />
	<style>
		html,p,h1,ul,li{margin:0px;padding:0px;}
		ul{list-style-type:none;}
		body{color:#000;font:14px/1.5 微软雅黑,Helvetica,"Helvetica Neue","segoe UI Light","Kozuka Gothic Pro";}
		h1{font-size:16px; padding-bottom:12px;margin-bottom:5px;}
		.articleTitle{margin-bottom:6px;}
		.sec_body { margin:25px 15px;}
		p{margin-bottom:6px;}
		.bold{font-weight:bold;}
		.article{margin-bottom:32px;}
		.sec_body .content { padding:10px;border-width:0px; }
        @media (prefers-color-scheme: dark) {
          body {
              background-color: #232323;
              color: rgba(255, 255, 255, .8);
          }
        }
	</style>
	<title>No Internet Connection</title>
</head>
<body class="sec_body">
	<div class="container">
		<h1>Your device is not connected to the internet </h1>
		<div class="article">
			<p class="articleTitle">To connect to the internet, try the below methods:</p>
			<ul>
				<li>On your device, go to &quot;<strong>Settings</strong>&quot; - &quot;<strong>Wi-Fi</strong>&quot;, and join an available Wi-Fi network. </li>
				<li>On your device, go to &quot;<strong>Settings</strong>&quot; - &quot;<strong>Cellular</strong>&quot;, and enable &quot;<strong>Cellular Data</strong>&quot;. (You may be charged by your service provider for data usage.)</li>
			</ul>
		</div>
		<div class="article">
			<p class="articleTitle">If you've paird your device with an Apple Watch：</p>
			<ul>
				<li>Open the “<strong>Watch</strong>” app - “<strong>Celluar Data</strong>” - “<strong>WeChat</strong>”, and allow permission for WeChat to access data.</li>
			</ul>
		</div>
		<div class="article">
			<p class="articleTitle">If you've connected to a Wi-Fi network: </p>
			<ul>
				<li>Check if the Wi-Fi hotspot is connected to the internet, or if your device is allowed to access the hotspot.</li>
			</ul>
		</div>
	</div>
</body>
</html>

