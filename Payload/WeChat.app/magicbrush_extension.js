(function(global) {
  var __NativeGlobal = global.NativeGlobal;
  var __WeixinCanvas = global.WeixinCanvas;
  if (__WeixinCanvas) {
    delete global.WeixinCanvas;
  }
    
  __NativeGlobal.bindFrameSet = function(id) {
    if (!__WeixinCanvas.hasFrameSet(id)) return null;
    return {
      get id() {
        return id;
      },
      get name() {
        return __WeixinCanvas.getFrameSetName(id);
      },
      get width() {
        return __WeixinCanvas.getFrameSetWidth(id);
      },
      get height() {
        return __WeixinCanvas.getFrameSetHeight(id);
      },
      createCanvas() {
        var canvasId = __WeixinCanvas.createCanvasView(id);
        var canvas = __WeixinCanvas.createCanvasContext(canvasId);
        canvas.id = canvasId;
          
        wrap(canvasId, canvas);
        return canvas;
      }
    };
  };
    
    function toArray(args) { return Array.prototype.slice.call(args); }
    function wrap(id, canvas) {
      if (!__WeixinCanvas.has(id)) {
        return
      }
      const style = {
        set left(left) {
          __WeixinCanvas.setLeft(id, left);
        },
        get left() {
            return __WeixinCanvas.getLeft(id);
        },
        set top(top) {
            __WeixinCanvas.setTop(id, top);
        },
        get top() {
            return __WeixinCanvas.getTop(id);
        },
        set width(width) {
            __WeixinCanvas.setWidth(id, width);
        },
        get width() {
            return __WeixinCanvas.getWidth(id);
        },
        set height(height) {
            __WeixinCanvas.setHeight(id, height);
        },
        get height() {
            return __WeixinCanvas.getHeight(id);
        },
      };
      Object.defineProperties(canvas, {
        __wrapped__: {
            value: true
        },
        style: {
          value: style,
          enumerable: true
        },
        remove: {
          value: function() {
              if (__WeixinCanvas.has(id)) {
                __WeixinCanvas.remove(id);
              }
          }
        },
        updatePointerEventBounds: {
          value: function(bounds) {
              if (__WeixinCanvas.has(id)) {
                __WeixinCanvas.updatePointerEventBounds(id, bounds);
              }
          }
        },
      });
  }
})(this);
 
