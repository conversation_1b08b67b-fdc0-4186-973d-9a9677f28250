{"v": "5.4.4", "fr": 30, "ip": 0, "op": 42, "w": 190, "h": 190, "nm": "双击动画", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "hand", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [99.441, 109.206, 0], "ix": 2}, "a": {"a": 0, "k": [62.65, 72.545, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100], "e": [95, 95, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 6, "s": [95, 95, 100], "e": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 12, "s": [100, 100, 100], "e": [95, 95, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 18, "s": [95, 95, 100], "e": [100, 100, 100]}, {"t": 24}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [-7.66, 0.928], [-1.268, -7.587], [0, 0], [0, 0], [2.905, -17.817], [0.041, -0.224], [0, 0], [7.005, 0], [0, 0], [2.743, 3.66], [0, 0], [-4.454, 5.136], [-5.093, -4.056]], "o": [[0, 0], [-0.5, -7.699], [7.637, -0.925], [0, 0], [0, 0], [17.817, 2.905], [-0.037, 0.224], [0, 0], [-1.272, 6.89], [0, 0], [-4.575, 0], [0, 0], [-4.077, -5.44], [4.264, -4.919], [0, 0]], "v": [[-27.945, 11.488], [-34.119, -59.75], [-21.359, -75.125], [-5.441, -63.216], [0.477, -24.544], [32.495, -19.323], [59.495, 18.198], [59.378, 18.87], [51.713, 60.404], [37.428, 72.294], [-12.471, 72.294], [-24.096, 66.479], [-58.324, 20.81], [-57.678, 2.688], [-40.937, 1.143]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [-5.178, 0.627], [-0.857, -5.129], [0, 0], [0, 0], [2.905, -17.817], [0.041, -0.224], [0, 0], [7.005, 0], [0, 0], [2.743, 3.66], [0, 0], [-4.454, 5.136], [-5.093, -4.056]], "o": [[0, 0], [-0.338, -5.204], [5.163, -0.625], [0, 0], [0, 0], [17.817, 2.905], [-0.037, 0.224], [0, 0], [-1.272, 6.89], [0, 0], [-4.575, 0], [0, 0], [-4.077, -5.44], [4.264, -4.919], [0, 0]], "v": [[-26.695, 12.988], [-30.281, -53.437], [-21.656, -63.831], [-10.896, -55.781], [-0.023, -21.544], [32.495, -16.823], [59.495, 18.198], [59.378, 18.87], [51.713, 60.404], [37.428, 72.294], [-12.471, 72.294], [-24.096, 66.479], [-55.324, 19.31], [-54.928, 2.188], [-38.187, 2.393]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 6, "s": [{"i": [[0, 0], [0, 0], [-5.178, 0.627], [-0.857, -5.129], [0, 0], [0, 0], [2.905, -17.817], [0.041, -0.224], [0, 0], [7.005, 0], [0, 0], [2.743, 3.66], [0, 0], [-4.454, 5.136], [-5.093, -4.056]], "o": [[0, 0], [-0.338, -5.204], [5.163, -0.625], [0, 0], [0, 0], [17.817, 2.905], [-0.037, 0.224], [0, 0], [-1.272, 6.89], [0, 0], [-4.575, 0], [0, 0], [-4.077, -5.44], [4.264, -4.919], [0, 0]], "v": [[-26.695, 12.988], [-30.281, -53.437], [-21.656, -63.831], [-10.896, -55.781], [-0.023, -21.544], [32.495, -16.823], [59.495, 18.198], [59.378, 18.87], [51.713, 60.404], [37.428, 72.294], [-12.471, 72.294], [-24.096, 66.479], [-55.324, 19.31], [-54.928, 2.188], [-38.187, 2.393]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [-7.66, 0.928], [-1.268, -7.587], [0, 0], [0, 0], [2.905, -17.817], [0.041, -0.224], [0, 0], [7.005, 0], [0, 0], [2.743, 3.66], [0, 0], [-4.454, 5.136], [-5.093, -4.056]], "o": [[0, 0], [-0.5, -7.699], [7.637, -0.925], [0, 0], [0, 0], [17.817, 2.905], [-0.037, 0.224], [0, 0], [-1.272, 6.89], [0, 0], [-4.575, 0], [0, 0], [-4.077, -5.44], [4.264, -4.919], [0, 0]], "v": [[-27.945, 11.488], [-34.119, -59.75], [-21.359, -75.125], [-5.441, -63.216], [0.477, -24.544], [32.495, -19.323], [59.495, 18.198], [59.378, 18.87], [51.713, 60.404], [37.428, 72.294], [-12.471, 72.294], [-24.096, 66.479], [-58.324, 20.81], [-57.678, 2.688], [-40.937, 1.143]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12, "s": [{"i": [[0, 0], [0, 0], [-7.66, 0.928], [-1.268, -7.587], [0, 0], [0, 0], [2.905, -17.817], [0.041, -0.224], [0, 0], [7.005, 0], [0, 0], [2.743, 3.66], [0, 0], [-4.454, 5.136], [-5.093, -4.056]], "o": [[0, 0], [-0.5, -7.699], [7.637, -0.925], [0, 0], [0, 0], [17.817, 2.905], [-0.037, 0.224], [0, 0], [-1.272, 6.89], [0, 0], [-4.575, 0], [0, 0], [-4.077, -5.44], [4.264, -4.919], [0, 0]], "v": [[-27.945, 11.488], [-34.119, -59.75], [-21.359, -75.125], [-5.441, -63.216], [0.477, -24.544], [32.495, -19.323], [59.495, 18.198], [59.378, 18.87], [51.713, 60.404], [37.428, 72.294], [-12.471, 72.294], [-24.096, 66.479], [-58.324, 20.81], [-57.678, 2.688], [-40.937, 1.143]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [-5.178, 0.627], [-0.857, -5.129], [0, 0], [0, 0], [2.905, -17.817], [0.041, -0.224], [0, 0], [7.005, 0], [0, 0], [2.743, 3.66], [0, 0], [-4.454, 5.136], [-5.093, -4.056]], "o": [[0, 0], [-0.338, -5.204], [5.163, -0.625], [0, 0], [0, 0], [17.817, 2.905], [-0.037, 0.224], [0, 0], [-1.272, 6.89], [0, 0], [-4.575, 0], [0, 0], [-4.077, -5.44], [4.264, -4.919], [0, 0]], "v": [[-26.695, 12.988], [-30.281, -53.437], [-21.656, -63.831], [-10.896, -55.781], [-0.023, -21.544], [32.495, -16.823], [59.495, 18.198], [59.378, 18.87], [51.713, 60.403], [37.428, 72.294], [-12.471, 72.294], [-24.096, 66.479], [-55.324, 19.31], [-54.928, 2.188], [-38.187, 2.393]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 18, "s": [{"i": [[0, 0], [0, 0], [-5.178, 0.627], [-0.857, -5.129], [0, 0], [0, 0], [2.905, -17.817], [0.041, -0.224], [0, 0], [7.005, 0], [0, 0], [2.743, 3.66], [0, 0], [-4.454, 5.136], [-5.093, -4.056]], "o": [[0, 0], [-0.338, -5.204], [5.163, -0.625], [0, 0], [0, 0], [17.817, 2.905], [-0.037, 0.224], [0, 0], [-1.272, 6.89], [0, 0], [-4.575, 0], [0, 0], [-4.077, -5.44], [4.264, -4.919], [0, 0]], "v": [[-26.695, 12.988], [-30.281, -53.437], [-21.656, -63.831], [-10.896, -55.781], [-0.023, -21.544], [32.495, -16.823], [59.495, 18.198], [59.378, 18.87], [51.713, 60.403], [37.428, 72.294], [-12.471, 72.294], [-24.096, 66.479], [-55.324, 19.31], [-54.928, 2.188], [-38.187, 2.393]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [-7.66, 0.928], [-1.268, -7.587], [0, 0], [0, 0], [2.905, -17.817], [0.041, -0.224], [0, 0], [7.005, 0], [0, 0], [2.743, 3.66], [0, 0], [-4.454, 5.136], [-5.093, -4.056]], "o": [[0, 0], [-0.5, -7.699], [7.637, -0.925], [0, 0], [0, 0], [17.817, 2.905], [-0.037, 0.224], [0, 0], [-1.272, 6.89], [0, 0], [-4.575, 0], [0, 0], [-4.077, -5.44], [4.264, -4.919], [0, 0]], "v": [[-27.945, 11.488], [-34.119, -59.75], [-21.359, -75.125], [-5.441, -63.216], [0.477, -24.544], [32.495, -19.323], [59.495, 18.198], [59.378, 18.87], [51.713, 60.404], [37.428, 72.294], [-12.471, 72.294], [-24.096, 66.479], [-58.324, 20.81], [-57.678, 2.688], [-40.937, 1.143]], "c": true}]}, {"t": 24}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 0, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [62.65, 72.545], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 42, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "cycle 3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 16, "s": [0], "e": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 21, "s": [50], "e": [0]}, {"t": 27}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [77.318, 46.543, 0], "ix": 2}, "a": {"a": 0, "k": [36.318, 36.318, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 16, "s": [80, 80, 100], "e": [120, 120, 100]}, {"t": 27}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[15.019, 0], [0, -15.02], [-15.02, 0], [0, 15.019]], "o": [[-15.02, 0], [0, 15.019], [15.019, 0], [0, -15.02]], "v": [[0, -27.238], [-27.239, 0.001], [0, 27.239], [27.238, 0.001]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[20.025, 0], [0, 20.025], [-20.026, 0], [0, -20.026]], "o": [[-20.026, 0], [0, -20.026], [20.025, 0], [0, 20.025]], "v": [[0, 36.319], [-36.318, 0.001], [0, -36.318], [36.318, 0.001]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [36.318, 36.318], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 6, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 16, "op": 42, "st": 16, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "cycle 2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 4, "s": [0], "e": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 9, "s": [50], "e": [0]}, {"t": 15}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [77.318, 46.543, 0], "ix": 2}, "a": {"a": 0, "k": [36.318, 36.318, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 4, "s": [80, 80, 100], "e": [120, 120, 100]}, {"t": 15}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[15.019, 0], [0, -15.02], [-15.02, 0], [0, 15.019]], "o": [[-15.02, 0], [0, 15.019], [15.019, 0], [0, -15.02]], "v": [[0, -27.238], [-27.239, 0.001], [0, 27.239], [27.238, 0.001]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[20.025, 0], [0, 20.025], [-20.026, 0], [0, -20.026]], "o": [[-20.026, 0], [0, -20.026], [20.025, 0], [0, 20.025]], "v": [[0, 36.319], [-36.318, 0.001], [0, -36.318], [36.318, 0.001]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [36.318, 36.318], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 6, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 4, "op": 42, "st": 4, "bm": 0}], "markers": []}