/*
默认主题颜色定义文件
*/

@colors
{
/* 字体颜色 */
	MAIN_TEXT_COLOR: #888888;
    TIMELINE_NAME_HB_GOLDEN_COLOR:#B19462;
	WARN_COLOR: #B71414;


/* 基本配色 -------------------------------------------------------- */
	
	/* 默认灰色文字颜色 */
	DEFAULT_TEXT_GRAY_COLOR:#888888;
	
	/* 默认深灰色文字颜色 */
	DEFAULT_TEXT_DARK_COLOR:#888888;
	
	/* 默认高亮文字颜色 */
	DEFAULT_TEXT_HIGHLIGHT_COLOR:#fff;
	
	/* 默认置灰文字颜色 */
	DEFAULT_TEXT_DISABLED_COLOR:#aaaaaa;
	
	/* 默认按钮或toolbar背景颜色 */
	DEFAULT_TINT_BACKGROUND_COLOR:#000;
	
	/* 默认链接高亮颜色 */
	DEFAULT_LINK_HIGHLIGHT_COLOR:#486497;
    
    /* 通用的下拉背景颜色 */
    DEFAULT_GENERAL_BACKGROUND_COLOR:#2e3132;

    /* 通用的列表HEADER文字颜色 */
    DEFAUTL_TABLE_HEADER_COLOR:#888888;

    /* 通用的列表FOOTER文字颜色 */
    DEFAUTL_TABLE_FOOT_COLOR:#b2b2b2;
    
    /* 默认OPENIM DESC描述文字颜色 */
    DEFAULT_OPENIM_DESC_TEXT_COLOR:#FD8F44;
	
	
/* 通用文字 --------------------------------------------------------- */	

	/* 通用文字高亮颜色 */
	default_text_highlight_color:DEFAULT_TEXT_HIGHLIGHT_COLOR;

	/* 摘要文字颜色 */
	summary_text_color:DEFAULT_TEXT_GRAY_COLOR;
	
	
	/* SECTION标题 */
	
	label_title_text_color:DEFAULT_TEXT_DARK_COLOR;
    
    /* SECTION注释 */
    
	label_note_text_color:DEFAULT_TEXT_DARK_COLOR;
	
/* 通用TABLEVIEW --------------------------------------------------------------------- */
	
	/* CELL标题文本高亮颜色 */
	cell_label_text_highlight_color:DEFAULT_TEXT_HIGHLIGHT_COLOR;
	
	/* CELL值文本颜色 */
	cell_value_text_color:DEFAULT_TEXT_GRAY_COLOR;
	
	/* Cell在disable状态的颜色值 */
	cell_text_disabled_color:#B2B2B2;
	
	/* Cell默认背景色 */
	cell_background_color:#ffffff;
	
/* 通用按钮 --------------------------------------------------------- */
	
	/* 深色按钮置灰文字 */
	btn_dark_text_disabled_color:DEFAULT_TEXT_DISABLED_COLOR;
	
/* 顶部导航 --------------------------------------------------------- */
    
    /* 导航栏前景色 */
    navigator_foreground_black_color:#181818;
    
    /* 这两个颜色已经被用错，新增代码不要使用 */
	/* 顶部导航按钮文字颜色 */
	navigator_btn_text_color:#fff;
	/* 顶部导航按钮文字高亮颜色 */
	navigator_btn_text_highlight_color:#a0a0a0;
	
	/* 顶部导航条按钮默认背景色 */
	navigator_tint_color:DEFAULT_TINT_BACKGROUND_COLOR;
    
    /* 主界面“+”号文字颜色 */
    main_frame_add_title_color:#fff;


	

/* 会话列表 ----------------------------------------------------- */
    
    session_title_desc_color:DEFAULT_OPENIM_DESC_TEXT_COLOR;

	/* 日期 */
	session_date_color:#B2B2B2;

	/* 摘要 #8094a3 */
	session_summary_color:DEFAULT_TEXT_GRAY_COLOR;

	/* 未读语音文字提示 */
	session_unread_color:#aa0000;
    
    /* 置顶cell背景色*/
    message_list_cell_top_color:#F7F7FA;
    
    /*  首页列表时间  */
    new_main_frame_cell_time_color:#B2B2B2;
    /*  首页列表 摘要  */
    new_main_frame_cell_summay_color:#9b9b9b;

/* 聊天界面 --------------------------------------------------------- */
	/* 聊天界面时间文字颜色 */
	message_time_text_color:#fff;

/* 通讯录 ---------------------------------------------------------- */
	
	/* 联系人名高亮颜色 */
	contact_title_text_highlight_color:DEFAULT_TEXT_HIGHLIGHT_COLOR;
	
	/* 附属信息颜色 */
	contact_summary_text_color:DEFAULT_TEXT_GRAY_COLOR;
	
	/* 签名文字颜色 */
	contact_signature_text_color:DEFAULT_TEXT_GRAY_COLOR;
	
	/* 通讯录机器人人名字颜色 */
	contact_plugin_text_color:#4473ac;

/* 个人资料页 ------------------------------------------------------- */
	
	/* 个人资料微信号颜色 */
	profile_uin_color:DEFAULT_TEXT_DARK_COLOR;
	
/* ---------------------------------------------------------------- */		
	
	/*CELL 上提醒文字颜色（默认红色） */
	cell_text_alert_color:#f00;
	
	/* 消息未读数的颜色 */
	badage_text_color:DEFAULT_TEXT_HIGHLIGHT_COLOR;
	
	/* 搜索框背景色 */
	searchbar_background_color:#9a9894;
    
/* --------------------------我的相册颜色-------------------------------------- */
    
    album_username_hb_golden_color : #B19462;
    album_username_hb_golden_color_highlighted : #B19462;
    
    album_I_know_button_shadow_color : #1D5922;
    
    album_text_highlighted_background_color : #C7C7C5;
    
/* --------------------------相册详情页颜色-------------------------------------- */
    album_detail_background_hb_golden_color : #F8F5F1 ;
    
/* --------------------------动态颜色-------------------------------------- */
    wc_link_desc_color : #737373 ;
	
	wc_timeline_comment_hl_color :rgba(87,107,149,0.3);
    
    wc_timeline_comment_hb_hl_golden_color : #F1EADC;
    
/* --------------------------open api 颜色-------------------------------------- */	
    wc_intro_view_bkg_color : rgba(0,0,0,0.8) ;

/* ---------------------------------- */
    /* 摇一摇、扫一扫 切换按钮的文字颜色 */
    TYPE_BUTTON_TITLE_COLOR:#b2b2b2;
    
}
