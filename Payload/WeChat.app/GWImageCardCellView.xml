<View nodeId="content" dynamicSize="2" layout="width:100%,height:100%,flexDirection:column">
    <NetImage layout="width:100%,aspectRatio:16/9,alignContent:center,justifyContent:center" attr="src:{{coverImgUrl}},clipsToBounds:true,contentMode:scaleAspectFill,autoScale:true">
        <View layout="width:100%,height:100%,position:absolute" attr="background:#4C000000" />
        <Image nodeId="playBtn" layout="width:${40},height:${40},alignSelf:center" attr="localImage:chatVideoPlayIconSvg,hidden:{{hidePlayBtn}}"/>
    </NetImage>
    <Label nodeId="desc" layout="marginTop:${7.5}" attr="numberOfLines:2,fontSize:${15},color:FG_0,text:{{desc}},hidden:{{hideDesc}}"/>
</View>
