<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no">
  <meta http-equiv="Content-Security-Policy" content="script-src 'self' 'unsafe-eval' 'unsafe-inline'; worker-src 'self' blob:; frame-src 'self'">
  <style>
    * {
      -webkit-user-drag: none;
      -webkit-user-select: none;
    }
    html, body {
      margin: 0;
      width: 100%;
      height: 100%;
    }
    iframe {
      border: none;
      display: none;
    }
    #canvas, #vConsole {
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      position: fixed;
    }
    #vBtn {
      width: 100px;
      color: #000;
      position: fixed;
      font-size: 12px;
      line-height: 30px;
      border-radius: 15px;
      background: #0f0;
      font-family: PingFang SC;
      text-align: center;
      top: calc(100vh - 50px);
      left: calc(100vw - 120px);
    }
    #canvas {
      z-index: -1;
      display: block;
      /* background-color: #000000; */
    }
    #vBtn, #vConsole {
      z-index: 1000;
    }
  </style>
<body>
  <script>
    /* __wkrenderer */
    /* WAGAME_REPLACE */
    globalThis.debugMode = !!globalThis?.__wxConfig?.debug;
    debugMode && /^unknown/.test(__wxConfig.model) && alert('Simulator Debug');
    /* MAIN_JS */
  </script>
</body>
</html>
