<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no">
  <meta http-equiv="Content-Security-Policy" content="script-src 'self' 'unsafe-eval' 'unsafe-inline'; worker-src 'self' blob:; frame-src 'self'">
  <style>
    * {
      -webkit-user-drag: none;
      -webkit-user-select: none;
    }
    html, body {
      margin: 0;
      width: 100%;
      height: 100%;
    }
    iframe {
      border: none;
      display: none;
    }
    #canvas, #vConsole {
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      position: fixed;
    }
    #vBtn {
      width: 100px;
      color: #000;
      position: fixed;
      font-size: 12px;
      line-height: 30px;
      border-radius: 15px;
      background: #0f0;
      font-family: PingFang SC;
      text-align: center;
      top: calc(100vh - 50px);
      left: calc(100vw - 120px);
    }
    #canvas {
      z-index: -1;
      display: block;
      /* background-color: #000000; */
    }
    #vBtn, #vConsole {
      z-index: 1000;
    }
  </style>
<body>
  <script>
var _globalThis$__wxConfi;

/* __wkrenderer */

/* WAGAME_REPLACE */
globalThis.debugMode = !!(globalThis !== null && globalThis !== void 0 && (_globalThis$__wxConfi = globalThis.__wxConfig) !== null && _globalThis$__wxConfi !== void 0 && _globalThis$__wxConfi.debug);
debugMode && /^unknown/.test(__wxConfig.model) && alert('Simulator Debug');
/* MAIN_JS */

/******/

(() => {
  // webpackBootstrap

  /******/
  "use strict";
  /******/

  var __webpack_modules__ = {
    /***/
    "./src/JSBridge.ts":
    /*!*************************!*\
      !*** ./src/JSBridge.ts ***!
      \*************************/

    /***/
    (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "WeixinJSBridge": () =>
        /* binding */
        WeixinJSBridge
        /* harmony export */

      });

      const mapCallbacks = new Map();
      const mapOnEventCallbacks = new Map();
      let callbackId = 0;
      const WeixinJSBridge = {
        // 公共库调用客户端，触发 JsApi
        invoke(event, param, callback) {
          callbackId += 1;
          mapCallbacks.set(callbackId, callback); // @ts-ignore

          globalThis.WeixinJSCoreIOS.invokeHandler(event, param, callbackId);
        },

        // 客户端回调该接口，触发 JsApi 的异步 callback
        invokeCallbackHandler(id, obj) {
          const callback = mapCallbacks.get(id);

          if (callback) {
            callback(obj);
          }

          mapCallbacks.delete(id);
        },

        on(eventName, callback) {
          if (!callback && mapOnEventCallbacks.has(eventName)) {
            mapOnEventCallbacks.delete(eventName);
          } else {
            mapOnEventCallbacks.set(eventName, callback);
          }
        },

        // 客户端回调接口，触发 onEvent 事件
        subscribeHandler(eventName, params) {
          const callback = mapOnEventCallbacks.get(eventName);

          if (callback) {
            callback(params);
          }
        }

      };
      /***/
    },

    /***/
    "./src/context/index.ts":
    /*!******************************!*\
      !*** ./src/context/index.ts ***!
      \******************************/

    /***/
    (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "SubJsContext": () =>
        /* binding */
        SubJsContext
        /* harmony export */

      });

      class SubJsContext {
        constructor(name = '', env) {
          this.frame = document.createElement('iframe');
          this.name = name;
          this.frame.src = '/SubContextFrame.html';
          this.frame.setAttribute('sandbox', 'allow-scripts allow-same-origin');
          this.frame.name = `${name}Context`;
          this.frame.style.display = 'none';
          document.body.appendChild(this.frame);
          this.createPromise = new Promise((resolve, reject) => {
            this.frame.onload = () => {
              // @ts-ignore
              this.window = Object.assign(this.frame.contentWindow, env);
              resolve();
            };

            this.frame.onerror = reject;
          });
        }

        add(path) {
          return this.createPromise.then(() => {
            const {
              document
            } = this.window;
            const script = document.createElement('script');
            script.src = path;
            return new Promise((resolve, reject) => {
              document.body.appendChild(script);
              script.onload = resolve;
              script.onerror = reject;
            });
          });
        }
        /**
           * 将传入的字符串当做 JavaScript 代码进行执行
           */


        eval(code) {
          return this.createPromise.then(() => new Promise((resolve, reject) => {
            try {
              this.window.eval(code);
              resolve();
            } catch (e) {
              reject(e);
            }
          }));
        } // 销毁 SubJsContext


        destroy() {
          return this.createPromise.finally(() => {
            this.frame.remove();
            return Promise.resolve();
          });
        }

      }
      /***/

    },

    /***/
    "./wk-game/src/jitcore/buffer/abadapter.ts":
    /*!*************************************************!*\
      !*** ./wk-game/src/jitcore/buffer/abadapter.ts ***!
      \*************************************************/

    /***/
    (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "arraybufferDecodeAdapter": () =>
        /* binding */
        arraybufferDecodeAdapter,

        /* harmony export */
        "decodedStringToArrayBuffer": () =>
        /* binding */
        decodedStringToArrayBuffer
        /* harmony export */

      });
      /**
       * 这个模块我也看不懂，有问题找 liangyizhou
       */
      // 被替换的数值需要记录原来的位置，这里每个位置占4个字节，取216进制，就使得位置信息也避开utf16编码不允许的范围


      const ByteReplaceIndexWeight = [Math.pow(216, 3), Math.pow(216, 2), 216, 1]; // 被替换的数值减去这个magicNumber得到替换后的值

      const ByteReplaceMagicNumber = 200; // 转化为216进制的,4个字节表示的数组

      const to216Array = function (index) {
        const mod3 = index % ByteReplaceIndexWeight[0];
        const mod2 = mod3 % ByteReplaceIndexWeight[1];
        return [Math.floor(index / ByteReplaceIndexWeight[0]), Math.floor(mod3 / ByteReplaceIndexWeight[1]), Math.floor(mod2 / ByteReplaceIndexWeight[2]), mod2 % ByteReplaceIndexWeight[2]];
      };

      const decodedStringToArrayBuffer = function (decodedString) {
        let len;
        let bytes;

        if (decodedString instanceof ArrayBuffer) {
          bytes = new Uint8Array(decodedString);
          len = bytes.length;
        } else {
          // TextEncoder not support non-utf-8 format yet, so still need to encode with Base64
          const binary_string = window.atob(decodedString);
          len = binary_string.length;
          bytes = new Uint8Array(len);

          for (let i = 0; i < len; i++) {
            bytes[i] = binary_string.charCodeAt(i);
          }
        } // 后8个为标识符,如果是 11+index说明做了数值替换，参见pack那段，_new_native的反解析


        if (bytes.slice(-8).every((byte, index) => 11 + index === byte)) {
          // console.warn('arraybuffer is written by old wxapplib')
          let i = 1;
          const extraTailLength = 12; // 额外信息尾部的长度，其中后8为标识位，剩余4个字节为count，记录有多少个数值被替换了

          const count = bytes.slice(-12, -8).reduce((total, num, index) => total + num * ByteReplaceIndexWeight[index], 0); // 每个替换节点信息占6个字节

          const itemLength = 6;

          while (i <= count) {
            const group = len - extraTailLength - i * itemLength;
            const a1 = bytes[group];
            const a2 = bytes[group + 1] + ByteReplaceMagicNumber;
            const index0 = bytes[group + 2];
            const index1 = bytes[group + 3];
            const index2 = bytes[group + 4];
            const index3 = bytes[group + 5]; // index 计算出被替换的数值的位置

            const index = index0 * ByteReplaceIndexWeight[0] + index1 * ByteReplaceIndexWeight[1] + index2 * ByteReplaceIndexWeight[2] + index3; // 还原被替换的数值

            bytes[index] = a1;
            bytes[index + 1] = a2;
            i++;
          } // 还原完占替换信息后，前面还有一串0填充来让长度再编码范围内，这里检查填充是否为0,fillCount 表示有多少个0，数值由两个字节拼接而成


          const fillCount = (bytes[len - extraTailLength - (i - 1) * itemLength - 1] << 8) + bytes[len - extraTailLength - (i - 1) * itemLength - 2]; // 如果填充的不为0就说明是没处理过的，直接返回原来的内容

          for (let k = 1; k <= fillCount; k++) {
            if (bytes[len - extraTailLength - (i - 1) * itemLength - 2 - k] !== 0) {
              return bytes.buffer;
            }
          }

          return bytes.slice(0, len - extraTailLength - (i - 1) * itemLength - fillCount - 2).buffer;
        } else if (bytes.slice(-8).every((byte, index) => 21 + index === byte)) {
          // 后8个为标识符
          const fillCount = (bytes[len - 9] << 8) + bytes[len - 10]; // 如果填充的不为0就说明是没处理过的

          for (let k = 1; k <= fillCount; k++) {
            if (bytes[len - 10 - k] !== 0) {
              return bytes.buffer;
            }
          }

          return bytes.slice(0, len - 8 - fillCount - 2).buffer;
        }

        return bytes.buffer;
      };

      const arraybufferDecodeAdapter = function (buffer) {
        const inputBuffer = new Uint8Array(buffer).slice();
        const length = inputBuffer.byteLength % 2 === 0 ? inputBuffer.byteLength : inputBuffer.byteLength - 1;
        const alterArray = [];
        let count = 0;

        for (let i = 0; i < length; i = i + 2) {
          // 从U+D800到U+DFFF之间的码位区段是永久保留不映射到Unicode字符,所以这里做下替换
          if (inputBuffer[i + 1] >= 0xd8 && inputBuffer[i + 1] <= 0xdf) {
            // 这里为替换的信息，每个item占6个字节
            alterArray.push( // 前2个为替换后的数值
            inputBuffer[i], inputBuffer[i + 1] - ByteReplaceMagicNumber, // 后面4个为位置信息，用216进制，4个字节表示
            ...to216Array(i)); // 将原来的信息改为0，避免编码错误

            inputBuffer[i] = 0;
            inputBuffer[i + 1] = 0;
            count++;
          }
        }

        if (count > 0) {
          alterArray.push(...to216Array(count), // 后8个为标识字节
          11, 12, 13, 14, 15, 16, 17, 18);
        } else {
          // 后8个为标识字节
          alterArray.push(21, 22, 23, 24, 25, 26, 27, 28);
        }

        const totalLength = inputBuffer.byteLength + alterArray.length; // 长度也会走utf16编码,所以这里也要做下扩充长度

        const lowCount = totalLength & 0xffff;
        let fillCount = 0;

        if (lowCount >= 0xd800 && lowCount <= 0xdfff) {
          fillCount = 0xe000 - lowCount;
        }

        if ((inputBuffer.byteLength + fillCount) % 2 === 1) {
          // 凑偶数
          fillCount += 1;
        }

        const managed = new Uint8Array(inputBuffer.byteLength + fillCount + 2 + alterArray.length); // 被处理过后的数据

        managed.set(inputBuffer); // 填充0的长度

        managed[inputBuffer.byteLength + fillCount] = fillCount & 0xff; // 低位

        managed[inputBuffer.byteLength + fillCount + 1] = fillCount >> 8; // 高位
        // 记录替换的信息追加在后面

        managed.set(alterArray, inputBuffer.byteLength + fillCount + 2);
        return managed;
      };
      /***/

    },

    /***/
    "./wk-game/src/jitcore/buffer/commandbuffer.ts":
    /*!*****************************************************!*\
      !*** ./wk-game/src/jitcore/buffer/commandbuffer.ts ***!
      \*****************************************************/

    /***/
    (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "CommandBufferClass": () =>
        /* binding */
        CommandBufferClass
        /* harmony export */

      });
      /* harmony import */


      var _util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(
      /*! ../../util */
      "./wk-game/src/util.ts");

      const unicodeEncoder = new TextDecoder('unicode');
      /* 对 Text 来说是 Decoder，对 Command 来说是 Encoder */

      const utf8Eecoder = new TextEncoder();
      const UINT16_MAX_LENGTH = 65536;
      const UINT32_MAX_LENGTH = 65536 * 65536;
      const DATA_SEGMENT_HEADER_BYTE_LENGTH = 6; // id: 2bytes ; bytelength: 4bytes

      const BASIC_BYTE_LENGTH = 1024 * 1024 * 2;

      class CommandBufferClass {
        constructor() {
          this.dataSegmentExpandTimes = 0;
          /**
           * code段由一系列用反引号分隔的指令构成，其中每个指令的第一个数字为指令编号
           */

          this.codeSegment = '';
          /**
           * data段（arraybuffer形式，用于传输指令中用到的arraybuffer）
           */

          this.dataSegment = new ArrayBuffer(0);
          this.dataSegmentUint16 = new Uint16Array(this.dataSegment);
          this.dataSegmentUint8 = new Uint8Array(this.dataSegment);
          this.dataSegmentByteIndex = 2; // 用 2 个 byte 表示头部，标明共有多少段数据

          this.dataSegmentId = 0; // 日志输出函数

          this.logger = null;
        }

        allocBuffer() {
          this.dataSegment = new ArrayBuffer(BASIC_BYTE_LENGTH);
          this.dataSegmentUint16 = new Uint16Array(this.dataSegment);
          this.dataSegmentUint8 = new Uint8Array(this.dataSegment);
        }

        clearBuffer() {
          this.dataSegment = new ArrayBuffer(0);
          this.dataSegmentUint16 = new Uint16Array(this.dataSegment);
          this.dataSegmentUint8 = new Uint8Array(this.dataSegment);
        }
        /**
         * 如果当前数据段不足以容纳新数据，扩展数据段，并将旧数据拷贝到新数据段中
         * 扩展方法是，当前数据段的 byteLength * 2^n，直到满足需求
         */


        expandDataSegmentIfNeeded(neededByteLength) {
          const comsumedByteLength = this.dataSegmentByteIndex;
          const totalByteLength = this.dataSegment.byteLength;

          if (totalByteLength - comsumedByteLength >= neededByteLength) {
            return;
          } // 尝试拓展数据段


          let newTotalByteLength = this.dataSegment.byteLength * 2; // 如果不够，持续拓展

          while (newTotalByteLength - comsumedByteLength < neededByteLength) {
            newTotalByteLength *= 2;
          }
          /**
           * 记录下扩展的次数，可以考虑加个 log
           */


          this.dataSegmentExpandTimes += 1;
          const newDataSegment = new ArrayBuffer(newTotalByteLength);
          const newDataSegmentUint8 = new Uint8Array(newDataSegment);
          const newDataSegmentUint16 = new Uint16Array(newDataSegment);
          /**
           * 将旧的数据拷贝到新的 arraybuffer
           */

          if (comsumedByteLength > 0) {
            const oldDataSegment = new Uint8Array(this.dataSegment, 0, this.dataSegmentByteIndex);
            newDataSegmentUint8.set(oldDataSegment);
          }

          this.dataSegment = newDataSegment;
          this.dataSegmentUint8 = newDataSegmentUint8;
          this.dataSegmentUint16 = newDataSegmentUint16;
        }
        /**
         * 将数据放入 CommandBuffer，返回 Buffer 在数据段
         * @param ab {ArrayBuffer|ArrayBufferView}
         * @return buffer id, start with 1
         */


        pushDataSegment(ab) {
          if (!ab) {
            return;
          }

          if (ab.byteLength > UINT32_MAX_LENGTH) {
            throw new Error(`ArrayBuffer too large! [${ab.byteLength}]`);
          }

          if (this.dataSegmentByteIndex % 2 !== 0) {
            // debug
            throw new Error(`CommandBuffer internal error. dataSegmentByteIndex % 2 !== 0 [${this.dataSegmentByteIndex}]`);
          }

          const id = ++this.dataSegmentId;

          if (id > UINT16_MAX_LENGTH) {
            throw new Error(`CommandBuffer internal error. dataSegmentId too large. [${id}]`);
          } // prepare


          const neededByteLength = (ab.byteLength % 2 === 0 ? ab.byteLength : ab.byteLength + 1) + DATA_SEGMENT_HEADER_BYTE_LENGTH;

          if (this.logger) {
            // @ts-ignore
            this.logger.log(neededByteLength);
          }

          this.expandDataSegmentIfNeeded(neededByteLength); // write header

          const uint16StartIndex = this.dataSegmentByteIndex / 2;
          this.dataSegmentUint16[uint16StartIndex] = id; // header id

          this.dataSegmentUint16[uint16StartIndex + 1] = ab.byteLength >> 16; // header length; 高 16 位

          this.dataSegmentUint16[uint16StartIndex + 2] = ab.byteLength & 0xffff; // header length; 低 16 位
          // write data

          let abv;

          if (ArrayBuffer.isView(ab)) {
            abv = new Uint8Array(ab.buffer, ab.byteOffset, ab.byteLength);
          } else {
            abv = new Uint8Array(ab);
          }

          this.dataSegmentUint8.set(abv, this.dataSegmentByteIndex + DATA_SEGMENT_HEADER_BYTE_LENGTH);
          this.dataSegmentByteIndex += neededByteLength;
          return id;
        } // 将代码段化为 arraybuffer


        encodeCodeSegment() {
          const ab = utf8Eecoder.encode(this.codeSegment);
          const result = new ArrayBuffer(4 + ab.byteLength);
          new DataView(result).setUint32(0, ab.byteLength, true);
          new Uint8Array(result).set(ab, 4);
          return result;
        }

        pushCodeSegment(command) {
          this.codeSegment += command;
        }
        /**
         * code段由一系列用反引号分隔的指令构成，其中每个指令的第一个数字为指令编号、如这里的 888 意为 JsApi 调用
         * 每个指令号后面跟着的参数数量可能不同，这里属于前端和客户端的约定，比如 JSApi 场景下，包含4个分隔符 `
         * 客户端会按需从 codeSegment字符串取出需要的信息
         */


        pushInvokeSegment(event, id, param) {
          const len = (0, _util__WEBPACK_IMPORTED_MODULE_0__.getUtf8Length)(param);
          this.codeSegment += '`888`' + event + '`' + id + '`' + len + '`' + param;
        }
        /**
         * 将数据段序列化为 string，这个方式其实有坑：
         * 在把data转为字符串的过程中，如果出现unicode不能表示的数据，转换出来的结果会有变化
         * 所以后续这个方案会慢慢废弃
         */


        encodeDataSegment() {
          // 一个总长度，每个ab一个id，一个长度，所以乘以2
          this.dataSegmentUint16[0] = this.dataSegmentId;
          return unicodeEncoder.decode(new Uint8Array(this.dataSegment, 0, this.dataSegmentByteIndex));
        }

        toString() {
          const data = this.encodeDataSegment();
          return data + this.codeSegment;
        }
        /**
         * 按照 JSAPI 全量 xhr 的设计，code 端和 data 段都会统一转换成 ArrayBuffer，避免特殊字符问题
         * 首先会将 code 段转换成 ArrayBuffer，然后和 data 段拼成一个完整的 ArrayBuffer
         */


        toArrayBuffer() {
          this.dataSegmentUint16[0] = this.dataSegmentId;
          const codeArrayBuffer = this.encodeCodeSegment();
          const uint8CodeArray = new Uint8Array(codeArrayBuffer);
          const data = new ArrayBuffer(codeArrayBuffer.byteLength + this.dataSegmentByteIndex);
          const uint8Data = new Uint8Array(data);
          uint8Data.set(uint8CodeArray, 0);
          uint8Data.set(this.dataSegmentUint8.slice(0, this.dataSegmentByteIndex), uint8CodeArray.length);
          return data;
        }

        clear() {
          this.codeSegment = '';
          this.dataSegmentByteIndex = 2;
          this.dataSegmentId = 0; // 对于之前拓展的 buffer，在clear的时候需要缩回去，否则这部分内存会一直内占用

          if (this.dataSegment.byteLength > BASIC_BYTE_LENGTH) {
            this.dataSegment = new ArrayBuffer(BASIC_BYTE_LENGTH);
            this.dataSegmentUint16 = new Uint16Array(this.dataSegment);
            this.dataSegmentUint8 = new Uint8Array(this.dataSegment);
          }
        }

        flush() {
          console.log('[CommandBuffer] flush is empty!');
        }

        flushSync() {
          console.log('[CommandBuffer] flushSync is empty!');
        }

      }
      /***/

    },

    /***/
    "./wk-game/src/jitcore/buffer/ipc.ts":
    /*!*******************************************!*\
      !*** ./wk-game/src/jitcore/buffer/ipc.ts ***!
      \*******************************************/

    /***/
    (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "default": () => __WEBPACK_DEFAULT_EXPORT__
        /* harmony export */

      });

      class IPC {
        // 同步传输
        sync(cmd, dataString) {
          const result = prompt(cmd, dataString);
          return result;
        } // 异步传输


        async(dataString) {
          if (!dataString) {
            return;
          } // @ts-ignore


          return globalThis.webkit.messageHandlers.WeixinWeAppMessage.postMessage(dataString);
        }

        asyncInvoke(data) {
          // @ts-ignore
          globalThis.webkit.messageHandlers.WeixinWeAppInvoke.postMessage(data);
        }

      }
      /* harmony default export */


      const __WEBPACK_DEFAULT_EXPORT__ = new IPC();
      /***/

    },

    /***/
    "./wk-game/src/jitcore/buffer/jsapicommandbuffer.ts":
    /*!**********************************************************!*\
      !*** ./wk-game/src/jitcore/buffer/jsapicommandbuffer.ts ***!
      \**********************************************************/

    /***/
    (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "default": () => __WEBPACK_DEFAULT_EXPORT__
        /* harmony export */

      });
      /* harmony import */


      var _commandbuffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(
      /*! ./commandbuffer */
      "./wk-game/src/jitcore/buffer/commandbuffer.ts");
      /* harmony import */


      var _ipc__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(
      /*! ./ipc */
      "./wk-game/src/jitcore/buffer/ipc.ts");

      class JsApiCommandBuffer extends _commandbuffer__WEBPACK_IMPORTED_MODULE_0__.CommandBufferClass {
        flush() {
          if (this.codeSegment) {
            const str = this.toString();

            _ipc__WEBPACK_IMPORTED_MODULE_1__["default"].async(str);

            this.clear();
          }
        }

      }
      /* harmony default export */


      const __WEBPACK_DEFAULT_EXPORT__ = new JsApiCommandBuffer();
      /***/

    },

    /***/
    "./wk-game/src/jitcore/buffer/nativebuffer.ts":
    /*!****************************************************!*\
      !*** ./wk-game/src/jitcore/buffer/nativebuffer.ts ***!
      \****************************************************/

    /***/
    (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "GfxObjectHandler": () =>
        /* binding */
        GfxObjectHandler,

        /* harmony export */
        "NativeBuffer": () =>
        /* binding */
        NativeBuffer,

        /* harmony export */
        "arrayBufferToNativeBuffer": () =>
        /* binding */
        arrayBufferToNativeBuffer,

        /* harmony export */
        "batchUnpackNativeBuffer": () =>
        /* binding */
        batchUnpackNativeBuffer,

        /* harmony export */
        "bufferToBase64": () =>
        /* binding */
        bufferToBase64,

        /* harmony export */
        "pack": () =>
        /* binding */
        pack,

        /* harmony export */
        "unpack": () =>
        /* binding */
        unpack,

        /* harmony export */
        "unpackNativeBuffer": () =>
        /* binding */
        unpackNativeBuffer
        /* harmony export */

      });
      /* harmony import */


      var _jsapicommandbuffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(
      /*! ./jsapicommandbuffer */
      "./wk-game/src/jitcore/buffer/jsapicommandbuffer.ts");
      /* harmony import */


      var _xhrjsapicommandbuffer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(
      /*! ./xhrjsapicommandbuffer */
      "./wk-game/src/jitcore/buffer/xhrjsapicommandbuffer.ts");
      /* harmony import */


      var _util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(
      /*! ../../util */
      "./wk-game/src/util.ts");
      /* harmony import */


      var _abadapter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(
      /*! ./abadapter */
      "./wk-game/src/jitcore/buffer/abadapter.ts");
      /* harmony import */


      var _xhr__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(
      /*! ./xhr */
      "./wk-game/src/jitcore/buffer/xhr.ts");

      const GfxObjectHandler = {
        FakeId: 0,

        // 1: Pass
        // 2: GfxBuffer
        // 3: GfxImage
        // 4: Shader
        // 5: Pipeline
        // 6: NativeBuffer
        // @ts-ignore
        DestroyObject(type, obj) {
          _jsapicommandbuffer__WEBPACK_IMPORTED_MODULE_0__["default"].pushCodeSegment('`23`' + type + '`' + obj.id);
        }

      };

      class NativeBuffer {
        /**
         * buffer 如果是 int，则代表 byteLength
         * buffer 如果是 ArrayBuffer 或 ArrayBufferView:
         * 如果 borrow = false，后面创建一个 NativeBuffer，并将 ArrayBuffer 复制到 buffer 中
         * 如果 borrow = true，直接使用数据段中的数据，生命周期只持续到本次 CommandBuffer 结束
         * buffer 如果是 undefined，则创建一个空壳，走其他 init 方法
         */
        constructor(commandbuffer, buffer, borrow) {
          this.commandBuffer = commandbuffer;

          if (Number.isInteger(buffer)) {
            this.initWithLength(buffer);
          } else if (buffer instanceof ArrayBuffer || ArrayBuffer.isView(buffer)) {
            if (!borrow) {
              this.initWithLength(buffer.byteLength);
              this.set(buffer);
            } else {
              // 只是为了统一格式，将 DataSegment 包装成一个 NativeBuffer
              // 只是临时用一下
              this.initWithBufferBorrow(buffer);
            }
          }
        }

        initWithLength(byteLength) {
          const id = ++GfxObjectHandler.FakeId;
          this._id = id;
          this.byteLength = byteLength;
          this.isBorrow = false;
          this.commandBuffer.pushCodeSegment('`26`1`' + id + '`' + byteLength);
        } // @ts-ignore


        initWithBufferBorrow(buffer) {
          const id = ++GfxObjectHandler.FakeId;
          this._id = id;
          this.byteLength = buffer.byteLength;
          this.isBorrow = true;
          const sourceId = this.commandBuffer.pushDataSegment(buffer); // 指令26的作用为调用到客户端 createNativeBuffer

          this.commandBuffer.pushCodeSegment('`26`2`' + id + '`' + sourceId);
        }

        get length() {
          return this.byteLength;
        }

        get id() {
          return this._id;
        } // destOffset 是将 value 复制到 dest 的哪个位置


        set(value, destOffset = 0) {
          if (this.byteLength <= 0) {
            throw new Error(`NativeBuffer: Params error. current byteLength <= 0 ${this.byteLength}`);
          }

          if (destOffset + value.byteLength > this.byteLength) {
            throw new Error(`NativeBuffer: Params error. offset + byteLength > this.byteLength ` + `${destOffset} ${value.byteLength} ${this.byteLength}`);
          }

          const sourceId = this.commandBuffer.pushDataSegment(value); // 指令27的作用为调用到客户端 setNativeBuffer

          this.commandBuffer.pushCodeSegment('`27`' + this._id + '`' + sourceId + '`' + destOffset);
        }
        /**
         * 如果是 ArrayBuffer，就将其适配为 NativeBuffer
         * @TODO: gfx 也有相关调用
         */


        static ConvertIfNeededNonNull(input, commandbuffer) {
          let buffer; // 历史遗留代码，不知道用来干啥的，最好不要删

          let shouldDestroy = false;

          if (ArrayBuffer.isView(input) || input instanceof ArrayBuffer) {
            // adapte to nativebuffer
            buffer = new NativeBuffer(commandbuffer, input, true);
            shouldDestroy = true;
          } else {
            throw new Error('params type not accepted.');
          }

          return {
            id: buffer.id,
            buffer,
            shouldDestroy
          };
        } // for gfx，暂时可以不关注
        // @ts-ignore


        static PurgeConvertResultIfNeeded(
        /* Nullable*/
        result) {
          if (result === null || result === void 0 ? void 0 : result.shouldDestroy) {
            result.buffer.destroy();
          }
        } // for gfx，暂时可以不关注
        // @ts-ignore


        static ConvertSupported(input) {
          if (!input) {
            return false;
          }

          return input instanceof ArrayBuffer || ArrayBuffer.isView(input) || input instanceof NativeBuffer;
        } // for gfx，暂时可以不关注


        get() {
          if (this.byteLength <= 0) {
            return undefined;
          }

          this.commandBuffer.pushCodeSegment('`28`' + this._id);
          const str = this.commandBuffer.flushSync(); // @ts-ignore

          const uint16 = new Uint16Array(str.length); // @ts-ignore

          for (let i = 0, length = str.length; i < length; i++) {
            // @ts-ignore
            uint16[i] = str.charCodeAt(i);
          }

          return uint16.buffer;
        } // for gfx，暂时可以不关注


        destroy() {
          GfxObjectHandler.DestroyObject(6, {
            id: this._id
          });
        }

        static InitWithId(bufferId) {
          const ret = new NativeBuffer(_jsapicommandbuffer__WEBPACK_IMPORTED_MODULE_0__["default"]);
          ret._id = bufferId;
          ret.byteLength = 0;
          ret.isBorrow = false;
          ret.isFreeze = true;
          return ret;
        }

      } // #jsapi handle utils start


      const bufferToBase64 = function (buffer) {
        const str = btoa(new Uint8Array(buffer).reduce((data, byte) => data + String.fromCharCode(byte), ''));
        return {
          encodedBufferString: str
        };
      };

      const arrayBufferToNativeBuffer = function (buffer) {
        let nativeBuffer;

        if (_util__WEBPACK_IMPORTED_MODULE_2__.supportUseXhrForAllJSAPI) {
          nativeBuffer = NativeBuffer.ConvertIfNeededNonNull(new Uint8Array(buffer), _xhrjsapicommandbuffer__WEBPACK_IMPORTED_MODULE_1__["default"]);
        } else {
          const uint8Buffer = (0, _abadapter__WEBPACK_IMPORTED_MODULE_3__.arraybufferDecodeAdapter)(buffer);
          nativeBuffer = NativeBuffer.ConvertIfNeededNonNull(uint8Buffer, _jsapicommandbuffer__WEBPACK_IMPORTED_MODULE_0__["default"]);
        }

        const ret = {
          id: nativeBuffer.id
        };
        return ret;
      };
      /**
       * 将 JsApi 的参数打包成 NativeBuffer 的格式
       */


      const pack = function (params, base64) {
        if (!params) {
          return params;
        } //@ts-ignore


        const nativeBuffers = [];
        /**
         * 在JsApi调用中，根据之前基础库和客户端的约定，如果存在 arraybuffer 数据需要传输是通过 NativeBuffer 的方式
         * 这个arraybuffer应该位于JsApi请求或返回值json的顶层，因此对 params 的一级属性做一次遍历
         */

        Object.keys(params).forEach(key => {
          const value = params[key];

          if ((0, _util__WEBPACK_IMPORTED_MODULE_2__.isValidArrayBuffer)(value)) {
            /**
             * prompt 调用需要转换成 base64 格式
             */
            const buffer = base64 ? bufferToBase64(value) : arrayBufferToNativeBuffer(value);
            buffer.key = key;
            /**
             * 如果该字段（假设字段key为data) 是 arraybuffer，则为其分配一个id，和它的key一起统一放入 __nativeBuffers__ 字段中
             * 例如调用参数 {path: 'abc', data: arraybuffer } 会在传输前处理为:
             * {path:'abc', __nativeBuffers__: [{id: 1, key: 'data'}]}
             */

            nativeBuffers.push(buffer);
            /**
             * 原本的 data 也会被删除，因为在创建 NativeBuffer 的时候，已经拼接到 CommandBuffer 的 data 字段中了
             * 经过 pack，params 已经转换成纯粹的调用信息，最终被放入 CommandBuffer 的 code 段。
             * 接收方根据id作为索引找到相应的 NativeBuffer 还原回原始的请求参数
             */

            delete params[buffer.key];
          }
        });

        if (nativeBuffers.length) {
          // @ts-ignore
          params.__nativeBuffers__ = nativeBuffers;
        }

        return params;
      }; // prompt 模式使用


      const unpack = function (params) {
        if (!params || !params.__nativeBuffers__) {
          return params;
        }

        const nativeBuffers = params.__nativeBuffers__;
        delete params.__nativeBuffers__;

        for (let i = 0; i < nativeBuffers.length; i++) {
          const buffer = nativeBuffers[i];

          if (!buffer) {
            continue;
          }

          const arrayBuffer = (0, _abadapter__WEBPACK_IMPORTED_MODULE_3__.decodedStringToArrayBuffer)(buffer.base64);

          if ((0, _util__WEBPACK_IMPORTED_MODULE_2__.isValidArrayBuffer)(arrayBuffer)) {
            params[buffer.key] = arrayBuffer;
          }
        }

        return params;
      };

      const unpackNativeBuffer = function (params) {
        return new Promise(resolve => {
          if (!params || !params.__nativeBuffers__) {
            resolve(params);
            return;
          }

          const nativeBuffers = params.__nativeBuffers__;
          delete params.__nativeBuffers__; // console.log('nativeBuffers', nativeBuffers)

          const xhrNativeBuffers = [];
          const xhrNativeBufferIds = [];

          for (let i = 0; i < nativeBuffers.length; i++) {
            const buffer = nativeBuffers[i];

            if (!buffer) {
              continue;
            }

            if (buffer.base64) {
              const arrayBuffer = (0, _abadapter__WEBPACK_IMPORTED_MODULE_3__.decodedStringToArrayBuffer)(buffer.base64);

              if ((0, _util__WEBPACK_IMPORTED_MODULE_2__.isValidArrayBuffer)(arrayBuffer)) {
                // @ts-ignore
                params[buffer.key] = arrayBuffer;
              }
            } else if (buffer.id) {
              xhrNativeBuffers.push(buffer);
              xhrNativeBufferIds.push(buffer.id);
            }
          } // console.log('xhrNativeBufferIds', xhrNativeBufferIds)


          if (xhrNativeBufferIds.length) {
            _xhr__WEBPACK_IMPORTED_MODULE_4__["default"].getNativeBuffers(xhrNativeBufferIds, buffers => {
              for (let i = 0; i < xhrNativeBuffers.length; i++) {
                const buffer = xhrNativeBuffers[i]; // @ts-ignore

                params[buffer.key] = buffers[buffer.id];
              }

              resolve(params);
            });
          } else {
            resolve(params);
          }
        });
      };

      const batchUnpackNativeBuffer = function (events) {
        return new Promise(resolve => {
          // {buffer.id: params, ...}
          const nativeBufferIdToParams = {};

          for (let i = 0; i < events.length; i++) {
            const params = events[i].eventParam;

            if (!params || !params.__nativeBuffers__) {
              continue;
            }

            const nativeBuffers = params.__nativeBuffers__;

            for (let j = 0; j < nativeBuffers.length; j++) {
              const buffer = nativeBuffers[j];

              if (!buffer) {
                continue;
              }

              if (buffer.base64) {
                const arrayBuffer = (0, _abadapter__WEBPACK_IMPORTED_MODULE_3__.decodedStringToArrayBuffer)(buffer.base64);

                if ((0, _util__WEBPACK_IMPORTED_MODULE_2__.isValidArrayBuffer)(arrayBuffer)) {
                  params[buffer.key] = arrayBuffer;
                }
              } else if (buffer.id) {
                nativeBufferIdToParams[buffer.id] = params;
              }
            }
          }

          const ids = Object.keys(nativeBufferIdToParams).map(value => parseInt(value, 10));

          if (ids === null || ids === void 0 ? void 0 : ids.length) {
            _xhr__WEBPACK_IMPORTED_MODULE_4__["default"].getNativeBuffers(ids, buffers => {
              for (const [bufferId, nativeBuffer] of Object.entries(buffers)) {
                const params = nativeBufferIdToParams[bufferId];
                const nativeBuffers = params.__nativeBuffers__;

                for (let j = 0; j < nativeBuffers.length; j++) {
                  const buffer = nativeBuffers[j]; // console.log('buffer', buffer, bufferId, nativeBuffer, buffer.id)

                  if (buffer.id === parseInt(bufferId, 10)) {
                    params[buffer.key] = nativeBuffer;
                  }
                }
              }

              for (const params of Object.values(nativeBufferIdToParams)) {
                // @ts-ignore
                delete params.__nativeBuffers__;
              }

              resolve(events);
            });
          } else {
            resolve(events);
          }
        });
      };
      /***/

    },

    /***/
    "./wk-game/src/jitcore/buffer/xhr.ts":
    /*!*******************************************!*\
      !*** ./wk-game/src/jitcore/buffer/xhr.ts ***!
      \*******************************************/

    /***/
    (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "default": () => __WEBPACK_DEFAULT_EXPORT__
        /* harmony export */

      });
      /* harmony import */


      var _abadapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(
      /*! ./abadapter */
      "./wk-game/src/jitcore/buffer/abadapter.ts");

      const xhr = new XMLHttpRequest();
      const url = '/JsApi/';

      class XHR {
        constructor() {
          this.decoder = new TextDecoder('utf-8');
          this.waitingResults = 0;
        }
        /**
         * 这里会核心依赖于 XHR open 方法的 async 参数：
         * 如果值为false，send()方法直到收到答复前不会返回，该feature其实有风险，详情可见：
         * https://developer.mozilla.org/zh-CN/docs/Web/API/XMLHttpRequest/open
         */


        syncJsApiCommandBuffer(dataSeg) {
          // xhr.responseType = 'arraybuffer'
          xhr.open('POST', url, false);
          xhr.setRequestHeader('content-type', 'application/arraybuffer');
          xhr.setRequestHeader('timestamp', `${Date.now()}`);
          xhr.responseType = 'arraybuffer'; // xhr.send(dataSeg);

          xhr.send(new Uint8Array(dataSeg));
          return this.handlerResult(xhr);
        }
        /**
         * for WebGL，可以暂时不关注
         */


        asyncCommandBuffer(dataSeg, isWebGL) {
          const xhr = new XMLHttpRequest();
          const url = isWebGL ? '/WebGLCommandBuffer/' : '/CommandBuffer/';
          xhr.open('POST', url, false);
          xhr.setRequestHeader('content-type', 'application/arraybuffer');
          xhr.setRequestHeader('timestamp', `${Date.now()}`);
          xhr.responseType = 'arraybuffer'; // xhr.send(dataSeg);

          xhr.send(new Uint8Array(dataSeg));
        }

        handlerResult(xhr) {
          const resultString = xhr.getResponseHeader('api-result');

          if (!resultString) {
            return;
          }

          const resData = xhr.response;
          const resStrLen = parseInt(resultString, 10);
          const alignedResStrLen = resStrLen % 2 === 1 ? resStrLen + 1 : resStrLen;
          const resStrData = new Uint8Array(resData, 0, resStrLen);
          const results = JSON.parse(this.decoder.decode(resStrData));
          const arrBuffers = [];
          const resultCount = results.length;

          if (resultCount <= 0) {
            return;
          }

          const arrayBufferMeta = results[0];
          const arrayBufferMetaData = JSON.parse(arrayBufferMeta);

          for (let i = 0; i < arrayBufferMetaData.length; i++) {
            const meta = arrayBufferMetaData[i];
            const ab = resData.slice(alignedResStrLen + meta[0], alignedResStrLen + meta[0] + meta[1]);
            arrBuffers.push(ab);
          }

          for (let resultIndex = 1; resultIndex < resultCount; resultIndex++) {
            const result = results[resultIndex][1];
            const buffers = result.__nativeBuffers__;

            if (buffers && buffers.length > 0 && arrBuffers.length > 0) {
              for (let i = 0; i < buffers.length; i++) {
                const buf = buffers[i];
                /**
                 * 老版本的基础库，因为只能用 postMessage 的方式传数据到客户端，所以 ArrayBuffer 会转成 string，
                 * 这也会导致一些问题，详情可见 abadapter 模块，这也就埋下一个坑，如果是用老的方式写的，用新的方式来读就会出现读写不一致的问题
                 * 因此这里就必须要做一个擦屁股的事情，如果是读文件的 API，去做一下判断，是否为老的方式写进去的，如果是，则做一个反解
                 */

                if (result.errMsg && result.errMsg.indexOf('readFile:ok') > -1) {
                  result[buf.key] = (0, _abadapter__WEBPACK_IMPORTED_MODULE_0__.decodedStringToArrayBuffer)(arrBuffers[buf.id]);
                } else {
                  result[buf.key] = arrBuffers[buf.id];
                }
              }

              delete result.__nativeBuffers__;
            }
          }

          return results.slice(1);
        }

        handleGetNativeBuffersResult(xhr) {
          const arrayBufferMeta = xhr.getResponseHeader('api-result');
          const arrBuffers = [];
          const resData = xhr.response;

          if (!arrayBufferMeta) {
            arrBuffers.push(resData);
          } else {
            const arrayBufferMetaData = JSON.parse(arrayBufferMeta);

            for (let i = 0; i < arrayBufferMetaData.length; i++) {
              const meta = arrayBufferMetaData[i];
              const ab = resData.slice(meta[0], meta[0] + meta[1]);
              arrBuffers.push(ab);
            }
          }

          return arrBuffers;
        }

        getNativeBuffers(nativeBufferIds, callback) {
          const xhr = new XMLHttpRequest();
          xhr.open('POST', '/get_buffer/', true);
          xhr.setRequestHeader('content-type', 'application/json');
          xhr.responseType = 'arraybuffer';

          xhr.onload = () => {
            const buffers = this.handleGetNativeBuffersResult(xhr);

            if (buffers) {
              const result = {};

              for (let i = 0; i < nativeBufferIds.length; i++) {
                result[nativeBufferIds[i]] = buffers[i];
              }

              callback(result);
            }
          };

          xhr.send(JSON.stringify(nativeBufferIds));
        }

      }
      /* harmony default export */


      const __WEBPACK_DEFAULT_EXPORT__ = new XHR();
      /***/

    },

    /***/
    "./wk-game/src/jitcore/buffer/xhrjsapicommandbuffer.ts":
    /*!*************************************************************!*\
      !*** ./wk-game/src/jitcore/buffer/xhrjsapicommandbuffer.ts ***!
      \*************************************************************/

    /***/
    (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "default": () => __WEBPACK_DEFAULT_EXPORT__
        /* harmony export */

      });
      /* harmony import */


      var _commandbuffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(
      /*! ./commandbuffer */
      "./wk-game/src/jitcore/buffer/commandbuffer.ts");
      /* harmony import */


      var _xhr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(
      /*! ./xhr */
      "./wk-game/src/jitcore/buffer/xhr.ts");

      const emptyData = new ArrayBuffer(0);

      class XhrJsApiCommandBuffer extends _commandbuffer__WEBPACK_IMPORTED_MODULE_0__.CommandBufferClass {
        flush(supportXHRNotifyResult) {
          if (supportXHRNotifyResult && !this.codeSegment) {
            return;
          }

          const data = this.codeSegment ? this.toArrayBuffer() : emptyData;

          const results = _xhr__WEBPACK_IMPORTED_MODULE_1__["default"].syncJsApiCommandBuffer(data);

          this.clear();
          return results;
        } // 如果当前 CommandBuffer 里面已经有JsApi了，不用fetch，等RAF就行


        fetch() {
          if (this.codeSegment) {
            return;
          }

          return _xhr__WEBPACK_IMPORTED_MODULE_1__["default"].syncJsApiCommandBuffer(emptyData);
        }

      }
      /* harmony default export */


      const __WEBPACK_DEFAULT_EXPORT__ = new XhrJsApiCommandBuffer();
      /***/

    },

    /***/
    "./wk-game/src/jitcore/core.ts":
    /*!*************************************!*\
      !*** ./wk-game/src/jitcore/core.ts ***!
      \*************************************/

    /***/
    (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "WeixinJSCore": () =>
        /* binding */
        WeixinJSCore,

        /* harmony export */
        "WeixinJSCoreIOS": () =>
        /* binding */
        WeixinJSCoreIOS
        /* harmony export */

      });
      /* harmony import */


      var _buffer_ipc__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(
      /*! ./buffer/ipc */
      "./wk-game/src/jitcore/buffer/ipc.ts");
      /* harmony import */


      var _buffer_xhrjsapicommandbuffer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(
      /*! ./buffer/xhrjsapicommandbuffer */
      "./wk-game/src/jitcore/buffer/xhrjsapicommandbuffer.ts");
      /* harmony import */


      var _buffer_jsapicommandbuffer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(
      /*! ./buffer/jsapicommandbuffer */
      "./wk-game/src/jitcore/buffer/jsapicommandbuffer.ts");
      /* harmony import */


      var _buffer_nativebuffer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(
      /*! ./buffer/nativebuffer */
      "./wk-game/src/jitcore/buffer/nativebuffer.ts");
      /* harmony import */


      var _util__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(
      /*! ../util */
      "./wk-game/src/util.ts");
      /* harmony import */


      var _flush__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(
      /*! ./flush */
      "./wk-game/src/jitcore/flush.ts");
      /**
       * WKWebView与客户端通信方式枚举，详细可见文档：
       * https://iwiki.woa.com/pages/viewpage.action?pageId=1129714118
       * 不同的通信方式没有好坏之分，要根据场景甚至客户端版本来选择
       */


      var InvokeType;

      (function (InvokeType) {
        InvokeType[InvokeType["Prompt"] = 0] = "Prompt";
        InvokeType[InvokeType["PostMessage"] = 1] = "PostMessage";
        InvokeType[InvokeType["CommonXhr"] = 2] = "CommonXhr";
        InvokeType[InvokeType["CommandBuffer"] = 3] = "CommandBuffer";
        InvokeType[InvokeType["XhrCommandBufferSync"] = 4] = "XhrCommandBufferSync";
        InvokeType[InvokeType["XhrCommandBufferAsync"] = 5] = "XhrCommandBufferAsync";
      })(InvokeType || (InvokeType = {}));

      const WeixinJSCore = {
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
        config: {
          debug: false,
          useXhrForJsApiInvoke: true,
          supportXHRNotifyResult: true,
          syncApis: []
        },

        isSyncJsApi(event) {
          return event.toLowerCase().endsWith('sync') && !event.toLowerCase().endsWith('async') || WeixinJSCore.config.syncApis.includes(event);
        },

        setConfig(opt) {
          if (typeof opt.useXhrForJsApiInvoke !== 'undefined') {
            WeixinJSCore.config.useXhrForJsApiInvoke = opt.useXhrForJsApiInvoke;
          }

          if (typeof opt.supportXHRNotifyResult !== 'undefined') {
            WeixinJSCore.config.supportXHRNotifyResult = opt.supportXHRNotifyResult;
          }

          const syncApis = WeixinJSCore.config.syncApis;
          const newList = [];

          if (opt.syncApis && opt.syncApis.length) {
            opt.syncApis.forEach(event => {
              if (typeof event === 'string' && !syncApis.includes(event) && !newList.includes(event)) {
                newList.push(event);
              }
            });
          } // @ts-ignore


          WeixinJSCore.config.syncApis = WeixinJSCore.config.syncApis.concat(newList);
          (0, _flush__WEBPACK_IMPORTED_MODULE_5__.initFlush)(WeixinJSCore.config.useXhrForJsApiInvoke, WeixinJSCore.config.supportXHRNotifyResult, WeixinJSCore.invokeCallbackHandler);
        },

        invokeHandlerInternal(mode, event, param, id) {
          if (typeof param === 'string') {
            param = JSON.parse(param);
          }

          switch (mode) {
            /**
             * 最原始的 CommandBuffer 类型的调用，作用就是将 JSAPI 的调用合批
             * 调用数据不会立即发送出去，会随着下一次 requestAnimationFrame 一并发送
             */
            case InvokeType.CommandBuffer:
              _buffer_jsapicommandbuffer__WEBPACK_IMPORTED_MODULE_2__["default"].pushInvokeSegment(event, id, JSON.stringify((0, _buffer_nativebuffer__WEBPACK_IMPORTED_MODULE_3__.pack)(param, false)));

              break;

            /**
             * postMessage 是 WebKit 在 WKWebView 提供的标准通信方式，支持通过异步方式将js中的字符串传输到给客户端
             * 目前尚未使用到这种类型的通信，但因为比较通用，调用方式做保留
             */

            case InvokeType.PostMessage:
              _buffer_ipc__WEBPACK_IMPORTED_MODULE_0__["default"].asyncInvoke({
                id,
                event,
                param
              });

              break;

            /**
             * 对于异步类型的调用，仅仅是将数据拼接到 CommandBuffer 内，在下一次 requestAnimationFrame 的时候
             * 一起发送出去，达到合批的效果，减少传输性能消耗
             */

            case InvokeType.XhrCommandBufferAsync:
              _buffer_xhrjsapicommandbuffer__WEBPACK_IMPORTED_MODULE_1__["default"].pushInvokeSegment(event, id, JSON.stringify((0, _buffer_nativebuffer__WEBPACK_IMPORTED_MODULE_3__.pack)(param, false)));

              break;

            /**
             * 同步类型的调用和异步类型的调用并无差别，都是通过阻塞 xhr 的方式发送给客户端，只不过同步的是调用一次发送一次
             * 而异步的是随着每一次 requestAnimationFrame 发送出去达到合批的效果
             * 从性能上来讲，应该是要尽可能减少同步 JsApi 的设计和调用
             */

            case InvokeType.XhrCommandBufferSync:
              {
                _buffer_xhrjsapicommandbuffer__WEBPACK_IMPORTED_MODULE_1__["default"].pushInvokeSegment(event, id, JSON.stringify((0, _buffer_nativebuffer__WEBPACK_IMPORTED_MODULE_3__.pack)(param, false)));

                const results = _buffer_xhrjsapicommandbuffer__WEBPACK_IMPORTED_MODULE_1__["default"].flush(WeixinJSCore.config.supportXHRNotifyResult);

                if (results) {
                  for (let i = 0; i < results.length; i++) {
                    WeixinJSCoreIOS.invokeCallbackHandler(results[i][0], results[i][1]);
                  }
                }

                break;
              }

            default:
              {
                /**
                 * prompt 是H5标准中用于显示用户输入框的API，调用prompt()方法后，js执行将会被阻塞，
                 * 直到用户点击确定或取消。WKWebView没有默认实现这个功能，
                 * 而是在objective-c回调里面将弹框的逻辑交由开发者自己处理，我们可以充分利用这种特性来实现通信功能，
                 * 而且这种方式可以做到同步返回结果到js。
                 */
                const packedParam = (0, _buffer_nativebuffer__WEBPACK_IMPORTED_MODULE_3__.pack)(param, true);
                const apiFormatData = {
                  id,
                  event,
                  param: packedParam
                };

                const ret = _buffer_ipc__WEBPACK_IMPORTED_MODULE_0__["default"].sync('API', JSON.stringify(apiFormatData));

                if (ret) {
                  const obj = JSON.parse(ret); // WeixinJSCoreIOS.invokeCallbackHandler(id, unpack(obj))

                  WeixinJSCoreIOS.invokeCallbackHandler(id, obj);
                }

                break;
              }
          }
        },

        /**
         * 公共库调用客户端，触发 JsApi
         * 不同类型的客户端/不同类型的 JsApi 使用的通信方式都不一样，对于该使用哪一种类型的方式来通信，这里统一做一次筛选
         */
        invokeHandler(event, param, id) {
          let mode = InvokeType.Prompt; // 2021 T5 开始才能使用可用的xhr，之前的实现都有坑

          if (WeixinJSCore.config.useXhrForJsApiInvoke) {
            // xhr模式下，同步和异步的处理方式也是不一样的，需要简单根据配置文件过一遍
            if (WeixinJSCore.isSyncJsApi(event)) {
              mode = InvokeType.XhrCommandBufferSync;
            } else {
              mode = InvokeType.XhrCommandBufferAsync;
            }
          } else {
            // 最原始的对异步 jsapi 的 commandbuffer 处理方式，主要是为了调用合批
            if (WeixinJSCore.isSyncJsApi(event)) {
              mode = InvokeType.Prompt;
            } else {
              mode = InvokeType.CommandBuffer;
            }
          }

          WeixinJSCore.invokeHandlerInternal(mode, event, param, id);
        },

        // 客户端回调该接口，触发 JsApi 的异步 callback
        invokeCallbackHandler(id, obj) {
          if (!WeixinJSBridge) {
            (0, _util__WEBPACK_IMPORTED_MODULE_4__.weloge)('[invokeCallbackHandler] WeixinJSBridge not found');
            return;
          }
          /**
           * WeixinJSBridge.invokeCallbackHandler 是基础库实现的，里面有个细节是会执行WeixinNativeBuffer.unpack
           * 他的主要逻辑和这里的 unpack 的逻辑差不多也是判断是否存在 __nativeBuffers__ 然后做一定的转码逻辑
           * 按理这里不需要执行 unpack(obj)，直接交给WeixinJSBridge.invokeCallbackHandler就好了，但是wk下面对于
           * ArrayBuffer的处理是不一样的：
           * 1. 没有开启 xhr 的方案，以 writeFile 为例，写入 ArrayBuffer 类型数据的会转换成base64，同时为了处理字符问题
           * 还会做一些字符处理逻辑，所以要求对应的 readFile 拿到 ArrayBuffer 之前做一个反向处理，也就是这里的 unpack(obj)
           * 这样才能做到读写一致
           * 2. 开启了 xhr 的方案，调用到这里的时候，obj 是没有 __nativeBuffers__ 的，不管是这里的 unpack(obj)还是
           * WeixinNativeBuffer.unpack 都不会对 obj 做什么处理
           * 所以这里要对 obj 统一做一个 unpack 处理，不要轻易去掉，可能有坑
           */


          return WeixinJSBridge.invokeCallbackHandler(id, (0, _buffer_nativebuffer__WEBPACK_IMPORTED_MODULE_3__.unpack)(obj));
        },

        notifyResult() {
          const results = _buffer_xhrjsapicommandbuffer__WEBPACK_IMPORTED_MODULE_1__["default"].fetch();

          if (results) {
            for (let i = 0; i < results.length; i++) {
              WeixinJSCore.invokeCallbackHandler(results[i][0], results[i][1]);
            }
          }
        },

        notifyResultDirectly(results, hasArrayBufferResult) {
          if (results) {
            for (let i = 0; i < results.length; i++) {
              WeixinJSCore.invokeCallbackHandler(results[i][0], results[i][1]);
            }
          }

          if (hasArrayBufferResult) {
            const abResult = _buffer_xhrjsapicommandbuffer__WEBPACK_IMPORTED_MODULE_1__["default"].fetch();

            if (abResult) {
              for (let i = 0; i < abResult.length; i++) {
                WeixinJSCore.invokeCallbackHandler(abResult[i][0], abResult[i][1]);
              }
            }
          }
        },

        /**
         * 客户端通过 evaluate 调用 WeixinJSCoreIOS.subscribeHandler 方法，不能返回 ArrayBuffer 数据
         * 如果用 base64 返回一方面是很慢另一方面是还要转码，速度很慢（老的 base64 返回的方式仍然支持）
         * 所以在最新的 xhr 模式里如果是有 ArrayBuffer 返回为result.__nativeBuffers__，比如 [{id: 1, key: 'data'}]
         * 为了拿到真正的 ArrayBuffer 数据，还需要单独发送一条 xhr 请求去捞出真正的 ArrayBuffer 数据
         * 这里的id是客户端生成的，前端不用理解，只是透传作为getNativeBuffer的参数
         */
        subscribeHandler(event, result, webviewId, ext) {
          if (!WeixinJSBridge) {
            (0, _util__WEBPACK_IMPORTED_MODULE_4__.weloge)('[subscribeHandler] WeixinJSBridge not found');
            return;
          }
          /**
           * 客户端通过 evaluate
           */


          (0, _buffer_nativebuffer__WEBPACK_IMPORTED_MODULE_3__.unpackNativeBuffer)(result).then(convertedResult => WeixinJSBridge.subscribeHandler(event, convertedResult, webviewId, ext));
        },

        batchSubscribeHandler(events) {
          if (!WeixinJSBridge) {
            (0, _util__WEBPACK_IMPORTED_MODULE_4__.weloge)('[subscribeHandler] WeixinJSBridge not found');
            return;
          }

          (0, _buffer_nativebuffer__WEBPACK_IMPORTED_MODULE_3__.batchUnpackNativeBuffer)(events).then(convertedEvents => {
            convertedEvents.forEach(event => {
              WeixinJSBridge.subscribeHandler(event.eventName, event.eventParam, event.webViewId, event.ext);
            });
          });
        }

      };
      (0, _flush__WEBPACK_IMPORTED_MODULE_5__.initFlush)(WeixinJSCore.config.useXhrForJsApiInvoke, WeixinJSCore.config.supportXHRNotifyResult, WeixinJSCore.invokeCallbackHandler);
      const WeixinJSCoreIOS = {
        invokeHandler: WeixinJSCore.invokeHandler,
        invokeCallbackHandler: WeixinJSCore.invokeCallbackHandler,
        subscribeHandler: WeixinJSCore.subscribeHandler,
        batchSubscribeHandler: WeixinJSCore.batchSubscribeHandler,
        notifyResult: WeixinJSCore.notifyResult,
        notifyResultDirectly: WeixinJSCore.notifyResultDirectly,
        config: WeixinJSCore.config,
        setConfig: WeixinJSCore.setConfig
      };
      /***/
    },

    /***/
    "./wk-game/src/jitcore/flush.ts":
    /*!**************************************!*\
      !*** ./wk-game/src/jitcore/flush.ts ***!
      \**************************************/

    /***/
    (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "initFlush": () =>
        /* binding */
        initFlush
        /* harmony export */

      });
      /* harmony import */


      var _buffer_xhrjsapicommandbuffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(
      /*! ./buffer/xhrjsapicommandbuffer */
      "./wk-game/src/jitcore/buffer/xhrjsapicommandbuffer.ts");
      /* harmony import */


      var _buffer_jsapicommandbuffer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(
      /*! ./buffer/jsapicommandbuffer */
      "./wk-game/src/jitcore/buffer/jsapicommandbuffer.ts");
      /* harmony import */


      var _frame__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(
      /*! ./frame */
      "./wk-game/src/jitcore/frame/index.ts");

      let flushId;

      function initFlush(useXhrForJsApiInvoke, supportXHRNotifyResult, invokeCallbackHandler) {
        (0, _frame__WEBPACK_IMPORTED_MODULE_2__.cancelAnimationFrame)(flushId);

        if (useXhrForJsApiInvoke) {
          _buffer_jsapicommandbuffer__WEBPACK_IMPORTED_MODULE_1__["default"].clearBuffer();

          _buffer_xhrjsapicommandbuffer__WEBPACK_IMPORTED_MODULE_0__["default"].allocBuffer();

          const flush = function () {
            // jsapiCommandBuffer.flush()
            const results = _buffer_xhrjsapicommandbuffer__WEBPACK_IMPORTED_MODULE_0__["default"].flush(supportXHRNotifyResult);

            if (results) {
              for (let i = 0; i < results.length; i++) {
                invokeCallbackHandler(results[i][0], results[i][1]);
              }
            }

            flushId = (0, _frame__WEBPACK_IMPORTED_MODULE_2__.requestAnimationFrame)(flush);
          };

          flushId = (0, _frame__WEBPACK_IMPORTED_MODULE_2__.requestAnimationFrame)(flush);
        } else {
          _buffer_xhrjsapicommandbuffer__WEBPACK_IMPORTED_MODULE_0__["default"].clearBuffer();

          _buffer_jsapicommandbuffer__WEBPACK_IMPORTED_MODULE_1__["default"].allocBuffer(); // 不支持 xhr 的模式，commandbuffer的作用主要是为了异步 JSAPI 合批调用


          const flush = function () {
            _buffer_jsapicommandbuffer__WEBPACK_IMPORTED_MODULE_1__["default"].flush();

            flushId = (0, _frame__WEBPACK_IMPORTED_MODULE_2__.requestAnimationFrame)(flush);
          };

          flushId = (0, _frame__WEBPACK_IMPORTED_MODULE_2__.requestAnimationFrame)(flush);
        }
      }
      /***/

    },

    /***/
    "./wk-game/src/jitcore/frame/index.ts":
    /*!********************************************!*\
      !*** ./wk-game/src/jitcore/frame/index.ts ***!
      \********************************************/

    /***/
    (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "cancelAnimationFrame": () =>
        /* binding */
        cancelAnimationFrame,

        /* harmony export */
        "initStats": () =>
        /* binding */
        initStats,

        /* harmony export */
        "removeStats": () =>
        /* binding */
        removeStats,

        /* harmony export */
        "requestAnimationFrame": () =>
        /* binding */
        requestAnimationFrame,

        /* harmony export */
        "setPreferredFramesPerSecond": () =>
        /* binding */
        setPreferredFramesPerSecond,

        /* harmony export */
        "startLoop": () =>
        /* binding */
        startLoop,

        /* harmony export */
        "stopLoop": () =>
        /* binding */
        stopLoop
        /* harmony export */

      });
      /* harmony import */


      var _stats_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(
      /*! ./stats.js */
      "./wk-game/src/jitcore/frame/stats.js"); // import { StatsClass } from '../types'


      let frameId = 0; // let previouTimestamp = 0

      const callbacks = new Map();
      const raf = window.requestAnimationFrame;
      const caf = window.cancelAnimationFrame;

      function requestAnimationFrame(callback) {
        callbacks.set(++frameId, callback);
        return frameId;
      }

      function cancelAnimationFrame(id) {
        callbacks.delete(id);
      }

      function setPreferredFramesPerSecond(fps) {
        if (fps >= 1) {// frameInterval = 1000 / fps
        }
      } // const needRenderer = !!globalThis.debugMode
      // const needRenderer = true
      // @ts-ignore


      let stats;

      function initStats(needRenderer) {
        // @ts-ignore
        stats = new _stats_js__WEBPACK_IMPORTED_MODULE_0__["default"](needRenderer);

        if (needRenderer) {
          stats.dom.style.top = '80px';
          stats.dom.style.left = '20px';
          stats.dom.style.opacity = '.5';
          stats.dom.style.zIndex = '999';
          document.body.appendChild(stats.dom);
          stats.showPanel(0);
        }
      }

      function removeStats() {
        // @ts-ignore
        if (stats && stats.dom) {
          // @ts-ignore
          document.body.removeChild(stats.dom);
        } // @ts-ignore


        stats = new _stats_js__WEBPACK_IMPORTED_MODULE_0__["default"](false);
      }

      let loopId;
      let loopIng = false;

      function startLoop() {
        loopIng = true;

        const loop = function (timestamp) {
          if (!loopIng) {
            return;
          } // @ts-ignore


          stats === null || stats === void 0 ? void 0 : stats.begin();
          const cbs = [...callbacks.values()];
          callbacks.clear(); // 先清空再调用

          cbs.forEach(cb => cb(timestamp)); // @ts-ignore

          stats === null || stats === void 0 ? void 0 : stats.end();
          raf(loop);
        };

        raf(loop);
      }

      function stopLoop() {
        caf(loopId);
        loopIng = false;
      }
      /***/

    },

    /***/
    "./wk-game/src/native/screencanvas/event.ts":
    /*!**************************************************!*\
      !*** ./wk-game/src/native/screencanvas/event.ts ***!
      \**************************************************/

    /***/
    (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "TouchEvent": () =>
        /* binding */
        TouchEvent,

        /* harmony export */
        "proxyPropToCanvasEvent": () =>
        /* binding */
        proxyPropToCanvasEvent
        /* harmony export */

      });
      /* harmony import */


      var _util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(
      /*! ./util */
      "./wk-game/src/native/screencanvas/util.ts");

      class Touch {
        constructor(touch, canvas) {
          this.identifier = touch.identifier;
          this.clientX = this.pageX = touch.clientX;
          this.clientY = this.pageY = touch.clientY;
          this.force = touch.force;
          this.offsetX = touch.clientX - parseInt(canvas.style.left);
          this.offsetY = touch.clientY - parseInt(canvas.style.top);
        }

      }
      /**
       * 事件结构对齐小游戏标准的事件：https://developers.weixin.qq.com/minigame/dev/api/device/touch-event/wx.onTouchStart.html
       */


      class TouchEvent {
        constructor(event, canvas) {
          this.touches = Array.from(event.touches).map(t => new Touch(t, canvas));
          this.changedTouches = Array.from(event.changedTouches).map(t => new Touch(t, canvas));
          this.timeStamp = event.timeStamp;
        }

      }

      const proxyPropToCanvasEvent = () => (target, key, descriptor) => {
        descriptor.get = function () {
          return (0, _util__WEBPACK_IMPORTED_MODULE_0__.getPrivateThis)(this)[key];
        };

        descriptor.set = function (func) {
          const _private = (0, _util__WEBPACK_IMPORTED_MODULE_0__.getPrivateThis)(this);

          const canvas = _private.canvas;
          _private[key] = func;
          /**
           * Screencanvas 的设计是如果事件处理程序返回 false 则阻止事件冒泡，但这并不是一个标准的浏览器事件行为：
           * https://stackoverflow.com/questions/1357118/event-preventdefault-vs-return-false
           * 所以要对事件包一层，如果事件处理程序返回 false 则阻止默认行为并阻止事件冒泡
           */

          if (typeof func === 'function') {
            // @ts-ignore
            _private.canvas[key] = function (e) {
              const ret = func(new TouchEvent(e, canvas));

              if (ret === false) {
                e.preventDefault();
                e.stopPropagation();
              }
            };
          } else {
            _private.canvas[key] = func;
          }
        };
      };
      /***/

    },

    /***/
    "./wk-game/src/native/screencanvas/index.ts":
    /*!**************************************************!*\
      !*** ./wk-game/src/native/screencanvas/index.ts ***!
      \**************************************************/

    /***/
    (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "ScreenCanvas": () =>
        /* binding */
        ScreenCanvas
        /* harmony export */

      });
      /* harmony import */


      var tslib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(
      /*! tslib */
      "./wk-game/node_modules/tslib/tslib.es6.js");
      /* harmony import */


      var _event__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(
      /*! ./event */
      "./wk-game/src/native/screencanvas/event.ts");
      /* harmony import */


      var _util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(
      /*! ./util */
      "./wk-game/src/native/screencanvas/util.ts");
      /* harmony import */


      var _style__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(
      /*! ./style */
      "./wk-game/src/native/screencanvas/style.ts");
      /**
       * 对齐设计，实现一个和客户端一样的 ScreenCanvas 功能：https://git.woa.com/wxweb/design/blob/master/appservice/ScreenCanvas.md
       */


      let id = 0;
      const EventList = ['webglcontextlost', 'viewready'];

      class ScreenCanvas {
        constructor() {
          this.__id = 0; // 创建canvas

          const canvas = document.createElement('canvas');
          canvas.style.zIndex = '0';
          canvas.style.position = 'fixed';
          document.body.appendChild(canvas);
          const _private = {
            canvas
          };
          (0, _util__WEBPACK_IMPORTED_MODULE_1__.addPrivateThis)(this, _private); // 长按会有震动行为，对canvas添加阻止默认行为才有效，单纯给document添加没有效果

          canvas.addEventListener('touchstart', e => {
            e.preventDefault();
          });
          canvas.width = window.innerWidth;
          canvas.height = window.innerHeight;
          this.__id = id++;
        }

        get style() {
          const _private = (0, _util__WEBPACK_IMPORTED_MODULE_1__.getPrivateThis)(this);

          if (!_private.style) {
            _private.style = new _style__WEBPACK_IMPORTED_MODULE_2__.ScreenCanvasStyle(_private.canvas);
          }

          return _private.style;
        }

        get width() {
          return (0, _util__WEBPACK_IMPORTED_MODULE_1__.getPrivateThis)(this).canvas.width;
        }

        set width(value) {
          (0, _util__WEBPACK_IMPORTED_MODULE_1__.getPrivateThis)(this).canvas.width = value;
        }

        get height() {
          return (0, _util__WEBPACK_IMPORTED_MODULE_1__.getPrivateThis)(this).canvas.height;
        }

        set height(value) {
          (0, _util__WEBPACK_IMPORTED_MODULE_1__.getPrivateThis)(this).canvas.height = value;
        }

        get ontouchstart() {} // @ts-ignore


        set ontouchstart(func) {}

        get ontouchmove() {} // @ts-ignore


        set ontouchmove(func) {}

        get ontouchend() {} // @ts-ignore


        set ontouchend(func) {}

        get ontouchcancel() {} // @ts-ignore


        set ontouchcancel(func) {}

        remove() {
          (0, _util__WEBPACK_IMPORTED_MODULE_1__.getPrivateThis)(this).canvas.remove();
        } // 返回画布上的绘图上下文，同普通canvas


        getContext(contextType, contextAttributes) {
          return (0, _util__WEBPACK_IMPORTED_MODULE_1__.getPrivateThis)(this).canvas.getContext(contextType, contextAttributes);
        } // 方法返回一个包含图片展示的 data URI，同普通canvas


        toDataURL(type, encoderOptions) {
          return (0, _util__WEBPACK_IMPORTED_MODULE_1__.getPrivateThis)(this).canvas.toDataURL(type, encoderOptions);
        } // 注册某些事件的监听，eventListener 中提供以下回调
        // webglcontextlost: webgl context 或者 context 下对应的 buffer 丢失，此时需要前端主动停掉 raf
        // viewready: canvas 对应的 view 准备完毕，接下来的绘制能够正确上屏（看能不能干掉这个监听）


        addEventListener(type, listener) {
          if (EventList.includes(type)) {
            (0, _util__WEBPACK_IMPORTED_MODULE_1__.getPrivateThis)(this).canvas.addEventListener(type, listener);
          } else {
            console.warn(`[JITRuntime] event ${type} is not support!`);
          }
        }

        removeEventListener(type, listener) {
          (0, _util__WEBPACK_IMPORTED_MODULE_1__.getPrivateThis)(this).canvas.removeEventListener(type, listener);
        }

      }

      (0, tslib__WEBPACK_IMPORTED_MODULE_3__.__decorate)([(0, _event__WEBPACK_IMPORTED_MODULE_0__.proxyPropToCanvasEvent)()], ScreenCanvas.prototype, "ontouchstart", null);
      (0, tslib__WEBPACK_IMPORTED_MODULE_3__.__decorate)([(0, _event__WEBPACK_IMPORTED_MODULE_0__.proxyPropToCanvasEvent)()], ScreenCanvas.prototype, "ontouchmove", null);
      (0, tslib__WEBPACK_IMPORTED_MODULE_3__.__decorate)([(0, _event__WEBPACK_IMPORTED_MODULE_0__.proxyPropToCanvasEvent)()], ScreenCanvas.prototype, "ontouchend", null);
      (0, tslib__WEBPACK_IMPORTED_MODULE_3__.__decorate)([(0, _event__WEBPACK_IMPORTED_MODULE_0__.proxyPropToCanvasEvent)()], ScreenCanvas.prototype, "ontouchcancel", null);
      /***/
    },

    /***/
    "./wk-game/src/native/screencanvas/style.ts":
    /*!**************************************************!*\
      !*** ./wk-game/src/native/screencanvas/style.ts ***!
      \**************************************************/

    /***/
    (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "ScreenCanvasStyle": () =>
        /* binding */
        ScreenCanvasStyle
        /* harmony export */

      });
      /* harmony import */


      var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(
      /*! tslib */
      "./wk-game/node_modules/tslib/tslib.es6.js");
      /* harmony import */


      var _util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(
      /*! ./util */
      "./wk-game/src/native/screencanvas/util.ts");

      const proxyPropToCanvasStyle = () => (target, key, descriptor) => {
        descriptor.get = function () {
          return (0, _util__WEBPACK_IMPORTED_MODULE_0__.getPrivateThis)(this).canvas.style[key];
        };

        descriptor.set = function (v) {
          (0, _util__WEBPACK_IMPORTED_MODULE_0__.getPrivateThis)(this).canvas.style[key] = v;
        };
      };

      class ScreenCanvasStyle {
        constructor(canvas) {
          const _private = {
            canvas
          };
          (0, _util__WEBPACK_IMPORTED_MODULE_0__.addPrivateThis)(this, _private);
        }

        get width() {} // @ts-ignore


        set width(v) {}

        get height() {} // @ts-ignore


        set height(v) {}

        get left() {} // @ts-ignore


        set left(v) {}

        get top() {} // @ts-ignore


        set top(v) {}

        get zIndex() {
          return (0, _util__WEBPACK_IMPORTED_MODULE_0__.getPrivateThis)(this).canvas.style.zIndex;
        }

        set zIndex(v) {
          if (!isNaN(v) && Number(v) < 0) {
            console.debug('zIndex must be Number greater than or equal to 0');
          } else {
            (0, _util__WEBPACK_IMPORTED_MODULE_0__.getPrivateThis)(this).canvas.style.zIndex = v;
          }
        }

      }

      (0, tslib__WEBPACK_IMPORTED_MODULE_1__.__decorate)([proxyPropToCanvasStyle()], ScreenCanvasStyle.prototype, "width", null);
      (0, tslib__WEBPACK_IMPORTED_MODULE_1__.__decorate)([proxyPropToCanvasStyle()], ScreenCanvasStyle.prototype, "height", null);
      (0, tslib__WEBPACK_IMPORTED_MODULE_1__.__decorate)([proxyPropToCanvasStyle()], ScreenCanvasStyle.prototype, "left", null);
      (0, tslib__WEBPACK_IMPORTED_MODULE_1__.__decorate)([proxyPropToCanvasStyle()], ScreenCanvasStyle.prototype, "top", null);
      /***/
    },

    /***/
    "./wk-game/src/native/screencanvas/util.ts":
    /*!*************************************************!*\
      !*** ./wk-game/src/native/screencanvas/util.ts ***!
      \*************************************************/

    /***/
    (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "addPrivateThis": () =>
        /* binding */
        addPrivateThis,

        /* harmony export */
        "getPrivateThis": () =>
        /* binding */
        getPrivateThis
        /* harmony export */

      });

      const privateMap = new WeakMap(); // @ts-ignore

      const getPrivateThis = obj => privateMap.get(obj); // @ts-ignore


      const addPrivateThis = (obj, value) => privateMap.set(obj, value);
      /***/

    },

    /***/
    "./wk-game/src/util.ts":
    /*!*****************************!*\
      !*** ./wk-game/src/util.ts ***!
      \*****************************/

    /***/
    (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "addPrivateThis": () =>
        /* binding */
        addPrivateThis,

        /* harmony export */
        "getDataType": () =>
        /* binding */
        getDataType,

        /* harmony export */
        "getPrivateThis": () =>
        /* binding */
        getPrivateThis,

        /* harmony export */
        "getUtf8Length": () =>
        /* binding */
        getUtf8Length,

        /* harmony export */
        "isValidArrayBuffer": () =>
        /* binding */
        isValidArrayBuffer,

        /* harmony export */
        "partialXhrTest": () =>
        /* binding */
        partialXhrTest,

        /* harmony export */
        "readFileXhrSync": () =>
        /* binding */
        readFileXhrSync,

        /* harmony export */
        "supportUseXhrForAllJSAPI": () =>
        /* binding */
        supportUseXhrForAllJSAPI,

        /* harmony export */
        "supportXHRNotifyResult": () =>
        /* binding */
        supportXHRNotifyResult,

        /* harmony export */
        "welog": () =>
        /* binding */
        welog,

        /* harmony export */
        "weloge": () =>
        /* binding */
        weloge,

        /* harmony export */
        "xhrAbTest": () =>
        /* binding */
        xhrAbTest
        /* harmony export */

      });

      class welog {
        static log(data) {
          prompt('WELOG', data);
        }

      }

      function weloge(err) {
        welog.log('[JSError] ' + err);
      }

      function getUtf8Length(str) {
        const s = String(str);
        let len = 0;

        for (let i = 0; i < s.length; i++) {
          const code = s.charCodeAt(i);

          if (code <= 0x7f) {
            len += 1;
          } else if (code <= 0x7ff) {
            len += 2;
          } else if (code >= 0xd800 && code <= 0xdfff) {
            len += 4;
            i++;
          } else {
            len += 3;
          }
        }

        return len;
      }

      function isValidArrayBuffer(value) {
        return typeof value !== 'undefined' && getDataType(value) === 'ArrayBuffer' && typeof value.byteLength !== 'undefined';
      }

      function getDataType(data) {
        return Object.prototype.toString.call(data).split(' ')[1].split(']')[0];
      }

      function readFileXhrSync({
        filePath,
        responseType
      }) {
        const xhr = new XMLHttpRequest();
        xhr.open('GET', filePath, false);
        xhr.responseType = responseType;
        xhr.send(null);
        return responseType === 'arraybuffer' ? xhr.response : xhr.responseText;
      }

      const privateMap = new WeakMap();

      const getPrivateThis = obj => privateMap.get(obj);

      const addPrivateThis = (obj, value) => privateMap.set(obj, value);
      /**
       * https://git.woa.com/wxweb/game-design/issues/286
       */


      var WkVersion;

      (function (WkVersion) {
        WkVersion[WkVersion["Base"] = 0] = "Base";
        WkVersion[WkVersion["ImageProxy"] = 1] = "ImageProxy";
        WkVersion[WkVersion["UseXhrForSomeJSAPI"] = 2] = "UseXhrForSomeJSAPI";
        WkVersion[WkVersion["UseXhrForAllJSAPI"] = 3] = "UseXhrForAllJSAPI";
        WkVersion[WkVersion["gameContentPrefixByQuery"] = 4] = "gameContentPrefixByQuery";
        WkVersion[WkVersion["supportXHRNotifyResult"] = 5] = "supportXHRNotifyResult";
      })(WkVersion || (WkVersion = {})); // 开启 xhr 是否命中了 abtest
      // @ts-ignore


      const xhrAbTest = (globalThis.__featBit & 0x1) > 0;
      /**
       * 全部 JSAPI 都支持 xhr 模式
       * 这个完全依赖于xhrAbTest
       */
      // @ts-ignore

      const supportUseXhrForAllJSAPI = xhrAbTest; // @ts-ignore

      const partialXhrTest = globalThis.__wkVersion === WkVersion.supportXHRNotifyResult;
      /**
       * 通过notifyCallback通知有回调，这个先不去掉，等到观察稳定之后再去掉吧
       */
      // @ts-ignore

      const supportXHRNotifyResult = globalThis.__wkVersion >= WkVersion.supportXHRNotifyResult;
      /***/
    },

    /***/
    "./wk-game/node_modules/tslib/tslib.es6.js":
    /*!*************************************************!*\
      !*** ./wk-game/node_modules/tslib/tslib.es6.js ***!
      \*************************************************/

    /***/
    (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "__assign": () =>
        /* binding */
        __assign,

        /* harmony export */
        "__asyncDelegator": () =>
        /* binding */
        __asyncDelegator,

        /* harmony export */
        "__asyncGenerator": () =>
        /* binding */
        __asyncGenerator,

        /* harmony export */
        "__asyncValues": () =>
        /* binding */
        __asyncValues,

        /* harmony export */
        "__await": () =>
        /* binding */
        __await,

        /* harmony export */
        "__awaiter": () =>
        /* binding */
        __awaiter,

        /* harmony export */
        "__classPrivateFieldGet": () =>
        /* binding */
        __classPrivateFieldGet,

        /* harmony export */
        "__classPrivateFieldSet": () =>
        /* binding */
        __classPrivateFieldSet,

        /* harmony export */
        "__createBinding": () =>
        /* binding */
        __createBinding,

        /* harmony export */
        "__decorate": () =>
        /* binding */
        __decorate,

        /* harmony export */
        "__exportStar": () =>
        /* binding */
        __exportStar,

        /* harmony export */
        "__extends": () =>
        /* binding */
        __extends,

        /* harmony export */
        "__generator": () =>
        /* binding */
        __generator,

        /* harmony export */
        "__importDefault": () =>
        /* binding */
        __importDefault,

        /* harmony export */
        "__importStar": () =>
        /* binding */
        __importStar,

        /* harmony export */
        "__makeTemplateObject": () =>
        /* binding */
        __makeTemplateObject,

        /* harmony export */
        "__metadata": () =>
        /* binding */
        __metadata,

        /* harmony export */
        "__param": () =>
        /* binding */
        __param,

        /* harmony export */
        "__read": () =>
        /* binding */
        __read,

        /* harmony export */
        "__rest": () =>
        /* binding */
        __rest,

        /* harmony export */
        "__spread": () =>
        /* binding */
        __spread,

        /* harmony export */
        "__spreadArray": () =>
        /* binding */
        __spreadArray,

        /* harmony export */
        "__spreadArrays": () =>
        /* binding */
        __spreadArrays,

        /* harmony export */
        "__values": () =>
        /* binding */
        __values
        /* harmony export */

      });
      /*! *****************************************************************************
      Copyright (c) Microsoft Corporation.
      
      Permission to use, copy, modify, and/or distribute this software for any
      purpose with or without fee is hereby granted.
      
      THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
      REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
      AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
      INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
      LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
      OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
      PERFORMANCE OF THIS SOFTWARE.
      ***************************************************************************** */

      /* global Reflect, Promise */


      var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf || {
          __proto__: []
        } instanceof Array && function (d, b) {
          d.__proto__ = b;
        } || function (d, b) {
          for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };

        return extendStatics(d, b);
      };

      function __extends(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);

        function __() {
          this.constructor = d;
        }

        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
      }

      var __assign = function () {
        __assign = Object.assign || function __assign(t) {
          for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];

            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
          }

          return t;
        };

        return __assign.apply(this, arguments);
      };

      function __rest(s, e) {
        var t = {};

        for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];

        if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
        }
        return t;
      }

      function __decorate(decorators, target, key, desc) {
        var c = arguments.length,
            r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,
            d;
        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
        return c > 3 && r && Object.defineProperty(target, key, r), r;
      }

      function __param(paramIndex, decorator) {
        return function (target, key) {
          decorator(target, key, paramIndex);
        };
      }

      function __metadata(metadataKey, metadataValue) {
        if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(metadataKey, metadataValue);
      }

      function __awaiter(thisArg, _arguments, P, generator) {
        function adopt(value) {
          return value instanceof P ? value : new P(function (resolve) {
            resolve(value);
          });
        }

        return new (P || (P = Promise))(function (resolve, reject) {
          function fulfilled(value) {
            try {
              step(generator.next(value));
            } catch (e) {
              reject(e);
            }
          }

          function rejected(value) {
            try {
              step(generator["throw"](value));
            } catch (e) {
              reject(e);
            }
          }

          function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
          }

          step((generator = generator.apply(thisArg, _arguments || [])).next());
        });
      }

      function __generator(thisArg, body) {
        var _ = {
          label: 0,
          sent: function () {
            if (t[0] & 1) throw t[1];
            return t[1];
          },
          trys: [],
          ops: []
        },
            f,
            y,
            t,
            g;
        return g = {
          next: verb(0),
          "throw": verb(1),
          "return": verb(2)
        }, typeof Symbol === "function" && (g[Symbol.iterator] = function () {
          return this;
        }), g;

        function verb(n) {
          return function (v) {
            return step([n, v]);
          };
        }

        function step(op) {
          if (f) throw new TypeError("Generator is already executing.");

          while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];

            switch (op[0]) {
              case 0:
              case 1:
                t = op;
                break;

              case 4:
                _.label++;
                return {
                  value: op[1],
                  done: false
                };

              case 5:
                _.label++;
                y = op[1];
                op = [0];
                continue;

              case 7:
                op = _.ops.pop();

                _.trys.pop();

                continue;

              default:
                if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                  _ = 0;
                  continue;
                }

                if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                  _.label = op[1];
                  break;
                }

                if (op[0] === 6 && _.label < t[1]) {
                  _.label = t[1];
                  t = op;
                  break;
                }

                if (t && _.label < t[2]) {
                  _.label = t[2];

                  _.ops.push(op);

                  break;
                }

                if (t[2]) _.ops.pop();

                _.trys.pop();

                continue;
            }

            op = body.call(thisArg, _);
          } catch (e) {
            op = [6, e];
            y = 0;
          } finally {
            f = t = 0;
          }

          if (op[0] & 5) throw op[1];
          return {
            value: op[0] ? op[1] : void 0,
            done: true
          };
        }
      }

      var __createBinding = Object.create ? function (o, m, k, k2) {
        if (k2 === undefined) k2 = k;
        Object.defineProperty(o, k2, {
          enumerable: true,
          get: function () {
            return m[k];
          }
        });
      } : function (o, m, k, k2) {
        if (k2 === undefined) k2 = k;
        o[k2] = m[k];
      };

      function __exportStar(m, o) {
        for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);
      }

      function __values(o) {
        var s = typeof Symbol === "function" && Symbol.iterator,
            m = s && o[s],
            i = 0;
        if (m) return m.call(o);
        if (o && typeof o.length === "number") return {
          next: function () {
            if (o && i >= o.length) o = void 0;
            return {
              value: o && o[i++],
              done: !o
            };
          }
        };
        throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
      }

      function __read(o, n) {
        var m = typeof Symbol === "function" && o[Symbol.iterator];
        if (!m) return o;
        var i = m.call(o),
            r,
            ar = [],
            e;

        try {
          while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
        } catch (error) {
          e = {
            error: error
          };
        } finally {
          try {
            if (r && !r.done && (m = i["return"])) m.call(i);
          } finally {
            if (e) throw e.error;
          }
        }

        return ar;
      }
      /** @deprecated */


      function __spread() {
        for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));

        return ar;
      }
      /** @deprecated */


      function __spreadArrays() {
        for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;

        for (var r = Array(s), k = 0, i = 0; i < il; i++) for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++) r[k] = a[j];

        return r;
      }

      function __spreadArray(to, from, pack) {
        if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
          if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
          }
        }
        return to.concat(ar || Array.prototype.slice.call(from));
      }

      function __await(v) {
        return this instanceof __await ? (this.v = v, this) : new __await(v);
      }

      function __asyncGenerator(thisArg, _arguments, generator) {
        if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
        var g = generator.apply(thisArg, _arguments || []),
            i,
            q = [];
        return i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function () {
          return this;
        }, i;

        function verb(n) {
          if (g[n]) i[n] = function (v) {
            return new Promise(function (a, b) {
              q.push([n, v, a, b]) > 1 || resume(n, v);
            });
          };
        }

        function resume(n, v) {
          try {
            step(g[n](v));
          } catch (e) {
            settle(q[0][3], e);
          }
        }

        function step(r) {
          r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);
        }

        function fulfill(value) {
          resume("next", value);
        }

        function reject(value) {
          resume("throw", value);
        }

        function settle(f, v) {
          if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]);
        }
      }

      function __asyncDelegator(o) {
        var i, p;
        return i = {}, verb("next"), verb("throw", function (e) {
          throw e;
        }), verb("return"), i[Symbol.iterator] = function () {
          return this;
        }, i;

        function verb(n, f) {
          i[n] = o[n] ? function (v) {
            return (p = !p) ? {
              value: __await(o[n](v)),
              done: n === "return"
            } : f ? f(v) : v;
          } : f;
        }
      }

      function __asyncValues(o) {
        if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
        var m = o[Symbol.asyncIterator],
            i;
        return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function () {
          return this;
        }, i);

        function verb(n) {
          i[n] = o[n] && function (v) {
            return new Promise(function (resolve, reject) {
              v = o[n](v), settle(resolve, reject, v.done, v.value);
            });
          };
        }

        function settle(resolve, reject, d, v) {
          Promise.resolve(v).then(function (v) {
            resolve({
              value: v,
              done: d
            });
          }, reject);
        }
      }

      function __makeTemplateObject(cooked, raw) {
        if (Object.defineProperty) {
          Object.defineProperty(cooked, "raw", {
            value: raw
          });
        } else {
          cooked.raw = raw;
        }

        return cooked;
      }

      ;

      var __setModuleDefault = Object.create ? function (o, v) {
        Object.defineProperty(o, "default", {
          enumerable: true,
          value: v
        });
      } : function (o, v) {
        o["default"] = v;
      };

      function __importStar(mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);

        __setModuleDefault(result, mod);

        return result;
      }

      function __importDefault(mod) {
        return mod && mod.__esModule ? mod : {
          default: mod
        };
      }

      function __classPrivateFieldGet(receiver, state, kind, f) {
        if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
        if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
        return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
      }

      function __classPrivateFieldSet(receiver, state, value, kind, f) {
        if (kind === "m") throw new TypeError("Private method is not writable");
        if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
        if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
        return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
      }
      /***/

    },

    /***/
    "./wk-game/src/jitcore/frame/stats.js":
    /*!********************************************!*\
      !*** ./wk-game/src/jitcore/frame/stats.js ***!
      \********************************************/

    /***/
    (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
      __webpack_require__.r(__webpack_exports__);
      /* harmony export */


      __webpack_require__.d(__webpack_exports__, {
        /* harmony export */
        "default": () =>
        /* binding */
        Stats
        /* harmony export */

      });
      /* eslint-disable */

      /**
       * stat.js github 魔改版本
       */

      /**
       * 
       * @param { boolean } needRenderer 是否需要渲染，GitHub 版本默认是要渲染的，这里加个参数，可以不执行渲染作为纯逻辑
       */


      var Stats = function (needRenderer = true) {
        var mode = 0;
        var container;
        var round = Math.round;

        if (needRenderer) {
          container = document.createElement("div");
          container.style.cssText = "position:fixed;top:0;left:0;cursor:pointer;opacity:0.9;z-index:10000";
          container.addEventListener("click", function (event) {
            event.preventDefault();
            showPanel(++mode % container.children.length);
          }, false);
        } //


        function addPanel(panel) {
          if (needRenderer) {
            container.appendChild(panel.dom);
          }

          return panel;
        }

        function showPanel(id) {
          if (needRenderer) {
            for (var i = 0; i < container.children.length; i++) {
              container.children[i].style.display = i === id ? "block" : "none";
            }

            mode = id;
          }
        }

        var beginTime = (performance || Date).now(),
            prevTime = beginTime,
            frames = 0;
        var fpsPanel = addPanel(new Stats.Panel("FPS", "#0ff", "#002", needRenderer));
        var msPanel = addPanel(new Stats.Panel("MS", "#0f0", "#020", needRenderer));

        if (self.performance && self.performance.memory) {
          var memPanel = addPanel(new Stats.Panel("MB", "#f08", "#201", needRenderer));
        }

        showPanel(0);
        return {
          REVISION: 16,
          fps: 0,
          dom: container,
          addPanel: addPanel,
          showPanel: showPanel,
          begin: function () {
            beginTime = (performance || Date).now();
          },
          end: function () {
            frames++;
            var time = (performance || Date).now();
            msPanel.update(round(time - beginTime), 200);

            if (time > prevTime + 1000) {
              this.fps = round(frames * 1000 / (time - prevTime)); // 挂在全局共客户端读取用

              globalThis.fps = this.fps;
              fpsPanel.update(this.fps, 100);
              prevTime = time;
              frames = 0;

              if (memPanel) {
                var memory = performance.memory;
                memPanel.update(round(memory.usedJSHeapSize / 1048576), memory.jsHeapSizeLimit / 1048576);
              }
            }

            return time;
          },
          update: function () {
            beginTime = this.end();
          },
          // Backwards Compatibility
          domElement: container,
          setMode: showPanel
        };
      };

      Stats.Panel = function (name, fg, bg, needRenderer = true) {
        var min = Infinity,
            max = 0,
            round = Math.round;
        var PR = round(window.devicePixelRatio || 1);
        var WIDTH = 80 * PR,
            HEIGHT = 48 * PR,
            TEXT_X = 3 * PR,
            TEXT_Y = 2 * PR,
            GRAPH_X = 3 * PR,
            GRAPH_Y = 15 * PR,
            GRAPH_WIDTH = 74 * PR,
            GRAPH_HEIGHT = 30 * PR;
        var canvas;

        if (needRenderer) {
          canvas = document.createElement("canvas");
          canvas.width = WIDTH;
          canvas.height = HEIGHT;
          canvas.style.cssText = "width:80px;height:48px";
          var context = canvas.getContext("2d");
          context.font = "bold " + 9 * PR + "px Helvetica,Arial,sans-serif";
          context.textBaseline = "top";
          context.fillStyle = bg;
          context.fillRect(0, 0, WIDTH, HEIGHT);
          context.fillStyle = fg;
          context.fillText(name, TEXT_X, TEXT_Y);
          context.fillRect(GRAPH_X, GRAPH_Y, GRAPH_WIDTH, GRAPH_HEIGHT);
          context.fillStyle = bg;
          context.globalAlpha = 0.9;
          context.fillRect(GRAPH_X, GRAPH_Y, GRAPH_WIDTH, GRAPH_HEIGHT);
        }

        return {
          dom: canvas,
          update: function (value, maxValue) {
            if (!needRenderer) {
              return;
            }

            min = Math.min(min, value);
            max = Math.max(max, value);
            context.fillStyle = bg;
            context.globalAlpha = 1;
            context.fillRect(0, 0, WIDTH, GRAPH_Y);
            context.fillStyle = fg;
            context.fillText(value + " " + name + " (" + round(min) + "-" + round(max) + ")", TEXT_X, TEXT_Y);
            context.drawImage(canvas, GRAPH_X + PR, GRAPH_Y, GRAPH_WIDTH - PR, GRAPH_HEIGHT, GRAPH_X, GRAPH_Y, GRAPH_WIDTH - PR, GRAPH_HEIGHT);
            context.fillRect(GRAPH_X + GRAPH_WIDTH - PR, GRAPH_Y, PR, GRAPH_HEIGHT);
            context.fillStyle = bg;
            context.globalAlpha = 0.9;
            context.fillRect(GRAPH_X + GRAPH_WIDTH - PR, GRAPH_Y, PR, round((1 - value / maxValue) * GRAPH_HEIGHT));
          }
        };
      };
      /***/

    }
    /******/

  };
  /************************************************************************/

  /******/
  // The module cache

  /******/

  var __webpack_module_cache__ = {};
  /******/

  /******/
  // The require function

  /******/

  function __webpack_require__(moduleId) {
    /******/
    // Check if module is in cache

    /******/
    var cachedModule = __webpack_module_cache__[moduleId];
    /******/

    if (cachedModule !== undefined) {
      /******/
      return cachedModule.exports;
      /******/
    }
    /******/
    // Create a new module (and put it into the cache)

    /******/


    var module = __webpack_module_cache__[moduleId] = {
      /******/
      // no module.id needed

      /******/
      // no module.loaded needed

      /******/
      exports: {}
      /******/

    };
    /******/

    /******/
    // Execute the module function

    /******/

    __webpack_modules__[moduleId](module, module.exports, __webpack_require__);
    /******/

    /******/
    // Return the exports of the module

    /******/


    return module.exports;
    /******/
  }
  /******/

  /************************************************************************/

  /******/

  /* webpack/runtime/define property getters */

  /******/


  (() => {
    /******/
    // define getter functions for harmony exports

    /******/
    __webpack_require__.d = (exports, definition) => {
      /******/
      for (var key in definition) {
        /******/
        if (__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
          /******/
          Object.defineProperty(exports, key, {
            enumerable: true,
            get: definition[key]
          });
          /******/
        }
        /******/

      }
      /******/

    };
    /******/

  })();
  /******/

  /******/

  /* webpack/runtime/hasOwnProperty shorthand */

  /******/


  (() => {
    /******/
    __webpack_require__.o = (obj, prop) => Object.prototype.hasOwnProperty.call(obj, prop);
    /******/

  })();
  /******/

  /******/

  /* webpack/runtime/make namespace object */

  /******/


  (() => {
    /******/
    // define __esModule on exports

    /******/
    __webpack_require__.r = exports => {
      /******/
      if (typeof Symbol !== 'undefined' && Symbol.toStringTag) {
        /******/
        Object.defineProperty(exports, Symbol.toStringTag, {
          value: 'Module'
        });
        /******/
      }
      /******/


      Object.defineProperty(exports, '__esModule', {
        value: true
      });
      /******/
    };
    /******/

  })();
  /******/

  /************************************************************************/


  var __webpack_exports__ = {}; // This entry need to be wrapped in an IIFE because it need to be isolated against other modules in the chunk.

  (() => {
    /*!**********************!*\
      !*** ./src/index.ts ***!
      \**********************/
    __webpack_require__.r(__webpack_exports__);
    /* harmony import */


    var wk_game_jitcore_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(
    /*! wk-game/jitcore/core */
    "./wk-game/src/jitcore/core.ts");
    /* harmony import */


    var wk_game_jitcore_frame__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(
    /*! wk-game/jitcore/frame */
    "./wk-game/src/jitcore/frame/index.ts");
    /* harmony import */


    var wk_game_native_screencanvas__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(
    /*! wk-game/native/screencanvas */
    "./wk-game/src/native/screencanvas/index.ts");
    /* harmony import */


    var _JSBridge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(
    /*! ./JSBridge */
    "./src/JSBridge.ts");
    /* harmony import */


    var _context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(
    /*! ./context */
    "./src/context/index.ts");

    const JitRunTime = {
      requestAnimationFrame: wk_game_jitcore_frame__WEBPACK_IMPORTED_MODULE_1__.requestAnimationFrame,
      cancelAnimationFrame: wk_game_jitcore_frame__WEBPACK_IMPORTED_MODULE_1__.cancelAnimationFrame,
      ScreenCanvas: wk_game_native_screencanvas__WEBPACK_IMPORTED_MODULE_2__.ScreenCanvas,
      SubJsContext: _context__WEBPACK_IMPORTED_MODULE_4__.SubJsContext
    }; // hook setConfig

    const oldSetConfig = wk_game_jitcore_core__WEBPACK_IMPORTED_MODULE_0__.WeixinJSCoreIOS.setConfig; // @ts-ignore

    let vConsole;

    wk_game_jitcore_core__WEBPACK_IMPORTED_MODULE_0__.WeixinJSCoreIOS.setConfig = opt => {
      if (opt.debug === true) {
        if (typeof VConsole === 'undefined') {
          console.warn('[JITRuntime] VConsole is not exist!');
        } else {
          // @ts-ignore
          vConsole = new VConsole();
        }

        (0, wk_game_jitcore_frame__WEBPACK_IMPORTED_MODULE_1__.initStats)(true);
      } else {
        vConsole === null || vConsole === void 0 ? void 0 : vConsole.destroy();
        (0, wk_game_jitcore_frame__WEBPACK_IMPORTED_MODULE_1__.removeStats)();
      }

      if (opt.loop) {
        (0, wk_game_jitcore_frame__WEBPACK_IMPORTED_MODULE_1__.startLoop)();
      } else {
        (0, wk_game_jitcore_frame__WEBPACK_IMPORTED_MODULE_1__.stopLoop)();
      }

      oldSetConfig(opt);
    };

    Object.assign(globalThis, {
      WeixinJSCoreIOS: wk_game_jitcore_core__WEBPACK_IMPORTED_MODULE_0__.WeixinJSCoreIOS,
      WeixinJSBridge: _JSBridge__WEBPACK_IMPORTED_MODULE_3__.WeixinJSBridge,
      JitRunTime
    });
  })();
  /******/

})();
  </script>
</body>
</html>
