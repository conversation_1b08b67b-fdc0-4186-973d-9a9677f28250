!function(){var e={460:function(e){e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r},e.exports.__esModule=!0,e.exports.default=e.exports},6:function(e,t,n){var r=n(460);e.exports=function(e){if(Array.isArray(e))return r(e)},e.exports.__esModule=!0,e.exports.default=e.exports},20:function(e){e.exports=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},112:function(e){e.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},639:function(e){function t(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}e.exports=function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},935:function(e,t,n){var r=n(757),i=n(992),o=n(99);e.exports=function(e){var t=i();return function(){var n,i=r(e);if(t){var s=r(this).constructor;n=Reflect.construct(i,arguments,s)}else n=i.apply(this,arguments);return o(this,n)}},e.exports.__esModule=!0,e.exports.default=e.exports},757:function(e){function t(n){return e.exports=t=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,t(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},846:function(e,t,n){var r=n(462);e.exports=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&r(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},992:function(e){e.exports=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}},e.exports.__esModule=!0,e.exports.default=e.exports},986:function(e){e.exports=function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports.default=e.exports},534:function(e){e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},99:function(e,t,n){var r=n(861).default,i=n(20);e.exports=function(e,t){if(t&&("object"===r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return i(e)},e.exports.__esModule=!0,e.exports.default=e.exports},462:function(e){function t(n,r){return e.exports=t=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,t(n,r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},805:function(e,t,n){var r=n(6),i=n(986),o=n(913),s=n(534);e.exports=function(e){return r(e)||i(e)||o(e)||s()},e.exports.__esModule=!0,e.exports.default=e.exports},861:function(e){function t(n){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},913:function(e,t,n){var r=n(460);e.exports=function(e,t){if(e){if("string"==typeof e)return r(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},65:function(e,t,n){var r,i=n(861).default;!function(o){var s=Object.hasOwnProperty,a=Array.isArray?Array.isArray:function(e){return"[object Array]"===Object.prototype.toString.call(e)},l="object"==("undefined"==typeof process?"undefined":i(process))&&"function"==typeof process.nextTick,u="function"==typeof Symbol,c="object"===("undefined"==typeof Reflect?"undefined":i(Reflect)),f="function"==typeof setImmediate?setImmediate:setTimeout,h=u?c&&"function"==typeof Reflect.ownKeys?Reflect.ownKeys:function(e){var t=Object.getOwnPropertyNames(e);return t.push.apply(t,Object.getOwnPropertySymbols(e)),t}:Object.keys;function p(){this._events={},this._conf&&d.call(this,this._conf)}function d(e){e&&(this._conf=e,e.delimiter&&(this.delimiter=e.delimiter),e.maxListeners!==o&&(this._maxListeners=e.maxListeners),e.wildcard&&(this.wildcard=e.wildcard),e.newListener&&(this._newListener=e.newListener),e.removeListener&&(this._removeListener=e.removeListener),e.verboseMemoryLeak&&(this.verboseMemoryLeak=e.verboseMemoryLeak),e.ignoreErrors&&(this.ignoreErrors=e.ignoreErrors),this.wildcard&&(this.listenerTree={}))}function y(e,t){var n="(node) warning: possible EventEmitter memory leak detected. "+e+" listeners added. Use emitter.setMaxListeners() to increase limit.";if(this.verboseMemoryLeak&&(n+=" Event name: "+t+"."),"undefined"!=typeof process&&process.emitWarning){var r=new Error(n);r.name="MaxListenersExceededWarning",r.emitter=this,r.count=e,process.emitWarning(r)}else console.error(n),console.trace&&console.trace()}var v=function(e,t,n){var r=arguments.length;switch(r){case 0:return[];case 1:return[e];case 2:return[e,t];case 3:return[e,t,n];default:for(var i=new Array(r);r--;)i[r]=arguments[r];return i}};function _(e,t){for(var n={},r=e.length,i=t?t.length:0,s=0;s<r;s++)n[e[s]]=s<i?t[s]:o;return n}function m(e,t,n){var r,i;if(this._emitter=e,this._target=t,this._listeners={},this._listenersCount=0,(n.on||n.off)&&(r=n.on,i=n.off),t.addEventListener?(r=t.addEventListener,i=t.removeEventListener):t.addListener?(r=t.addListener,i=t.removeListener):t.on&&(r=t.on,i=t.off),!r&&!i)throw Error("target does not implement any known event API");if("function"!=typeof r)throw TypeError("on method must be a function");if("function"!=typeof i)throw TypeError("off method must be a function");this._on=r,this._off=i;var o=e._observers;o?o.push(this):e._observers=[this]}function g(e,t,n,r){var a=Object.assign({},t);if(!e)return a;if("object"!==i(e))throw TypeError("options must be an object");var l,u,c,f=Object.keys(e),h=f.length;function p(e){throw Error('Invalid "'+l+'" option value'+(e?". Reason: "+e:""))}for(var d=0;d<h;d++){if(l=f[d],!r&&!s.call(t,l))throw Error('Unknown "'+l+'" option');(u=e[l])!==o&&(c=n[l],a[l]=c?c(u,p):u)}return a}function b(e,t){return"function"==typeof e&&e.hasOwnProperty("prototype")||t("value must be a constructor"),e}function w(e){var t="value must be type of "+e.join("|"),n=e.length,r=e[0],o=e[1];return 1===n?function(e,n){if(i(e)===r)return e;n(t)}:2===n?function(e,n){var s=i(e);if(s===r||s===o)return e;n(t)}:function(r,o){for(var s=i(r),a=n;a-- >0;)if(s===e[a])return r;o(t)}}Object.assign(m.prototype,{subscribe:function(e,t,n){var r=this,i=this._target,o=this._emitter,s=this._listeners,a=function(){var r=v.apply(null,arguments),s={data:r,name:t,original:e};if(n){var a=n.call(i,s);!1!==a&&o.emit.apply(o,[s.name].concat(r))}else o.emit.apply(o,[t].concat(r))};if(s[e])throw Error("Event '"+e+"' is already listening");this._listenersCount++,o._newListener&&o._removeListener&&!r._onNewListener?(this._onNewListener=function(n){n===t&&null===s[e]&&(s[e]=a,r._on.call(i,e,a))},o.on("newListener",this._onNewListener),this._onRemoveListener=function(n){n===t&&!o.hasListeners(n)&&s[e]&&(s[e]=null,r._off.call(i,e,a))},s[e]=null,o.on("removeListener",this._onRemoveListener)):(s[e]=a,r._on.call(i,e,a))},unsubscribe:function(e){var t,n,r,i=this,o=this._listeners,s=this._emitter,a=this._off,l=this._target;if(e&&"string"!=typeof e)throw TypeError("event must be a string");function u(){i._onNewListener&&(s.off("newListener",i._onNewListener),s.off("removeListener",i._onRemoveListener),i._onNewListener=null,i._onRemoveListener=null);var e=T.call(s,i);s._observers.splice(e,1)}if(e){if(!(t=o[e]))return;a.call(l,e,t),delete o[e],--this._listenersCount||u()}else{for(r=(n=h(o)).length;r-- >0;)e=n[r],a.call(l,e,o[e]);this._listeners={},this._listenersCount=0,u()}}});var k=w(["function"]),x=w(["object","function"]);function L(e,t,n){var r,i,o,s=0,a=new e((function(l,u,c){function f(){i&&(i=null),s&&(clearTimeout(s),s=0)}n=g(n,{timeout:0,overload:!1},{timeout:function(e,t){return("number"!=typeof(e*=1)||e<0||!Number.isFinite(e))&&t("timeout must be a positive number"),e}}),r=!n.overload&&"function"==typeof e.prototype.cancel&&"function"==typeof c;var h=function(e){f(),l(e)},p=function(e){f(),u(e)};r?t(h,p,c):(i=[function(e){p(e||Error("canceled"))}],t(h,p,(function(e){if(o)throw Error("Unable to subscribe on cancel event asynchronously");if("function"!=typeof e)throw TypeError("onCancel callback must be a function");i.push(e)})),o=!0),n.timeout>0&&(s=setTimeout((function(){var e=Error("timeout");e.code="ETIMEDOUT",s=0,a.cancel(e),u(e)}),n.timeout))}));return r||(a.cancel=function(e){if(i){for(var t=i.length,n=1;n<t;n++)i[n](e);i[0](e),i=null}}),a}function T(e){var t=this._observers;if(!t)return-1;for(var n=t.length,r=0;r<n;r++)if(t[r]._target===e)return r;return-1}function P(e,t,n,r,o){if(!n)return null;if(0===r){var s=i(t);if("string"===s){var a,l,u=0,c=0,f=this.delimiter,p=f.length;if(-1!==(l=t.indexOf(f))){a=new Array(5);do{a[u++]=t.slice(c,l),c=l+p}while(-1!==(l=t.indexOf(f,c)));a[u++]=t.slice(c),t=a,o=u}else t=[t],o=1}else"object"===s?o=t.length:(t=[t],o=1)}var d,y,v,_,m,g,b,w=null,k=t[r],x=t[r+1];if(r===o)n._listeners&&("function"==typeof n._listeners?(e&&e.push(n._listeners),w=[n]):(e&&e.push.apply(e,n._listeners),w=[n]));else{if("*"===k){for(l=(g=h(n)).length;l-- >0;)"_listeners"!==(d=g[l])&&(b=P(e,t,n[d],r+1,o))&&(w?w.push.apply(w,b):w=b);return w}if("**"===k){for((m=r+1===o||r+2===o&&"*"===x)&&n._listeners&&(w=P(e,t,n,o,o)),l=(g=h(n)).length;l-- >0;)"_listeners"!==(d=g[l])&&("*"===d||"**"===d?(n[d]._listeners&&!m&&(b=P(e,t,n[d],o,o))&&(w?w.push.apply(w,b):w=b),b=P(e,t,n[d],r,o)):b=P(e,t,n[d],d===x?r+2:r,o),b&&(w?w.push.apply(w,b):w=b));return w}n[k]&&(w=P(e,t,n[k],r+1,o))}if((y=n["*"])&&P(e,t,y,r+1,o),v=n["**"])if(r<o)for(v._listeners&&P(e,t,v,o,o),l=(g=h(v)).length;l-- >0;)"_listeners"!==(d=g[l])&&(d===x?P(e,t,v[d],r+2,o):d===k?P(e,t,v[d],r+1,o):((_={})[d]=v[d],P(e,t,{"**":_},r+1,o)));else v._listeners?P(e,t,v,o,o):v["*"]&&v["*"]._listeners&&P(e,t,v["*"],o,o);return w}function E(e,t,n){var r,i,o=0,s=0,a=this.delimiter,l=a.length;if("string"==typeof e)if(-1!==(r=e.indexOf(a))){i=new Array(5);do{i[o++]=e.slice(s,r),s=r+l}while(-1!==(r=e.indexOf(a,s)));i[o++]=e.slice(s)}else i=[e],o=1;else i=e,o=e.length;if(o>1)for(r=0;r+1<o;r++)if("**"===i[r]&&"**"===i[r+1])return;var u,c=this.listenerTree;for(r=0;r<o;r++)if(c=c[u=i[r]]||(c[u]={}),r===o-1)return c._listeners?("function"==typeof c._listeners&&(c._listeners=[c._listeners]),n?c._listeners.unshift(t):c._listeners.push(t),!c._listeners.warned&&this._maxListeners>0&&c._listeners.length>this._maxListeners&&(c._listeners.warned=!0,y.call(this,c._listeners.length,u))):c._listeners=t,!0;return!0}function j(e,t,n,r){for(var o,s,a,l,u=h(e),c=u.length,f=e._listeners;c-- >0;)o=e[s=u[c]],a="_listeners"===s?n:n?n.concat(s):[s],l=r||"symbol"===i(s),f&&t.push(l?a:a.join(this.delimiter)),"object"===i(o)&&j.call(this,o,t,a,l);return t}function M(e){for(var t,n,r,i=h(e),o=i.length;o-- >0;)(t=e[n=i[o]])&&(r=!0,"_listeners"===n||M(t)||delete e[n]);return r}function A(e,t,n){this.emitter=e,this.event=t,this.listener=n}function S(e,t,n){if(!0===n)s=!0;else if(!1===n)r=!0;else{if(!n||"object"!==i(n))throw TypeError("options should be an object or true");var r=n.async,s=n.promisify,a=n.nextTick,u=n.objectify}if(r||a||s){var c=t,h=t._origin||t;if(a&&!l)throw Error("process.nextTick is not supported");s===o&&(s="AsyncFunction"===t.constructor.name),t=function(){var e=arguments,t=this,n=this.event;return s?a?Promise.resolve():new Promise((function(e){f(e)})).then((function(){return t.event=n,c.apply(t,e)})):(a?process.nextTick:f)((function(){t.event=n,c.apply(t,e)}))},t._async=!0,t._origin=h}return[t,u?new A(this,e,t):this]}function O(e){this._events={},this._newListener=!1,this._removeListener=!1,this.verboseMemoryLeak=!1,d.call(this,e)}A.prototype.off=function(){return this.emitter.off(this.event,this.listener),this},O.EventEmitter2=O,O.prototype.listenTo=function(e,t,n){if("object"!==i(e))throw TypeError("target musts be an object");var r=this;function s(t){if("object"!==i(t))throw TypeError("events must be an object");var o,s=n.reducers,a=T.call(r,e);o=-1===a?new m(r,e,n):r._observers[a];for(var l,u=h(t),c=u.length,f="function"==typeof s,p=0;p<c;p++)l=u[p],o.subscribe(l,t[l]||l,f?s:s&&s[l])}return n=g(n,{on:o,off:o,reducers:o},{on:k,off:k,reducers:x}),a(t)?s(_(t)):s("string"==typeof t?_(t.split(/\s+/)):t),this},O.prototype.stopListeningTo=function(e,t){var n=this._observers;if(!n)return!1;var r,o=n.length,s=!1;if(e&&"object"!==i(e))throw TypeError("target should be an object");for(;o-- >0;)r=n[o],e&&r._target!==e||(r.unsubscribe(t),s=!0);return s},O.prototype.delimiter=".",O.prototype.setMaxListeners=function(e){e!==o&&(this._maxListeners=e,this._conf||(this._conf={}),this._conf.maxListeners=e)},O.prototype.getMaxListeners=function(){return this._maxListeners},O.prototype.event="",O.prototype.once=function(e,t,n){return this._once(e,t,!1,n)},O.prototype.prependOnceListener=function(e,t,n){return this._once(e,t,!0,n)},O.prototype._once=function(e,t,n,r){return this._many(e,1,t,n,r)},O.prototype.many=function(e,t,n,r){return this._many(e,t,n,!1,r)},O.prototype.prependMany=function(e,t,n,r){return this._many(e,t,n,!0,r)},O.prototype._many=function(e,t,n,r,i){var o=this;if("function"!=typeof n)throw new Error("many only accepts instances of Function");function s(){return 0==--t&&o.off(e,s),n.apply(this,arguments)}return s._origin=n,this._on(e,s,r,i)},O.prototype.emit=function(){if(!this._events&&!this._all)return!1;this._events||p.call(this);var e,t,n,r,o,s,a=arguments[0],l=this.wildcard;if("newListener"===a&&!this._newListener&&!this._events.newListener)return!1;if(l&&(e=a,"newListener"!==a&&"removeListener"!==a&&"object"===i(a))){if(n=a.length,u)for(r=0;r<n;r++)if("symbol"===i(a[r])){s=!0;break}s||(a=a.join(this.delimiter))}var c,f=arguments.length;if(this._all&&this._all.length)for(r=0,n=(c=this._all.slice()).length;r<n;r++)switch(this.event=a,f){case 1:c[r].call(this,a);break;case 2:c[r].call(this,a,arguments[1]);break;case 3:c[r].call(this,a,arguments[1],arguments[2]);break;default:c[r].apply(this,arguments)}if(l)c=[],P.call(this,c,e,this.listenerTree,0,n);else{if("function"==typeof(c=this._events[a])){switch(this.event=a,f){case 1:c.call(this);break;case 2:c.call(this,arguments[1]);break;case 3:c.call(this,arguments[1],arguments[2]);break;default:for(t=new Array(f-1),o=1;o<f;o++)t[o-1]=arguments[o];c.apply(this,t)}return!0}c&&(c=c.slice())}if(c&&c.length){if(f>3)for(t=new Array(f-1),o=1;o<f;o++)t[o-1]=arguments[o];for(r=0,n=c.length;r<n;r++)switch(this.event=a,f){case 1:c[r].call(this);break;case 2:c[r].call(this,arguments[1]);break;case 3:c[r].call(this,arguments[1],arguments[2]);break;default:c[r].apply(this,t)}return!0}if(!this.ignoreErrors&&!this._all&&"error"===a)throw arguments[1]instanceof Error?arguments[1]:new Error("Uncaught, unspecified 'error' event.");return!!this._all},O.prototype.emitAsync=function(){if(!this._events&&!this._all)return!1;this._events||p.call(this);var e,t,n,r,o,s,a=arguments[0],l=this.wildcard;if("newListener"===a&&!this._newListener&&!this._events.newListener)return Promise.resolve([!1]);if(l&&(e=a,"newListener"!==a&&"removeListener"!==a&&"object"===i(a))){if(r=a.length,u)for(o=0;o<r;o++)if("symbol"===i(a[o])){t=!0;break}t||(a=a.join(this.delimiter))}var c,f=[],h=arguments.length;if(this._all)for(o=0,r=this._all.length;o<r;o++)switch(this.event=a,h){case 1:f.push(this._all[o].call(this,a));break;case 2:f.push(this._all[o].call(this,a,arguments[1]));break;case 3:f.push(this._all[o].call(this,a,arguments[1],arguments[2]));break;default:f.push(this._all[o].apply(this,arguments))}if(l?(c=[],P.call(this,c,e,this.listenerTree,0)):c=this._events[a],"function"==typeof c)switch(this.event=a,h){case 1:f.push(c.call(this));break;case 2:f.push(c.call(this,arguments[1]));break;case 3:f.push(c.call(this,arguments[1],arguments[2]));break;default:for(n=new Array(h-1),s=1;s<h;s++)n[s-1]=arguments[s];f.push(c.apply(this,n))}else if(c&&c.length){if(c=c.slice(),h>3)for(n=new Array(h-1),s=1;s<h;s++)n[s-1]=arguments[s];for(o=0,r=c.length;o<r;o++)switch(this.event=a,h){case 1:f.push(c[o].call(this));break;case 2:f.push(c[o].call(this,arguments[1]));break;case 3:f.push(c[o].call(this,arguments[1],arguments[2]));break;default:f.push(c[o].apply(this,n))}}else if(!this.ignoreErrors&&!this._all&&"error"===a)return arguments[1]instanceof Error?Promise.reject(arguments[1]):Promise.reject("Uncaught, unspecified 'error' event.");return Promise.all(f)},O.prototype.on=function(e,t,n){return this._on(e,t,!1,n)},O.prototype.prependListener=function(e,t,n){return this._on(e,t,!0,n)},O.prototype.onAny=function(e){return this._onAny(e,!1)},O.prototype.prependAny=function(e){return this._onAny(e,!0)},O.prototype.addListener=O.prototype.on,O.prototype._onAny=function(e,t){if("function"!=typeof e)throw new Error("onAny only accepts instances of Function");return this._all||(this._all=[]),t?this._all.unshift(e):this._all.push(e),this},O.prototype._on=function(e,t,n,r){if("function"==typeof e)return this._onAny(e,t),this;if("function"!=typeof t)throw new Error("on only accepts instances of Function");this._events||p.call(this);var i,s=this;return r!==o&&(t=(i=S.call(this,e,t,r))[0],s=i[1]),this._newListener&&this.emit("newListener",e,t),this.wildcard?(E.call(this,e,t,n),s):(this._events[e]?("function"==typeof this._events[e]&&(this._events[e]=[this._events[e]]),n?this._events[e].unshift(t):this._events[e].push(t),!this._events[e].warned&&this._maxListeners>0&&this._events[e].length>this._maxListeners&&(this._events[e].warned=!0,y.call(this,this._events[e].length,e))):this._events[e]=t,s)},O.prototype.off=function(e,t){if("function"!=typeof t)throw new Error("removeListener only takes instances of Function");var n,r=[];if(this.wildcard){var i="string"==typeof e?e.split(this.delimiter):e.slice();if(!(r=P.call(this,null,i,this.listenerTree,0)))return this}else{if(!this._events[e])return this;n=this._events[e],r.push({_listeners:n})}for(var o=0;o<r.length;o++){var s=r[o];if(n=s._listeners,a(n)){for(var l=-1,u=0,c=n.length;u<c;u++)if(n[u]===t||n[u].listener&&n[u].listener===t||n[u]._origin&&n[u]._origin===t){l=u;break}if(l<0)continue;return this.wildcard?s._listeners.splice(l,1):this._events[e].splice(l,1),0===n.length&&(this.wildcard?delete s._listeners:delete this._events[e]),this._removeListener&&this.emit("removeListener",e,t),this}(n===t||n.listener&&n.listener===t||n._origin&&n._origin===t)&&(this.wildcard?delete s._listeners:delete this._events[e],this._removeListener&&this.emit("removeListener",e,t))}return this.listenerTree&&M(this.listenerTree),this},O.prototype.offAny=function(e){var t,n=0,r=0;if(e&&this._all&&this._all.length>0){for(n=0,r=(t=this._all).length;n<r;n++)if(e===t[n])return t.splice(n,1),this._removeListener&&this.emit("removeListenerAny",e),this}else{if(t=this._all,this._removeListener)for(n=0,r=t.length;n<r;n++)this.emit("removeListenerAny",t[n]);this._all=[]}return this},O.prototype.removeListener=O.prototype.off,O.prototype.removeAllListeners=function(e){if(e===o)return!this._events||p.call(this),this;if(this.wildcard){var t,n=P.call(this,null,e,this.listenerTree,0);if(!n)return this;for(t=0;t<n.length;t++)n[t]._listeners=null;this.listenerTree&&M(this.listenerTree)}else this._events&&(this._events[e]=null);return this},O.prototype.listeners=function(e){var t,n,r,i,s,a=this._events;if(e===o){if(this.wildcard)throw Error("event name required for wildcard emitter");if(!a)return[];for(i=(t=h(a)).length,r=[];i-- >0;)"function"==typeof(n=a[t[i]])?r.push(n):r.push.apply(r,n);return r}if(this.wildcard){if(!(s=this.listenerTree))return[];var l=[],u="string"==typeof e?e.split(this.delimiter):e.slice();return P.call(this,l,u,s,0),l}return a&&(n=a[e])?"function"==typeof n?[n]:n:[]},O.prototype.eventNames=function(e){var t=this._events;return this.wildcard?j.call(this,this.listenerTree,[],null,e):t?h(t):[]},O.prototype.listenerCount=function(e){return this.listeners(e).length},O.prototype.hasListeners=function(e){if(this.wildcard){var t=[],n="string"==typeof e?e.split(this.delimiter):e.slice();return P.call(this,t,n,this.listenerTree,0),t.length>0}var r=this._events,i=this._all;return!!(i&&i.length||r&&(e===o?h(r).length:r[e]))},O.prototype.listenersAny=function(){return this._all?this._all:[]},O.prototype.waitFor=function(e,t){var n=this,r=i(t);return"number"===r?t={timeout:t}:"function"===r&&(t={filter:t}),L((t=g(t,{timeout:0,filter:o,handleError:!1,Promise:Promise,overload:!1},{filter:k,Promise:b})).Promise,(function(r,i,o){function s(){var o=t.filter;if(!o||o.apply(n,arguments))if(n.off(e,s),t.handleError){var a=arguments[0];a?i(a):r(v.apply(null,arguments).slice(1))}else r(v.apply(null,arguments))}o((function(){n.off(e,s)})),n._on(e,s,!1)}),{timeout:t.timeout,overload:t.overload})};var R=O.prototype;Object.defineProperties(O,{defaultMaxListeners:{get:function(){return R._maxListeners},set:function(e){if("number"!=typeof e||e<0||Number.isNaN(e))throw TypeError("n must be a non-negative number");R._maxListeners=e},enumerable:!0},once:{value:function(e,t,n){return L((n=g(n,{Promise:Promise,timeout:0,overload:!1},{Promise:b})).Promise,(function(n,r,i){var o;if("function"==typeof e.addEventListener)return o=function(){n(v.apply(null,arguments))},i((function(){e.removeEventListener(t,o)})),void e.addEventListener(t,o,{once:!0});var s,a=function(){s&&e.removeListener("error",s),n(v.apply(null,arguments))};"error"!==t&&(s=function(n){e.removeListener(t,a),r(n)},e.once("error",s)),i((function(){s&&e.removeListener("error",s),e.removeListener(t,a)})),e.once(t,a)}),{timeout:n.timeout,overload:n.overload})},writable:!0,configurable:!0}}),Object.defineProperties(R,{_maxListeners:{value:10,writable:!0,configurable:!0},_observers:{value:null,writable:!0,configurable:!0}}),(r=function(){return O}.call(t,n,t,e))===o||(e.exports=r)}()}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},function(){"use strict";var e,t,r,i,o={},s={},a=function(e){for(var t=[],n=e.split("/"),r=0,i=n.length;r<i;++r){var o=n[r];if(""!==o&&"."!==o)if(".."===o){if(0===t.length){t=null;break}t.pop()}else r+1<i&&".."===n[r+1]?r++:t.push(o)}return t},l=function(e){var t=a(e+"/index.js").join("/");return o[t]?t:(t=a(e).join("/"),/\.js$/.test(t)||(t+=".js"),o[t]?t:"")},u=function(e){var t=function(e){var t=e.match(/(.*)\/([^/]+)?$/);return t&&t[1]?t[1]:"./"}(e);return function(e){if("string"!=typeof e)throw new Error("require args must be a string");var n=a(t+"/"+e);if("/__wx__/private-api"===e&&(n=a(e)),!n)throw new Error("can't find module : "+e);var r=n.join("/");return r=function(e,t,n){var r=e;if(/\.js$/.test(r)||(r+=".js"),"string"==typeof r&&o[r])return r;var i=a(t);if(!i)throw new Error("can't find module : "+n);for(var s,u=e.substring(i.join("/").length);i.length;){var c=i.join("/")+"/miniprogram_npm"+u;if(s=l(c))break;i.pop()}return s||(u="/"===u[0]?u:"/"+u,s=l("miniprogram_npm"+u)),s||e}(r,t,e),globalThis.require(r)}},c=n(639),f=n.n(c),h=n(112),p=n.n(h),d=n(846),y=n.n(d),v=n(935),_=n.n(v),m=function(e){return"function"==typeof e},g=function(){return Object.assign}(),b=NativeGlobal.WXAUDIO,w=new WeakMap,k=new WeakMap,x=new WeakMap,L=new WeakMap,T=new WeakMap,P=new WeakMap,E=new WeakMap,j=new WeakMap,M=new WeakMap,A=new WeakMap,S=new WeakMap,O={},R=(t=function(e){mb.JSBridge.invoke("readFile",e,(function(t){!t||0!==t.errCode&&"ok"!==t.errMsg.split(":")[1]?(e.fail&&e.fail(t),e.complete&&e.complete(t)):(e.success&&e.success({errMsg:"readFile: ok",data:t.data}),e.complete&&e.complete({errMsg:"readFile: ok",data:t.data}))}))},function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new Promise((function(n,r){t(g(e,{success:n,fail:r}))}))}),C=function(e){return R({filePath:e}).then((function(e){return e.data}))},W=function(e){w.set(this,Object.assign(w.get(this),e))},N=function(e){return w.get(this)[e]},I=function(){return w.get(this)},F=function(){this.stop(!0),W.call(this,{_source:null});var e=x.get(this);e&&e()},B=function(t){var n=e.createBufferSource(),r=U.call(this,t),i=r.currentPlayBackTime,o=r.state,s=o.loop,a=o.playbackRate,l=o._buffer,u=o._gainNode,c=F.bind(this);n.onended=c,n.buffer=l,n.loop=s,n.playbackRate.value=a,u&&n.connect(u),n.start(0,i),e.resume(),W.call(this,{_isPlaying:!0,paused:!1,_source:n,_played:!0,_playbackTime:0,_lastCurrentTime:i,_startedAt:e.currentTime,_endedHandler:c})},U=function(t){var n,r=t=t||I.call(this),i=r._startedAt,o=r._playbackTime,s=r._isPlaying,a=r.playbackRate,l=r._lastCurrentTime,u=r.duration;return(n=s?l+(e.currentTime-i)*a:o)>u&&(n%=u),{currentPlayBackTime:n,state:t}},D=function(){this.stop(!0);var e=I.call(this)._gainNode;e&&e.disconnect(),W.call(this,{_buffer:null,_source:null,_loadPromise:null,_gainNode:null,_audioLoadPromiseResolve:null}),k.delete(this),x.delete(this),L.delete(this),T.delete(this),E.delete(this),P.delete(this),j.delete(this),M.delete(this),S.delete(this),clearInterval(A.get(this)),A.delete(this)},H=function(){this.stop(!0),W.call(this,{_buffer:null,_source:null,_loadPromise:null,_audioLoadPromiseResolve:null})},q=function(e){var t;H.call(this);var n=new Promise((function(e){t=e}));W.call(this,{src:e,_ready:!1,_played:!1,_loadPromise:n,_audioLoadPromiseResolve:t})},J=function(){function t(){p()(this,t),e||"function"==typeof(e=new b).create&&e.create();var n=e.createGain();n.connect(e.destination);var r={},i=new Promise((function(e){r._audioLoadPromiseResolve=e}));w.set(this,Object.assign(r,{startTime:0,autoplay:!1,loop:!1,volume:1,playbackRate:1,paused:!1,duration:NaN,_loadPromise:i,_gainNode:n,_played:!1,_playbackTime:0,_ready:!1,_isPlaying:!1,src:""}))}return f()(t,[{key:"src",get:function(){return N.call(this,"src")},set:function(t){var n=this,r=I.call(this).src;"string"==typeof t&&t.length>0&&r!==t&&(r&&q.call(this,t),W.call(this,{src:t}),function(t){var n=O[t];if(n&&n.audioBuffer)return Promise.resolve(n.audioBuffer);if(n&&n.loadPromise)return n.loadPromise;var r=C(t).then((function(n){return new Promise((function(r,i){e.decodeAudioData(n,(function(e){var n=O[t];n.audioBuffer||(n.audioBuffer=e),r(n.audioBuffer)}),i)}))}));return O[t]={loadPromise:r},r}(t).then((function(e){var t=I.call(n),r=k.get(n),i=t._isPlaying,o=t.autoplay,s=t._audioLoadPromiseResolve;return W.call(n,{_ready:!0,_buffer:e,duration:e.duration}),r&&r(),o&&!i&&n.play(),s(),null})).catch((function(e){var r=L.get(n);r&&r({type:"error",errMsg:"set audio src ".concat(t," fail: ").concat(e.message)})})))}},{key:"startTime",get:function(){return N.call(this,"startTime")},set:function(e){var t=I.call(this),n=t._played;e!==t.startTime&&"number"==typeof e&&e>=0&&(W.call(this,{startTime:e}),n||this.seek(e))}},{key:"autoplay",get:function(){return N.call(this,"autoplay")},set:function(e){var t=this;!!e!==I.call(this).autoplay&&(W.call(this,{autoplay:!!e}),e&&I.call(this)._loadPromise.then((function(){var e=I.call(t),n=e._isPlaying,r=e.autoplay;return e._source&&!n&&r&&t.play(),null})))}},{key:"loop",get:function(){return N.call(this,"loop")},set:function(e){var t=N.call(this,"_source");W.call(this,{loop:e=!!e}),t&&(t.loop=e)}},{key:"volume",get:function(){return N.call(this,"volume")},set:function(e){var t=I.call(this),n=t.volume,r=t._gainNode;"number"==typeof e&&e>=0&&e<=1&&e!==n&&(W.call(this,{volume:e}),r&&r.gain&&(r.gain.value=e))}},{key:"playbackRate",get:function(){return N.call(this,"playbackRate")},set:function(t){var n=I.call(this),r=n.playbackRate,i=n._source;"number"==typeof t&&r!==t&&t>=.5&&t<=2&&(W.call(this,{playbackRate:t,_lastCurrentTime:this.currentTime,_startedAt:e.currentTime}),i&&(i.playbackRate.value=t))}},{key:"duration",get:function(){return N.call(this,"duration")}},{key:"currentTime",get:function(){return U.call(this).currentPlayBackTime}},{key:"paused",get:function(){return N.call(this,"paused")}},{key:"buffered",get:function(){var e=I.call(this).duration;return e>0?e:0}},{key:"play",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=I.call(this),r=n._isPlaying,i=n._ready,o=n._loadPromise;if(!r){var s=function(){return t?null:T.get(e)};if(i){B.call(this,n);var a=s();a&&a()}else o.then((function(){B.call(e);var t=s();return t&&t(),null}))}}},{key:"pause",value:function(){var e=I.call(this),t=e._isPlaying,n=e._source;if(n&&t){var r=P.get(this);n.stop(0),n.disconnect(),n.onended=null,W.call(this,{_isPlaying:!1,_playbackTime:this.currentTime,paused:!0,_endedHandler:null}),r&&r()}}},{key:"stop",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=I.call(this),n=t._source;if(n){var r=E.get(this);n.stop(0),n.disconnect(),n.onended=null,W.call(this,{_isPlaying:!1,_playbackTime:0,paused:!0,_endedHandler:null}),!e&&r&&r()}}},{key:"seek",value:function(e){if("number"==typeof e&&e>=0){var t=I.call(this)._isPlaying,n=j.get(this),r=M.get(this),i=k.get(this);r&&r(),t?(this.stop(!0),W.call(this,{_playbackTime:e}),this.play(!0)):W.call(this,{_playbackTime:e}),n&&n(),i&&i()}}},{key:"destroy",value:function(){D.call(this)}},{key:"oncanplay",set:function(e){"function"==typeof e?k.set(this,e):k.delete(this)}},{key:"onended",set:function(e){"function"==typeof e?x.set(this,e):x.delete(this)}},{key:"onerror",set:function(e){"function"==typeof e?L.set(this,e):L.delete(this)}},{key:"onplay",set:function(e){"function"==typeof e?T.set(this,e):T.delete(this)}},{key:"onpause",set:function(e){"function"==typeof e?P.set(this,e):P.delete(this)}},{key:"onstop",set:function(e){"function"==typeof e?E.set(this,e):E.delete(this)}},{key:"onseeked",set:function(e){"function"==typeof e?j.set(this,e):j.delete(this)}},{key:"onseeking",set:function(e){"function"==typeof e?M.set(this,e):M.delete(this)}},{key:"ontimeupdate",set:function(e){var t=this;if("function"==typeof e){var n=A.get(this);clearInterval(n);var r=setInterval((function(){I.call(t)._isPlaying&&e()}),350);A.set(this,r)}else A.delete(this)}},{key:"onwaiting",set:function(e){"function"==typeof e?S.set(this,e):S.delete(this)}}]),t}(),G=n(65),$=n.n(G),X=["src","startTime","autoplay","loop","obeyMuteSwitch","volume","playbackRate","duration","currentTime","paused","buffered","referrerPolicy"],K=["startTime","duration","currentTime"],z=["Canplay","Ended","Error","Pause","Play","Seeked","Seeking","Stop","TimeUpdate","Waiting"],V=new Set,Q=new WeakMap,Y=new WeakMap,Z=new WeakMap,ee=new WeakMap,te=function(){function e(t){var n=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3;p()(this,e);var i=new($());Q.set(this,t),V.add(this),ee.set(this,{conversion:r}),X.forEach((function(e){return Object.defineProperty(n,e,{get:function(){return K.includes(e)?t[e]/r:t[e]},set:function(n){K.includes(e)?t[e]=r*n:t[e]=n}})})),z.forEach((function(e){n["on"+e]=function(t){var n=t;Y.set(t,n),Z.set(n,t),i.on(e,n)},n["off"+e]=function(t){if(m(t)){var n=Y.get(t);m(n)&&(i.off(e,n),Y.delete(t),Z.delete(n))}else i.listeners(e).forEach((function(e){var t=Z.get(e);Y.delete(t),Z.delete(e)})),i.removeAllListeners(e)},t["on"+e.toLowerCase()]=function(t,n){t?i.emit(e,{errCode:t,errMsg:n}):i.emit(e)}}))}return f()(e,[{key:"play",value:function(){var e;null===(e=Q.get(this))||void 0===e||e.play()}},{key:"stop",value:function(){var e;null===(e=Q.get(this))||void 0===e||e.stop()}},{key:"pause",value:function(){var e;null===(e=Q.get(this))||void 0===e||e.pause()}},{key:"seek",value:function(e){var t,n,r=(null!==(t=ee.get(this))&&void 0!==t?t:{}).conversion;null===(n=Q.get(this))||void 0===n||n.seek(r*e)}},{key:"destroy",value:function(){var e=this,t=Q.get(this);t.destroy&&t.destroy(),z.forEach((function(t){e["off"+t]()})),V.delete(this),Q.delete(this)}}],[{key:"pauseAllAudio",value:function(){V.forEach((function(e){var t=Q.get(e);t&&t.pause()}))}}]),e}(),ne=function(e){y()(n,e);var t=_()(n);function n(){p()(this,n);var e=new J;return t.call(this,e,1)}return f()(n)}(te),re=n(805),ie=n.n(re),oe=function(e,t){try{for(var n=arguments.length,r=new Array(n>2?n-2:0),i=2;i<n;i++)r[i-2]=arguments[i];e.apply(void 0,r)}catch(e){console.error("thirdScriptError\n".concat(null==e?void 0:e.message,";").concat(t)),console.error(e)}},se=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];p()(this,e),this.type="",this.surround=!1,this.eventMap=new Map,this.type=t,this.surround=n}return f()(e,[{key:"on",value:function(e,t){"function"==typeof t?this.eventMap.has(e)?this.eventMap.get(e).add(t):this.eventMap.set(e,new Set([t])):console.error("EmitterError: callback must be a function")}},{key:"off",value:function(e,t){var n=this.eventMap.get(e);"function"!=typeof t?null==n||n.clear():null==n||n.delete(t)}},{key:"emit",value:function(e){for(var t=this,n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];var o=ie()(this.eventMap.get(e)||[]),s="at ".concat(this.type).concat(e," callback function");o.forEach((function(e){return t.surround?oe.apply(void 0,[e,s].concat(r)):e.apply(void 0,r)}))}},{key:"removeAllListeners",value:function(e){this.off(e)}},{key:"getListenersLength",value:function(e){var t;return(null===(t=this.eventMap.get(e))||void 0===t?void 0:t.size)||0}}]),e}(),ae=new se("",!0);r="onPlayableCustomEvent",i=function(e){ae.emit("onPlayableCustomEvent",e)},mb.JSBridge.on(r,i);var le,ue=new se("",!0),ce={};le=function(e){Object.assign(ce,JSON.parse(e.data)),ue.emit("onWXConfigUpdate",ce)},ae.on("onPlayableCustomEvent",le);var fe=ce,he=[],pe=function(e){var t;console.log("开始上报",e,fe.appid),mb.JSBridge.invoke("kvReport",{logId:24927,logStr:(t=[fe.appid,"",1,"","","playableAdImgUseRemoteUrl","",0,encodeURIComponent(e)],t.map((function(e){return String(e).replace(/,/g,";")})).join(","))},(function(e){console.log("kvReport 上报完成",e)}))};!function(e){ue.on("onWXConfigUpdate",e)}((function(){he.forEach((function(e){pe(e)})),he=[]}));var de=function(e){Object.defineProperty(e,"src",{get:function(){return e.__proto__.getSrc.call(e)},set:function(t){"string"!=typeof t||0!==t.indexOf("http://")&&0!==t.indexOf("https://")||function(e){fe.appid?pe(e):he.push(e)}(t),e.__proto__.setSrc.call(e,t)}})},ye=function(e,t){return mb.JSBridge.invoke(e,t)},ve=function(e,t){mb.JSBridge.invoke(e,t[0],(function(e){0===e.errCode||"ok"===e.errMsg.split(":")[1]?t[0].success&&t[0].success(e):t[0].fail&&t[0].fail(e)}))};function _e(){return ye("getSystemInfoSync",{})}var me,ge=new se("TouchEvent",!0),be=_e();globalThis.__wxConfig={screenWidth:be.screenWidth,screenHeight:be.screenHeight},globalThis.wx={createCanvas:function(){if(me){var e=new mb.OffscreenCanvas;return e.style={},e}me=!0;var t=new mb.ScreenCanvas;return t.width=n.g.__wxConfig.screenWidth,t.height=n.g.__wxConfig.screenHeight,t.style.width=n.g.__wxConfig.screenWidth,t.style.height=n.g.__wxConfig.screenHeight,"ios"===globalThis.mb.env.platform&&t.__lockStyle(),t.ontouchstart=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];ge.emit.apply(ge,["touchstart"].concat(t))},t.ontouchmove=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];ge.emit.apply(ge,["touchmove"].concat(t))},t.ontouchend=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];ge.emit.apply(ge,["touchend"].concat(t))},t.ontouchcancel=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];ge.emit.apply(ge,["touchcancel"].concat(t))},t},createInnerAudioContext:function(){return new ne},getSystemInfoSync:_e,onTouchStart:function(e){ge.on("touchstart",e)},onTouchMove:function(e){ge.on("touchmove",e)},onTouchEnd:function(e){ge.on("touchend",e)},onTouchCancel:function(e){ge.on("touchcancel",e)},createImage:function(){var e=new NativeGlobal.Image;return de(e),e},setEnableDebug:function(e){var t=e&&!0===e.enableDebug;mb.switchVConsole(t),console.log("setEnableDebug",t),e&&e.success&&e.success({})},notifyMiniProgramPlayableStatus:function(e){"boolean"==typeof(e=e||{}).isEnd?ve("notifyMiniProgramPlayableStatus",arguments):"function"==typeof e.fail&&e.fail({errMsg:"notifyMiniProgramPlayableStatus:fail parameter.isEnd must be boolean"})},getFileSystemManager:function(){return{mkdirSync:function(){return{}},readdirSync:function(){return{}},readFile:function(){ve("readFile",arguments)},readFileSync:function(){var e=ye("readFileSync",{filePath:arguments[0],encoding:arguments[1]});if(0===e.errCode||"ok"===e.errMsg.split(":")[1])return e.data;throw e.errMsg},copyFile:function(){return{}},getFileInfo:function(){return{}},statSync:function(){return{}}}},downloadFile:function(){return{}},request:function(){return{}},getStorageSync:function(){return{}},setStorageSync:function(){return{}},env:{USER_DATA_PATH:"/"}},globalThis.define=function(e,t){o[e]={status:1,factory:t}},globalThis.require=function(e,t){if(void 0===t&&(t=!0),"string"!=typeof e)throw new Error("require args must be a string");var n=o[e];if(!n){var r=-1===e.indexOf("/")?e+"/index.js":e;/\.js$/.test(r="miniprogram_npm/"+r)||(r+=".js"),n=o[r]}if(e.endsWith(".js")||(e+=".js"),!n)throw new Error('module "'+e+'" is not defined');var i={exports:{}},a=n.factory;if(!t||s[e])return delete n.exports,n.status=1,s[e]=!0,a&&a(u(e),i,i.exports),i.exports;if(1===n.status){n.exports=i.exports,n.status=2;var l=void 0;a&&(l=a(u(e),i,i.exports)),n.exports=void 0!==i.exports?i.exports:l}return n.exports},globalThis.GameGlobal=globalThis}()}();