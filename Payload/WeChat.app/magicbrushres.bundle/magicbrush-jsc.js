(function (global) {
  global.WebAssembly = NativeGlobal.WebAssembly;
  global.WXWebAssembly = NativeGlobal.WebAssembly;

  global.setTimeout = NativeGlobal.setTimeout;
  global.clearTimeout = NativeGlobal.clearTimeout;
  global.setInterval = NativeGlobal.setInterval;
  global.clearInterval = NativeGlobal.clearInterval;
  global.requestAnimationFrame = NativeGlobal.requestAnimationFrame;
  global.cancelAnimationFrame = NativeGlobal.cancelAnimationFrame;

  /*
        SubJsContext
      */
  const SubJsContext = (() => {
    // private container
    var __private__ = new WeakMap();

    class SubJsContext {
      constructor(name, env) {
        let impl = new NativeGlobal.SubJsContext(name, env);
        __private__.set(this, { impl: impl, name: name });
      }
      add(path) {
        return new Promise((resolve, reject) => {
          const impl = __private__.get(this).impl;
          if (!impl) {
            reject(`SubJsContext[${this.name}] already destroyed!`);
          }
          if (impl.add(path)) {
            resolve();
          }
          reject(`SubJsContext[${this.name}] add script failed`);
        });
      }
      eval(script) {
        return new Promise((resolve, reject) => {
          const impl = __private__.get(this).impl;
          if (!impl) {
            reject(`SubJsContext[${this.name}] already destroyed!`);
          }
          if (impl.eval(script)) {
            resolve();
          }
          reject(`SubJsContext[${this.name}] eval failed`);
        });
      }
      destroy() {
        return new Promise((resolve, reject) => {
          const impl = __private__.get(this).impl;
          if (!impl) {
            reject(`SubJsContext[${this.name}] already destroyed!`);
          }
          impl.destroy();
          __private__.get(this).impl = undefined;
          resolve();
        });
      }
      get name() {
        return __private__.get(this).name
          ? __private__.get(this).name
          : __private__.get(this).impl.name;
      }
    }
    return SubJsContext;
  })();
  //     global.mb.SubJsContext = SubJsContext;
  global.mb.SubJsContext = global.NativeGlobal.SubJsContext;

  global.mb.PublicService = global.NativeGlobal.PublicService;

  /*
        JSBridge
      */
  global.mb.JSBridge = global.NativeGlobal.JSBridge;
  global.mb.JSBridge.on("onJsError", function(res) {
    console.error(`${res.exception}\n${res.stack}`)
  });

  global.mb.ScreenCanvas = NativeGlobal.ScreenCanvas;
  global.mb.OffscreenCanvas = NativeGlobal.OffscreenCanvas;
  global.mb.loadFont = NativeGlobal.loadFont;
  global.mb.setPreferredFps = NativeGlobal.setPreferredFramesPerSecond;
  global.Image = NativeGlobal.Image;

  let rectPaletteAgent = {};
  rectPaletteAgent.FontContext = NativeGlobal.FontContext;
  global.mb.rectPaletteAgent = rectPaletteAgent;
})(globalThis);

(function(global) {
    var originSetTimeout = global.setTimeout;
    global.setTimeout = function(cb, after, ...args) {
        let func = function() { try { cb(); } catch (e) { console.error(e); } };
        return originSetTimeout(func, after, ...args);
    };
    var originSetInterval = global.setInterval;
    global.setInterval = function(cb, after, ...args) {
        let func = function() { try { cb(); } catch (e) { console.error(e); } };
        return originSetInterval(func, after, ...args);
    };
})(globalThis);
 
global = globalThis;
 
console.info("MagicBrush(JSC) init!");
