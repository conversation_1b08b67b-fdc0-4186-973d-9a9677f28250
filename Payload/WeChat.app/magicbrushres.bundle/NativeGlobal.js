(function () {
  // 默认从 Native 生成，如果 Native 没有生成，就声明一个
  if (typeof NativeGlobal === "undefined") {
    NativeGlobal = {};
  }

  var g = NativeGlobal;

  var mb = new magicbrush.GlobalUtils();
  var Canvas = magicbrush.Canvas;

  g.setTimeout = function (cb, t) {
    return mb.setTimeout(cb, t || 0);
  };
  g.setInterval = function (cb, t) {
    return mb.setInterval(cb, t || 0);
  };
  g.clearTimeout = function (id) {
    return mb.clearTimeout(id);
  };
  g.clearInterval = function (id) {
    return mb.clearInterval(id);
  };
  g.requestAnimationFrame = function (cb) {
    return mb.requestAnimationFrame(cb);
  };
  g.cancelAnimationFrame = function (id) {
    return mb.cancelAnimationFrame(id);
  };
  g.setPreferredFramesPerSecond = function (fps) {
    return mb.setPreferredFramesPerSecond(fps);
  };
  g.loadFont = function (path) {
    return mb.loadFont(path);
  };
  g.encodeArrayBuffer = function (str, code) {
    return mb.encodeArrayBuffer(str, code);
  };
  g.decodeArrayBuffer = function (buffer, code) {
    return mb.decodeArrayBuffer(buffer, code);
  };
  g.performanceNow = function () {
    return mb.performanceNow();
  };
  g.getTextLineHeight = function (style, weight, size, family, text) {
    return mb.getTextLineHeight(style, weight, size, family, text);
  };
  g.decodeUint64Array = function () {
    return mb.decodeUint64Array.apply(mb, arguments);
  };
  g.decodeVarintArray = function () {
    return mb.decodeVarintArray.apply(mb, arguments);
  };
  g.getSystemInfo = function () {
    return mb.getSystemInfo();
  };

  var Image = magicbrush.Image;
  const TouchInput = magicbrush.TouchInput;
  const touchEventNames = [
    "ontouchstart",
    "ontouchmove",
    "ontouchend",
    "ontouchcancel",
  ];
  g.Image = function (w, h) {
    var img = new Image(w, h);
    img.uid = img.__id();
    return img;
  };

  g.ScreenCanvas = function () {
    var c = new Canvas(true);
    c.id = c.__id;
    let touchInput = new TouchInput(c, c.id);

    touchEventNames.forEach(function (touchEventName) {
      var FUNCTION_STR = "function";
      touchInput[touchEventName] = function (
        touches,
        changedTouches,
        timestamp
      ) {
        if (typeof c[touchEventName] === FUNCTION_STR) {
          var event = {
            type: touchEventName,
            touches: touches,
            changedTouches: changedTouches,
            timeStamp: timestamp,
          };
          c[touchEventName].call(c, event);
        }
      };
    });
    return c;
  };

  g.OffscreenCanvas = function () {
    var c = new Canvas(false, false);
    c.id = c.__id;
    return c;
  };

  var global = (function () {
    return this;
  })();
  delete global.magicbrush;
})();

if (typeof NativeGlobal.WebAssembly !== "undefined") {
  let origin_wasm_instantiate = NativeGlobal.WebAssembly.instantiate;
  NativeGlobal.WebAssembly.instantiate = function (wasm, importObject) {
    return new Promise(function (resolve, reject) {
      let output = origin_wasm_instantiate(wasm, importObject);
      resolve(output);
    });
  };
}
