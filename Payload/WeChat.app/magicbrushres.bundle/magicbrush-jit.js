(function (global) {
    var readFileXhr = function () {
        var args = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
        var filePath = args.filePath
        var encoding = args.encoding
        var success = args.success
        var fail = args.fail
        
        var result;
        var xhr = new XMLHttpRequest();
        var reqUrl = "/fs/" + btoa(filePath)
        console.log(`req url: ${reqUrl}`)
        
        xhr.open('POST', reqUrl, true);
        xhr.setRequestHeader('content-type', 'application/json');
        
        if (typeof encoding === 'string') {
            xhr.responseType = 'text';
        } else {
            xhr.responseType = 'arraybuffer';
        }
        
        xhr.onload = function () {
            if (typeof encoding === 'string') {
                result = xhr.responseText;
            } else {
                result = xhr.response;
            }
            success({
            data: result
            });
        };
        
        xhr.onerror = function (err) {
            console.error(`wtf: {err}`)
            fail(err.message);
        };
        
        xhr.send(JSON.stringify({}));
    }
    global.readFileXhr = readFileXhr
    
    /*
        WebAssembly
     */
    if (global && typeof global.WebAssembly !== 'undefined') {
        const wasm = global.WebAssembly;
        const WebAssembly = {
            Table: wasm.Table,
            Memory: wasm.Memory,
            Global: wasm.Global,
            Instance: wasm.Instance,
            instantiate: function instantiate(file, imports) {
                if (typeof file !== 'string') {
                    return Promise.reject(new Error(
                        'WebAssembly.instantiate: first argument must be a string'));
                } else if (/^(wxfile|https?):/.test(file)) {
                    return Promise.reject(new Error(
                        'WebAssembly.instantiate: not support wxfile: or http: path'));
                } else if (!/\.wasm(\.br)?$/.test(file)) {
                    return Promise.reject(new Error(
                        'WebAssembly.instantiate: only support file type .wasm or .wasm.br'));
                }

                return new Promise(function (resolve, reject) {
                    if (global.readFileXhr) {
                        global.readFileXhr({
                        filePath: file,
                        success: (ret) => {
                            console.log("wasm readfile", ret);
                            resolve(wasm.instantiate(ret.data, imports));
                        },
                        fail: () => {
                            reject();
                        },
                        });
                    } else {
                        var res = global.__mbinternal__.InvokeHelper.invokeSync("readFileSync", {
                        filePath: file
                        });
                        if (res && res.data) {
                            resolve(wasm.instantiate(res.data, imports));
                        } else {
                            var errMsg = res.errMsg;
                            reject(new Error(errMsg.replace(/^\w+:fail\s*/, '')));
                        }
                    }
                });
            }
        };
        global.WebAssembly = WebAssembly;
        global.WXWebAssembly = WebAssembly;
    }
    
    /*
        NativeGlobal
     */
    let firstTime = true;

    let NativeGlobal = {
        // JIT目前不支持ScreenCanvas，所以永远只返回一个在屏的Canvas
        ScreenCanvas: function() {
            const canvas = document.createElement('canvas');

            if (firstTime) {
              canvas.id = 'canvas';
              document.body.appendChild(canvas);
              firstTime = false;
            } else {
              canvas.style.display = 'none';
            }

            canvas.addEventListener('touchstart', e => e.preventDefault());
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            Object.defineProperties(canvas, {
              id: {
                value: '',
                writable: true
              },
              style: {
                value: {},
                writable: true
              }
            });
            return canvas;
        },
        setPreferredFramesPerSecond: function() {
            // dummy
        },
        Image: function() {
            return document.createElement("image")
        }
    };
    global.NativeGlobal = NativeGlobal;
    
    /*
        WeixinJSBridge
     */
    global.mb.JSBridge = global.WeixinJSBridge;
    if (global.WeixinJSBridge) {
        global.WeixinJSBridge.log = function(...args) {
            let logLevel = 0
            let msg = ''
            if (args.length === 1) {
                msg = args[0]
            } else if (args.length === 2) {
                logLevel = args[0]
                msg = args[1]
            }
            global.__mbinternal__.InvokeHelper.invokeAsync("log", {logLevel: logLevel, msg: msg}, undefined, true);
        }
    }
    global.mb.switchVConsole = function(enable) {
        global.__mbinternal__.InvokeHelper.invokeAsync("vConsoleOp", {op: 2, enable}, undefined, true);
    }
    
    global.mb.vConsoleLog = function(logs) {
        global.__mbinternal__.InvokeHelper.invokeAsync("vConsoleOp", {op: 1, logs}, undefined, true);
    }
})(globalThis);

 console.info("MagicBrush(JIT) init!");
