 (function(self, undefined) {if (!("defineProperty"in Object&&function(){try{var e={}
     return Object.defineProperty(e,"test",{value:42}),!0}catch(t){return!1}}()
                                   )) {!function(e){var t=Object.prototype.hasOwnProperty.call(Object.prototype,"__defineGetter__"),r="A property cannot both have accessors and be writable or have a value";Object.defineProperty=function n(o,i,f){if(e&&(o===window||o===document||o===Element.prototype||o instanceof Element))return e(o,i,f);if(null===o||!(o instanceof Object||"object"==typeof o))throw new TypeError("Object.defineProperty called on non-object");if(!(f instanceof Object))throw new TypeError("Property description must be an object");var c=String(i),a="value"in f||"writable"in f,p="get"in f&&typeof f.get,s="set"in f&&typeof f.set;if(p){if(p===undefined)return o;if("function"!==p)throw new TypeError("Getter must be a function");if(!t)throw new TypeError("Getters & setters cannot be defined on this javascript engine");if(a)throw new TypeError(r);Object.__defineGetter__.call(o,c,f.get)}else o[c]=f.value;if(s){if(s===undefined)return o;if("function"!==s)throw new TypeError("Setter must be a function");if(!t)throw new TypeError("Getters & setters cannot be defined on this javascript engine");if(a)throw new TypeError(r);Object.__defineSetter__.call(o,c,f.set)}return"value"in f&&(o[c]=f.value),o}}(Object.defineProperty);}if (!("object"==typeof globalThis
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  )) {!function(){function e(e){return e&&e.Object==Object&&e}var t=e("object"==typeof c&&c),o=e("object"==typeof window&&window),b=e("object"==typeof self&&self),l=e("object"==typeof global&&global),c=t||o||b||l||Function("return this")();try{Object.defineProperty(c,"globalThis",{configurable:!0,enumerable:!1,writable:!0,value:c})}catch(n){c.globalThis=c}}();}})('object' === typeof window && window || 'object' === typeof self && self || 'object' === typeof global && global || {});
(function (global) {
    if (!global.__mbinternal__) {
        global.__mbinternal__ = {};
    }
    /*
        Object Define
     */
    let ScreenCanvas;
    
    const InvokeHelper = {
        invokeSync: function(event, param, internal = false) {
            let result;
            const callback = function(res) {
                if (res && typeof res.errMsg === "string") {
                    if(res.errMsg.indexOf(`${event}:fail`) !== 0) {
                        // !fail
                        result = res;
                    }
                }
            }
            
            const eventName = internal ? "mb_internal_" + event : event;
            global.mb.JSBridge.invoke(eventName, param, callback);
            return result;
        },
        invokeAsync: function(event, param, callbacks, internal = false) {
            const callback = function(res) {
                if (res && typeof res.errMsg === "string") {
                    if(res.errMsg.indexOf(`${event}:fail`) !== 0) {
                        // !fail
                        if (callbacks && typeof callbacks.succees === "function") {
                            callbacks.success(res);
                        }
                    } else {
                        if (callbacks && typeof callbacks.fail === "function") {
                            callbacks.fail(res);
                        }
                    }
                }
            }
            const eventName = internal ? "mb_internal_" + event : event;
            global.mb.JSBridge.invoke(eventName, param, callback);
        }
    }
    global.__mbinternal__.InvokeHelper = InvokeHelper;
    
    /*
        mb
     */
    if (typeof global.mb === "undefined") {
        global.mb = {};
    }
    
    /*
       Console
     */
    var originInfo = global.console.info;
    var originWarn = global.console.warn;
    var originError = global.console.error;
    
    global.console.info = function(...args) {
        global.mb.JSBridge.log(2, args.toString());
        originInfo(...args);
    }
    
    global.console.warn = function(...args) {
        global.mb.JSBridge.log(3, args.toString());
        originWarn(...args);
    }
    
    global.console.error = function(...args) {
        global.mb.JSBridge.log(4, args.toString());
        originError(...args);
    }
    
    // 只在debug版将前端的console.log和console.debug打印到客户端xlog
    if (global.__mbinternal__.debug) {
        var originLog = global.console.log;
        var originDebug = global.console.debug;
        
        global.console.log = function(...args) {
            global.mb.JSBridge.log(1, args.toString());
            originLog(...args);
        }
        
        global.console.debug = function(...args) {
            global.mb.JSBridge.log(0, args.toString());
            originDebug(...args);
        }
    }
})(globalThis);
