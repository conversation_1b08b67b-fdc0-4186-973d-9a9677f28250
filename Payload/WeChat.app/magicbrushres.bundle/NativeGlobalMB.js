(function(global) {
  let OriginCanvas = NativeGlobal.ScreenCanvas;
  if (!OriginCanvas) return;
  var canvas = function() {
    let c = new OriginCanvas();
    c.id = c.__wid;
    return c;
  }
  NativeGlobal.Canvas = canvas;
  NativeGlobal.ScreenCanvas = canvas;
  global.ScreenCanvas = canvas;
  global.Image = NativeGlobal.Image;
})(this);

 if (typeof NativeGlobal.WebAssembly !== "undefined") {
   let origin_wasm_instantiate = NativeGlobal.WebAssembly.instantiate;
   NativeGlobal.WebAssembly.instantiate = function (wasm, importObject) {
     return new Promise(function (resolve, reject) {
       let output = origin_wasm_instantiate(wasm, importObject);
       resolve(output);
     });
   };
 }
