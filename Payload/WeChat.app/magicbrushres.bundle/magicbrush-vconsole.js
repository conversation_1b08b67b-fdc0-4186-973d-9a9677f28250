(function (global) {
  const Object$$toString = /* #__PURE__ */ (() => Object.prototype.toString)();
  const TO_STRING =
    /* #__PURE__ */ Function.prototype.call.bind(Object$$toString);

  function getDataType(data) {
    return TO_STRING(data).slice(8, -1); // Proxy 暂不考虑吧
  }

  // NOTICE: 跨context情况下，使用instanceof可能无法准确判断类型
  function safeInstanceOf(x, constructor) {
    // eslint-disable-next-line
    if (x == null) return false;
    // eslint-disable-next-line
    return (
      x instanceof constructor ||
      (x.constructor != null && x.constructor.name === constructor.name)
    );
  }
  // basic
  const isString = (x) => getDataType(x) === "String";
  const isNumber = (x) => getDataType(x) === "Number";
  const isBoolean = (x) =>
    x === true || x === false || getDataType(x) === "Boolean";
  const isUndefined = (x) => x === undefined;
  const isNull = (x) => x === null;
  const isNaN = /* #__PURE__ */ (() => Number.isNaN || ((x) => x !== x))();
  const isFinite = /* #__PURE__ */ (() =>
    Number.isFinite || ((x) => isNumber(x) && global.isFinite(x)))();
  const isInfinity = (x) => isNumber(x) && Math.abs(x) === Infinity;

  const isInteger = (value) => isFinite(value) && Math.floor(value) === value;

  const isBasicValue = (x) =>
    ["string", "number", "boolean", "undefined"].includes(typeof x);

  // Object
  const isObject = (x) => getDataType(x) === "Object";
  const isNonNullObject = (x) => isObject(x) && !isNull(x);
  const isJustObject = (x) => getDataType(x) === "Object";
  const isArray = /* #__PURE__ */ (() =>
    Array.isArray || ((x) => getDataType(x) === "Array"))();
  const isFunction = (x) => typeof x === "function";
  const isDate = (x) => getDataType(x) === "Date";
  const isRegExp = (x) => getDataType(x) === "RegExp";
  const isError = (x) => getDataType(x) === "Error";
  const isSymbol = (x) => getDataType(x) === "Symbol";
  const isMap = (x) => getDataType(x) === "Map";
  const isWeakMap = (x) => getDataType(x) === "WeakMap";
  const isSet = (x) => getDataType(x) === "Set";
  const isWeakSet = (x) => getDataType(x) === "WeakSet";
  const isPromise = (x) => getDataType(x) === "Promise";
  const isEmptyObject = (x) => {
    // eslint-disable-next-line
    for (const p in x) return false;
    return true;
  };

  // binary
  // export const isArrayBuffer = x => safeInstanceOf(x, ArrayBuffer)
  const isArrayBuffer = (x) => getDataType(x) === "ArrayBuffer";
  // export const isDataView = x => ArrayBuffer.isView(x) && safeInstanceOf(x, DataView)
  const isDataView = (x) => getDataType(x) === "DataView";
  // export const isTypedArray = x => ArrayBuffer.isView(x) && !safeInstanceOf(x, DataView)
  const isTypedArray = (x) => ArrayBuffer.isView(x) && !isDataView(x);

  // wx Type
  const isVirtualNode = (x) => x && x.type === "WxVirtualNode";
  const isVirtualText = (x) => x && x.type === "WxVirtualText";

  /* 必填参数的类型校验 */
  function paramCheck(value, expect, dept = "parameter") {
    const type = getDataType(expect);
    const valueType = getDataType(value);
    if (valueType !== type) {
      return `${dept} should be ${type} instead of ${valueType};`;
    }
    let result = "";
    switch (type) {
      case "Object":
        Object.keys(expect).forEach((key) => {
          result += paramCheck(value[key], expect[key], `${dept}.${key}`);
        });
        break;
      case "Array":
        if (value.length < expect.length) {
          return `${dept} should have at least ${expect.length} item;`;
        }
        for (let i = 0; i < expect.length; ++i) {
          result += paramCheck(value[i], expect[i], `${dept}[${i}]`);
        }
        break;
      default:
        break;
    }
    return result;
  }

  function safelyToString(value) {
    return JSON.stringify(value);
  }

  function getTypeKey(val) {
    const dataType = getDataType(val);
    if (dataType === "Number") {
      if (isNaN(val)) return "NaN";
      else if (isInfinity(val)) return "Infinity";
    } else if (dataType === "Object") {
      if (isNull(val)) return "Null";
    } else if (
      dataType.endsWith("Array") &&
      dataType !== "Array" &&
      isTypedArray(val)
    ) {
      return "TypedArray";
    }

    return dataType;
  }

  // vConsole 中使用 [xxx] 打印日志会被吞掉
  const mapping = {
    String: 0,
    NaN: "<NaN>",
    Infinity: (val) => (val > 0 ? "<Infinity>" : "<-Infinity>"),
    Number: 0,
    Boolean: 0,
    Null: 0,
    Undefined: "<Undefined>",

    Function: (val) =>
      val.name === "" ? "<Function>" : `<Function: ${val.name}>`,
    Date: (val) => `<Date: ${val.toJSON()}>`,
    RegExp: (val) => `<RegExp: ${val.toString()}>`,
    Error: (val) => `<${val.name}: ${val.message}>`,
    Symbol: (val) => `<Symbol: ${val.toString()}>`,
    Promise: "<Promise>",

    Map: (val) => `<Map: size=${val.size}>`,
    WeakMap: "<WeakMap>",
    Set: (val) => `<Set: size=${val.size}>`,
    WeakSet: "<WeakSet>",

    ArrayBuffer: (val) => `<ArrayBuffer: byteLength=${val.byteLength}>`,
    DataView: (val) =>
      `<DataView: byteLength=${val.byteLength}, byteOffset=${val.byteOffset}>`,
    TypedArray: (val) =>
      `<${val.constructor && val.constructor.name
        ? val.constructor.name
        : "TypedArray"
      }: byteLength=${val.byteLength}, length=${val.length}>`,
  };

  // object, array, number, string, boolean, and null
  // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify#toJSON()_behavior
  function getMappedValue(val) {
    const typeKey = getTypeKey(val);
    if (typeKey in mapping) {
      if (!mapping[typeKey]) return [val, true];
      else if (!isFunction(mapping[typeKey])) return [mapping[typeKey], true];
      else return [mapping[typeKey](val), true];
    }
    return [null, false];
  }

  function decycleAndToJSONable(object) {
    const refs = new WeakMap();
    let refsSize = 0;
    return (function derez(val, path) {
      let res;
      const [mappedVal, isMapped] = getMappedValue(val);
      if (!isMapped) {
        if (refsSize > 3000) {
          return "<Hidden>";
        }
        if (refs.has(val)) {
          return `<Circular: ${refs.get(val)}>`;
        }

        refs.set(val, path);
        refsSize++;

        if (isArray(val))
          return val.map((ele, i) => derez(ele, `${path}[${i}]`));
        res = {};

        Object.keys(val).forEach((key) => {
          res[key] = derez(val[key], `${path}.${key}`);
        });

        return res;
      } else {
        return mappedVal;
      }
    })(object, "@");
  }

  function transformLogArgs(args) {
    try {
      args = Array.prototype.slice.call(args); // in case of arguments
      return args.map(decycleAndToJSONable);
    } catch (e) {
      console.warn("[console] This object can not be logged");
      return undefined;
    }
  }

  const LOG_LEVEL = {
    LOG: 0,
    INFO: 1,
    WARNING: 2,
    ERROR: 3,
    DEBUG: 4,
    TIME: 5,
    TIME_END: 5,
  };

  const LOG_LEVEL_TEXT = {
    LOG: "log",
    INFO: "info",
    WARNING: "warn",
    ERROR: "error",
    DEBUG: "debug",
    TIME: "time",
    TIME_END: "timeEnd",
  };

  const MAX_LOG_LENGTH = 1024 * 1024;
  const MAX_LOG_LENGTH_DEBUG = 2 * 1024 * 1024;
  function getNativeGlobalLog(level, args, isDebugOn = true) {
    // 若未开启调试面板，只输出 warning 和 error
    try {
      const logs = transformLogArgs(args);
      if (typeof logs === "undefined") return undefined;
      const result = JSON.stringify({ level, logs }); // level 多出一点点就忽略吧
      if (result.length > MAX_LOG_LENGTH && !isDebugOn) {
        return JSON.stringify({ level, logs: ["<LOG_EXCEED_MAX_LENGTH>"] });
      } else if (result.length > MAX_LOG_LENGTH_DEBUG && isDebugOn) {
        return JSON.stringify({ level, logs: ["<LOG_EXCEED_MAX_LENGTH>"] });
      } else {
        return result;
      }
    } catch (e) {
      console.warn("[console] This object can not be logged");
      return undefined;
    }
  }

  const _log = function (level, args) {
    const logData = getNativeGlobalLog(level, args);
    if (logData) global.mb.vConsoleLog(logData);
  };
  global._log = _log;
})(globalThis);

(function (global) {
  if (!global.__mbinternal__) {
    global.__mbinternal__ = {};
  }
  const _log = global._log;
  const originConsole = global.console;

  const LOG_LEVEL = {
    LOG: 0,
    INFO: 1,
    WARNING: 2,
    ERROR: 3,
    DEBUG: 4,
    TIME: 5,
    TIME_END: 5,
  };

  global.console = {
    log() {
      _log(LOG_LEVEL.LOG, arguments);
      originConsole.log(...arguments);
    },
    info() {
      _log(LOG_LEVEL.INFO, arguments);
      originConsole.info(...arguments);
    },
    warn() {
      _log(LOG_LEVEL.WARNING, arguments);
      originConsole.warn(...arguments);
    },
    error() {
      _log(LOG_LEVEL.ERROR, arguments);
      originConsole.error(...arguments);
    },
    debug() {
      _log(LOG_LEVEL.DEBUG, arguments);
      originConsole.debug(...arguments);
    },
    time() {
      _log(LOG_LEVEL.TIME, arguments);
      originConsole.time(...arguments);
    },
    timeEnd() {
      _log(LOG_LEVEL.TIME_END, arguments);
      originConsole.timeEnd(...arguments);
    },
    group() { },
    groupEnd() { },
  };
})(globalThis);
