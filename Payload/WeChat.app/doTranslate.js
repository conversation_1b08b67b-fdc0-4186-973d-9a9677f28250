 (function() {

     // don't repeated inject
     if (window.WeixinTranslate) {
         return;
     }
     // private variable
     var _xxyy = "__wx_trans_xxyy";
     var _needTransNodeList = [];
     var _nodeMap = new Map();
     var _transTimeInterval;
     var _transTimeIntervalNum = 0;
     var _mutationObserver = new MutationObserver((mutations, observer) => {
         mutations.forEach(function(mutation) {
             textNodesUnder(mutation.target);
         });
         sendPack();
     });

     // functions
     function postMessage(action,data){
         window.webkit.messageHandlers.transHandler.postMessage({
             action:action,
             randomID:_xxyy,
             data:data
         });
     }
     function sendPack(){
         var needSendArray = [];
         _needTransNodeList.forEach(function(node){
             var needSendNode = new Map();
             needSendNode['text'] = node.WX_Trans_Info.srcText;
             needSendNode['hashID'] = node.WX_Trans_Info.hash;
             needSendNode['isFirstScreen'] = checkInScreen(node);
             needSendNode['isTitle'] = node.WX_Trans_Info.isTitle;
             needSendNode['tagName'] = node.WX_Trans_Info.tagName;
             needSendNode['styleList'] = node.WX_Trans_Info.styleList;
             needSendNode['parentId'] = node.WX_Trans_Info.parentId;
             needSendNode['grandParentId'] = node.WX_Trans_Info.grandParentId;
             needSendNode['greatGrandParentId'] = node.WX_Trans_Info.greatGrandParentId;
             needSendNode['grandTagname'] = node.WX_Trans_Info.grandTagname;
             needSendNode['greatGrandParentTagname'] = node.WX_Trans_Info.greatGrandParentTagname;

             needSendArray.push(needSendNode);
         });
         if (needSendArray.length!==0) {
             postMessage('trans',needSendArray);
             //清空
             _needTransNodeList = [];
         }
     }

     function checkInScreen(node) {
         const visibleBottom = document.documentElement.clientHeight;
         const visibleTop = 0;
         let totalTop = null, par = node.offsetParent;
         //首先把自己本身的进行累加
         totalTop += node.parentElement.offsetTop;
         //只要没有找到body，我们就把父级参照物的边框和偏移量累加
         while (par) {
             if (navigator.userAgent.indexOf("MSIE 8.0") === -1) {
                 //不是标准的ie8浏览器，才进行边框累加
                 //累加父级参照物边框
                 totalTop += par.clientTop;
             }
             //累加父级参照物本身的偏移
             totalTop += par.offsetTop;
             par = par.offsetParent;
         }
         return totalTop > visibleTop && totalTop < visibleBottom;
     }

     var _hashIDSequence = 0;
     function genHash(node){
         return (++_hashIDSequence)+"";
     }

     function safeSetNodeHash(node) {
         if (node !== undefined && node.WX_Trans_id === undefined) {
             node.WX_Trans_id = genHash(node)
         }
     }

     function validTagName(tagName) {
         const tagFilter = new Set(['SPAN', 'A', 'P', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6'
             , 'B', 'I', 'FIGCAPTION', 'DIV', 'EM', 'STRONG', 'LI','FONT','TABLE','TR','TH'
             , 'TD', 'TITLE', 'SECTION', 'LABEL', 'INPUT', 'BUTTON', 'Q', 'CITE', 'BLOCKQUOTE', 'PRE']);
         return tagFilter.has(tagName);
     }

     function processNode(node, isTitle){
         if(node === undefined){
             return;
         }
         if (node.WX_Trans_Info!==undefined && node.WX_Trans_Info.hasGoThrough !== undefined) {
             return;
         }
         var WX_Trans_Info_obj = {};
         WX_Trans_Info_obj.hash = genHash(node);
         WX_Trans_Info_obj.hasGoThrough = true;
         WX_Trans_Info_obj.hasTransed = false;
         WX_Trans_Info_obj.isTitle = isTitle;
         if(node.WX_Trans_Info === undefined||node.WX_Trans_Info.srcText === undefined){
             WX_Trans_Info_obj.srcText = node.nodeValue;
         }
         if(node.WX_Trans_Info !== undefined && node.WX_Trans_Info.srcText !== undefined){
             WX_Trans_Info_obj.srcText = node.WX_Trans_Info.srcText;
         }
         WX_Trans_Info_obj.tagName = node.parentNode.tagName;

         var extraInfo = processExtraInfo(node);
         WX_Trans_Info_obj.styleList = mapToJson(extraInfo.styleList)
         WX_Trans_Info_obj.parentId = extraInfo.parentId
         WX_Trans_Info_obj.grandParentId = extraInfo.grandParentId
         WX_Trans_Info_obj.greatGrandParentId = extraInfo.greatGrandParentId
         WX_Trans_Info_obj.grandTagname = extraInfo.grandTagname
         WX_Trans_Info_obj.greatGrandParentTagname = extraInfo.greatGrandParentTagname

         node.WX_Trans_Info = WX_Trans_Info_obj;
         _needTransNodeList.push(node);
         _nodeMap.set(node.WX_Trans_Info.hash, node);
     }

     function processExtraInfo(txtNode) {
         var elementNode = txtNode
         var parentNode = elementNode.parentNode
         var grandParentNode = parentNode.parentNode
         var greatGrandParentNode = grandParentNode.parentNode

         safeSetNodeHash(parentNode)
         safeSetNodeHash(grandParentNode)
         safeSetNodeHash(greatGrandParentNode)

         if (parentNode !== undefined) {
             const parentNodeStyle = window.getComputedStyle(parentNode)
             var styleMap = new Map()
             if (parentNodeStyle !== undefined) {
                 styleMap.set("fontSize", parentNodeStyle.fontSize)
                 styleMap.set("fontWeight", parentNodeStyle.fontWeight)
                 styleMap.set("fontStyle", parentNodeStyle.fontStyle)
                 styleMap.set("textDecoration", parentNodeStyle.textDecorationLine)
                 styleMap.set("color", parentNodeStyle.color)
             }
         }

         var extraInfo = {}
         extraInfo.styleList = styleMap
         if (parentNode !== undefined) {
             extraInfo.parentId = parentNode.WX_Trans_id
         }
         if (grandParentNode !== undefined) {
             extraInfo.grandParentId = grandParentNode.WX_Trans_id
             extraInfo.grandTagname = grandParentNode.tagName
         }
         if (greatGrandParentNode !== undefined) {
             extraInfo.greatGrandParentId = greatGrandParentNode.WX_Trans_id
             extraInfo.greatGrandParentTagname = greatGrandParentNode.tagName
         }

         return extraInfo
     }

     function strMapToObj(strMap){
         let obj= Object.create(null);
         for (let[k,v] of strMap) {
             obj[k] = v;
         }
         return obj;
     }

     function mapToJson(map) {
         return JSON.stringify(strMapToObj(map));
     }

     function textNodesUnder(node) {
         if (node == null || node.firstChild == null){
             return;
         }
         for (node = node.firstChild; node; node = node.nextSibling) {
             if (node.nodeType === 3 && validTagName(node.parentElement.tagName) && node.nodeValue.replace(/\s+/, '') !== "") {
                 processNode(node,false)
             } else {
                 textNodesUnder(node)
             }
         }
     }
     
     function processTitle(){
         let title = document.getElementsByTagName('title')
         if (title == undefined) {
             return
         }
         title = title[0]
         if (title == undefined) {
             return
         }
         title = title.childNodes
         if (title == undefined) {
             return
         }
         title = title[0]
         //公众号文章的titleNode比较特殊，要先判断一下
         processNode(title,true)
     }
     
     function goThroughDOM(){
         processTitle()
         textNodesUnder(document.body);
     }
     
     function clearTransInfo(node) {
         node.WX_Trans_Info.hash = undefined;
         node.WX_Trans_Info.transText = undefined;
         node.WX_Trans_Info.hasGoThrough = undefined;
         node.WX_Trans_Info.hasTransed = undefined;
         node.WX_Trans_Info.isTitle = undefined;
         node.WX_Trans_Info.tagName = undefined;
         node.WX_Trans_Info.styleList = undefined;
         node.WX_Trans_Info.parentId = undefined;
         node.WX_Trans_Info.grandParentId = undefined;
         node.WX_Trans_Info.greatGrandParentId = undefined;
         node.WX_Trans_Info.grandTagname = undefined;
         node.WX_Trans_Info.greatGrandParentTagname = undefined;
         //scrText不能重置，否则取消翻译还原时就找不到了
     }

     function startTrans(){
         //*1.先重置
         clearTransIntervalAndObserver();
         _nodeMap.forEach(function(value,key){
             if(value.WX_Trans_Info != undefined){
                 clearTransInfo(value)
             }
         });
         _nodeMap = new Map();
         //*2.页面完全加载前主动遍历DOM
         _transTimeInterval = setInterval(function () {

             if(_transTimeIntervalNum > 100){
                 clearInterval(_transTimeInterval);
                 return;
             }
             _transTimeIntervalNum++;
             //在observer接管前要保证至少主动遍历一次！
             goThroughDOM();
             sendPack();
             if(document.readyState === 'complete'){
                 clearInterval(_transTimeInterval);
                 //*3.observer开始观察，在页面完全加载后生效
                 _mutationObserver.observe(document.documentElement, {
                     attributes: true,
                     characterData: true,
                     childList: true,
                     subtree: true,
                 });
             }
         },200);
     }

     function clearTransIntervalAndObserver(){
         if(_mutationObserver!==null){
             _mutationObserver.disconnect();
         }
         if(_transTimeInterval!==null){
             clearInterval(_transTimeInterval);
         }
         _transTimeIntervalNum = 0;
     }

     function revertTrans(){
         clearTransIntervalAndObserver();
         _nodeMap.forEach(function (value,key,map) {
             if(value.WX_Trans_Info!==undefined&&value.WX_Trans_Info.srcText!==undefined){
                 value.nodeValue = value.WX_Trans_Info.srcText;
                 value.WX_Trans_Info = undefined;
             }
         });
         _nodeMap = new Map();
     }

     function replaceText(jsonStr) {
         if(jsonStr instanceof Array == false){
             return;
         }
         var textWithHashList = jsonStr;
         textWithHashList.forEach(function(dic){
             var hash = dic['hashCode'];
             var transText = dic['transText'];
             var node = _nodeMap.get(hash);
             if(node != undefined){
                 node.nodeValue = transText;
                 if (node.WX_Trans_Info != undefined){
                     node.WX_Trans_Info.transText = transText;
                     node.WX_Trans_Info.hasTransed = true;//如果发现没有翻译，方便检查错误，看是不是后台的问题
                 }
             }
         });
     }

     // public interface
     var _WeixinTranslate = {
         startTrans : startTrans,
         revertTrans : revertTrans,
         replaceText : replaceText,
     }

     // define window object
     if (!window.WeixinTranslate) {
         try {
             Object.defineProperty(window, 'WeixinTranslate', {
                 value : _WeixinTranslate,
                 writable : false,
                 configurable : false,
             })
         } catch (e) {
             console.log('WeixinTranslate setup failure!')
         }
     }
 })();
