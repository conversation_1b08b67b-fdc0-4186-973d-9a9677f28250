(function(){function I(c){__ISWKWEBVIEW?(D.push(c),c=N(),window.weixinPostMessageHandlers.weixinDispatchMessage.postMessage(c)):(D.push(c),document.location=aa)}function N(){if(B._fetchQueue!==ba)return"";var c=t.stringify(D);D=[];var d=[];d[0]=c;d[1]=J;var a=d.join(""),d="";"yes"===O&&(d=K.SHA1(a).toString());a={};a[ca]=c;a[R]=d;return t.stringify(a)}function S(c){if(B._handleMessageFromWeixin!==da)return"{}";var d;d=c[ea];var c=c[R],a=[];a[0]=d;a[1]=J;var a=a.join(""),b="";if("yes"===O&&(b=K.SHA1(a).toString(),
b!==c))return"{}";var i;try{i=t.parse(d)}catch(n){i="undefined"!==typeof window.weixinJSONParse?window.weixinJSONParse(d):window.JSON.parse(d)}switch(i[T]){case "callback":return"string"===typeof i[G]&&"function"===typeof L[i[G]]?(d=L[i[G]](i.__params),delete L[i[G]],t.stringify(d)):t.stringify({__err_code:"cb404"});case "event":"object"===typeof i[U]&&(V=i[U]);W=i[X];if("string"===typeof i[H]){if("function"===typeof E[i[H]]&&M(i[H]))return d=E[i[H]](i.__params),t.stringify(d);if("function"===typeof F[i[H]])return d=
F[i[H]](i.__params),t.stringify(d)}return t.stringify({__err_code:"ev404"})}return"{}"}function M(c){return V.some(function(d){return d===c})}function x(c){if(B.log===fa){for(var d=[],a=0;a<arguments.length;a++)d.push(arguments[a]);var a=d.shift(),b;try{d.unshift(a),b=P.apply(null,d)}catch(i){b=c}q("log",{msg:b})}}function q(c,d,a){if(B.call===ga&&c&&"string"===typeof c){"object"!==typeof d&&(d={});var b=(ha++).toString();"function"===typeof a&&(L[b]=a);a=[];a[0]=W;a[1]=J;var a=a.join(""),i="";"yes"===
O&&(i=K.SHA1(a).toString());d[X]=i;c={func:c,params:d};c[T]="call";c[G]=b;I(t.stringify(c))}}function A(c,d){c&&"string"===typeof c&&"function"===typeof d&&(F[c]=d)}function Y(c,d){B.on===ia&&c&&"string"===typeof c&&"function"===typeof d&&(E[c]=d)}function C(c,d){if("function"===typeof E[c]&&M(c))E[c](d);else if("function"===typeof F[c])F[c](d)}function ja(c,d){var a=new Image,b=!1;a.onload=function(){b||(b=!0,d(a))};a.src=c;setTimeout(function(){b||(b=!0,d(a))},1E3)}function ka(c,d){ja(c.src,function(a){var b=
/^data:image\/(png|jpg|jpeg|tiff|gif|bmp);base64,/i,i="";if(a.src.match(b))i=a.src;else if(629145.6>a.width*a.height){var n=document.createElement("canvas");n.width=a.width;n.height=a.height;n.getContext("2d").drawImage(a,0,0);var f="jpg",j=a.src.match(/\.(png|jpg|jpeg|tiff|gif|bmp)$/i);j&&(f=j[1].toLowerCase());try{i=n.toDataURL("image/"+f)}catch(k){x(k.message)}}d(i.replace(b,""),a,c)})}function la(){for(var c=p("audio"),d=0;d<c.length;d++)if(!c[d].paused&&!c[d].ended){q("audioStateChanged",{state:"play"});
break}c.on("play",function(){q("audioStateChanged",{state:"play"})});c.on("ended",function(){q("audioStateChanged",{state:"ended"})});c.on("pause",function(){q("audioStateChanged",{state:"pause"})});c=p("video");c.on("play",function(){q("videoStateChanged",{state:"play"})});c.on("ended",function(){q("videoStateChanged",{state:"ended"})});c.on("pause",function(){q("videoStateChanged",{state:"pause"})})}function Z(){var c=window.getSelection().toString();if(c&&0<c.length)return c;for(var d=document.querySelectorAll("iframe"),
a=0;a<d.length;++a)try{var b=d[a].contentWindow.getSelection().toString();if(0<b.length){c=b;break}}catch(i){}return c}function $(c){var d=document.createElement("a");d.href=location.href;d.href=c;return d.href}var p=function(){function c(e){return"[object Function]"==G.call(e)}function d(e){return e instanceof Object}function a(e){var a,b;if("[object Object]"!==G.call(e))return!1;b=c(e.constructor)&&e.constructor.prototype;if(!b||!hasOwnProperty.call(b,"isPrototypeOf"))return!1;for(a in e);return a===
o||hasOwnProperty.call(e,a)}function b(e){return e instanceof Array}function i(e){return"number"==typeof e.length}function n(e){return e.filter(function(e){return e!==o&&null!==e})}function f(e){return e.replace(/::/g,"/").replace(/([A-Z]+)([A-Z][a-z])/g,"$1_$2").replace(/([a-z\d])([A-Z])/g,"$1_$2").replace(/_/g,"-").toLowerCase()}function j(e){return e in q?q[e]:q[e]=RegExp("(^|\\s)"+e+"(\\s|$)")}function k(e,a){return a===o?h(e):h(e).filter(a)}function m(e,a,b,g){return c(a)?a.call(e,b,g):a}function l(e,
a,b){var c=e%2?a:a.parentNode;c?c.insertBefore(b,!e?a.nextSibling:1==e?c.firstChild:2==e?a:null):h(b).remove()}function r(e,a){a(e);for(var b in e.childNodes)r(e.childNodes[b],a)}var o,g,h,w,s=[],u=s.slice,y=window.document,p={},q={},t=y.defaultView.getComputedStyle,x={"column-count":1,columns:1,"font-weight":1,"line-height":1,opacity:1,"z-index":1,zoom:1},z=/^\s*<(\w+|!)[^>]*>/,A=[1,3,8,9,11],Q=y.createElement("table"),B=y.createElement("tr"),C={tr:y.createElement("tbody"),tbody:Q,thead:Q,tfoot:Q,
td:B,th:B,"*":y.createElement("div")},E=/complete|loaded|interactive/,H=/^\.([\w-]+)$/,I=/^#([\w-]+)$/,K=/^[\w-]+$/,G={}.toString,v={},F,D,J=y.createElement("div");v.matches=function(e,a){if(!e||1!==e.nodeType)return!1;var b=e.webkitMatchesSelector||e.mozMatchesSelector||e.oMatchesSelector||e.matchesSelector;if(b)return b.call(e,a);var c;c=e.parentNode;(b=!c)&&(c=J).appendChild(e);c=~v.qsa(c,a).indexOf(e);b&&J.removeChild(e);return c};F=function(e){return e.replace(/-+(.)?/g,function(e,a){return a?
a.toUpperCase():""})};D=function(e){return e.filter(function(a,b){return e.indexOf(a)==b})};v.fragment=function(e,a){a===o&&(a=z.test(e)&&RegExp.$1);a in C||(a="*");var b=C[a];b.innerHTML=""+e;return h.each(u.call(b.childNodes),function(){b.removeChild(this)})};v.Z=function(e,a){e=e||[];e.__proto__=arguments.callee.prototype;e.selector=a||"";return e};v.isZ=function(e){return e instanceof v.Z};v.init=function(e,g){if(e){if(c(e))return h(y).ready(e);if(v.isZ(e))return e;var w;if(b(e))w=n(e);else if(a(e))w=
[h.extend({},e)],e=null;else if(0<=A.indexOf(e.nodeType)||e===window)w=[e],e=null;else if(z.test(e))w=v.fragment(e.trim(),RegExp.$1),e=null;else{if(g!==o)return h(g).find(e);w=v.qsa(y,e)}return v.Z(w,e)}return v.Z()};h=function(e,a){return v.init(e,a)};h.extend=function(e){u.call(arguments,1).forEach(function(a){for(g in a)a[g]!==o&&(e[g]=a[g])});return e};v.qsa=function(e,a){var b;return e===y&&I.test(a)?(b=e.getElementById(RegExp.$1))?[b]:s:1!==e.nodeType&&9!==e.nodeType?s:u.call(H.test(a)?e.getElementsByClassName(RegExp.$1):
K.test(a)?e.getElementsByTagName(a):e.querySelectorAll(a))};h.isFunction=c;h.isObject=d;h.isArray=b;h.isPlainObject=a;h.inArray=function(e,a,b){return s.indexOf.call(a,e,b)};h.trim=function(e){return e.trim()};h.uuid=0;h.map=function(e,a){var b,c=[],g;if(i(e))for(g=0;g<e.length;g++)b=a(e[g],g),null!=b&&c.push(b);else for(g in e)b=a(e[g],g),null!=b&&c.push(b);return 0<c.length?[].concat.apply([],c):c};h.each=function(e,a){var b;if(i(e))for(b=0;b<e.length&&!1!==a.call(e[b],b,e[b]);b++);else for(b in e)if(!1===
a.call(e[b],b,e[b]))break;return e};h.fn={forEach:s.forEach,reduce:s.reduce,push:s.push,indexOf:s.indexOf,concat:s.concat,map:function(e){return h.map(this,function(a,b){return e.call(a,b,a)})},slice:function(){return h(u.apply(this,arguments))},ready:function(e){E.test(y.readyState)?e(h):y.addEventListener("DOMContentLoaded",function(){e(h)},!1);return this},get:function(e){return e===o?u.call(this):this[e]},toArray:function(){return this.get()},size:function(){return this.length},remove:function(){return this.each(function(){null!=
this.parentNode&&this.parentNode.removeChild(this)})},each:function(e){this.forEach(function(a,b){e.call(a,b,a)});return this},filter:function(e){return h([].filter.call(this,function(a){return v.matches(a,e)}))},add:function(e,a){return h(D(this.concat(h(e,a))))},is:function(e){return 0<this.length&&v.matches(this[0],e)},not:function(e){var a=[];if(c(e)&&e.call!==o)this.each(function(b){e.call(this,b)||a.push(this)});else{var b="string"==typeof e?this.filter(e):i(e)&&c(e.item)?u.call(e):h(e);this.forEach(function(e){0>
b.indexOf(e)&&a.push(e)})}return h(a)},eq:function(e){return-1===e?this.slice(e):this.slice(e,+e+1)},first:function(){var e=this[0];return e&&!d(e)?e:h(e)},last:function(){var e=this[this.length-1];return e&&!d(e)?e:h(e)},find:function(e){var a;a=1==this.length?v.qsa(this[0],e):this.map(function(){return v.qsa(this,e)});return h(a)},closest:function(e,a){for(var b=this[0];b&&!v.matches(b,e);)b=b!==a&&b!==y&&b.parentNode;return h(b)},parents:function(e){for(var a=[],b=this;0<b.length;)b=h.map(b,function(e){if((e=
e.parentNode)&&e!==y&&0>a.indexOf(e))return a.push(e),e});return k(a,e)},parent:function(e){return k(D(this.pluck("parentNode")),e)},children:function(e){return k(this.map(function(){return u.call(this.children)}),e)},siblings:function(e){return k(this.map(function(e,a){return u.call(a.parentNode.children).filter(function(e){return e!==a})}),e)},empty:function(){return this.each(function(){this.innerHTML=""})},pluck:function(e){return this.map(function(){return this[e]})},show:function(){return this.each(function(){"none"==
this.style.display&&(this.style.display=null);if("none"==t(this,"").getPropertyValue("display")){var e=this.style,a=this.nodeName,b,c;p[a]||(b=y.createElement(a),y.body.appendChild(b),c=t(b,"").getPropertyValue("display"),b.parentNode.removeChild(b),"none"==c&&(c="block"),p[a]=c);e.display=p[a]}})},replaceWith:function(e){return this.before(e).remove()},wrap:function(e){return this.each(function(){h(this).wrapAll(h(e)[0].cloneNode(!1))})},wrapAll:function(e){this[0]&&(h(this[0]).before(e=h(e)),e.append(this));
return this},unwrap:function(){this.parent().each(function(){h(this).replaceWith(h(this).children())});return this},clone:function(){return h(this.map(function(){return this.cloneNode(!0)}))},hide:function(){return this.css("display","none")},toggle:function(e){return(e===o?"none"==this.css("display"):e)?this.show():this.hide()},prev:function(){return h(this.pluck("previousElementSibling"))},next:function(){return h(this.pluck("nextElementSibling"))},html:function(e){return e===o?0<this.length?this[0].innerHTML:
null:this.each(function(a){var b=this.innerHTML;h(this).empty().append(m(this,e,a,b))})},text:function(e){return e===o?0<this.length?this[0].textContent:null:this.each(function(){this.textContent=e})},attr:function(e,a){var b;return"string"==typeof e&&a===o?0==this.length||1!==this[0].nodeType?o:"value"==e&&"INPUT"==this[0].nodeName?this.val():!(b=this[0].getAttribute(e))&&e in this[0]?this[0][e]:b:this.each(function(b){if(1===this.nodeType)if(d(e))for(g in e)this.setAttribute(g,e[g]);else this.setAttribute(e,
m(this,a,b,this.getAttribute(e)))})},removeAttr:function(e){return this.each(function(){1===this.nodeType&&this.removeAttribute(e)})},prop:function(e,a){return a===o?this[0]?this[0][e]:o:this.each(function(b){this[e]=m(this,a,b,this[e])})},data:function(a,b){var c=this.attr("data-"+f(a),b);return null!==c?c:o},val:function(a){return a===o?0<this.length?this[0].value:o:this.each(function(b){this.value=m(this,a,b,this.value)})},offset:function(){if(0==this.length)return null;var a=this[0].getBoundingClientRect();
return{left:a.left+window.pageXOffset,top:a.top+window.pageYOffset,width:a.width,height:a.height}},css:function(a,b){if(b===o&&"string"==typeof a)return 0==this.length?o:this[0].style[F(a)]||t(this[0],"").getPropertyValue(a);var c="";for(g in a)"string"==typeof a[g]&&""==a[g]?this.each(function(){this.style.removeProperty(f(g))}):c+=f(g)+":"+("number"==typeof a[g]&&!x[f(g)]?a[g]+"px":a[g])+";";"string"==typeof a&&(""==b?this.each(function(){this.style.removeProperty(f(a))}):c=f(a)+":"+("number"==
typeof b&&!x[f(a)]?b+"px":b));return this.each(function(){this.style.cssText+=";"+c})},index:function(a){return a?this.indexOf(h(a)[0]):this.parent().children().indexOf(this[0])},hasClass:function(a){return 1>this.length?!1:j(a).test(this[0].className)},addClass:function(a){return this.each(function(b){w=[];var c=this.className;m(this,a,b,c).split(/\s+/g).forEach(function(a){h(this).hasClass(a)||w.push(a)},this);w.length&&(this.className+=(c?" ":"")+w.join(" "))})},removeClass:function(a){return this.each(function(b){if(a===
o)return this.className="";w=this.className;m(this,a,b,w).split(/\s+/g).forEach(function(a){w=w.replace(j(a)," ")});this.className=w.trim()})},toggleClass:function(a,b){return this.each(function(c){c=m(this,a,c,this.className);(b===o?!h(this).hasClass(c):b)?h(this).addClass(c):h(this).removeClass(c)})}};["width","height"].forEach(function(a){h.fn[a]=function(b){var c,g=a.replace(/./,function(a){return a[0].toUpperCase()});return b===o?this[0]==window?window["inner"+g]:this[0]==y?y.documentElement["offset"+
g]:(c=this.offset())&&c[a]:this.each(function(c){var g=h(this);g.css(a,m(this,b,c,g[a]()))})}});["after","prepend","before","append"].forEach(function(a,b){h.fn[a]=function(){var a=h.map(arguments,function(a){return d(a)?a:v.fragment(a)});if(1>a.length)return this;var e=this.length,c=1<e,g=2>b;return this.each(function(w,f){for(var s=0;s<a.length;s++){var h=a[g?a.length-s-1:s];r(h,function(a){null!=a.nodeName&&("SCRIPT"===a.nodeName.toUpperCase()&&(!a.type||"text/javascript"===a.type))&&window.eval.call(window,
a.innerHTML)});c&&w<e-1&&(h=h.cloneNode(!0));l(b,f,h)}})};h.fn[b%2?a+"To":"insert"+(b?"Before":"After")]=function(b){h(b)[a](this);return this}});v.Z.prototype=h.fn;v.camelize=F;v.uniq=D;h._WXJS=v;return h}();window._WXJS=p;(function(c){function d(a){return a._zid||(a._zid=l++)}function a(a,c,g,f){c=b(c);if(c.ns)var h=RegExp("(?:^| )"+c.ns.replace(" "," .* ?")+"(?: |$)");return(m[d(a)]||[]).filter(function(a){return a&&(!c.e||a.e==c.e)&&(!c.ns||h.test(a.ns))&&(!g||d(a.fn)===d(g))&&(!f||a.sel==f)})}
function b(a){a=(""+a).split(".");return{e:a[0],ns:a.slice(1).sort().join(" ")}}function i(a,b,g){c.isObject(a)?c.each(a,g):a.split(/\s/).forEach(function(a){g(a,b)})}function n(a,g,f,h,n,j){var j=!!j,l=d(a),o=m[l]||(m[l]=[]);i(g,f,function(g,f){var d=n&&n(f,g),s=d||f,i=function(b){var c=s.apply(a,[b].concat(b.data));!1===c&&b.preventDefault();return c},d=c.extend(b(g),{fn:f,proxy:i,sel:h,del:d,i:o.length});o.push(d);a.addEventListener(d.e,i,j)})}function f(b,c,g,f){var h=d(b);i(c||"",g,function(c,
g){a(b,c,g,f).forEach(function(a){delete m[h][a.i];b.removeEventListener(a.e,a.proxy,!1)})})}function j(a){var b=c.extend({originalEvent:a},a);c.each(h,function(c,f){b[c]=function(){this[f]=o;return a[c].apply(a,arguments)};b[f]=g});return b}function k(a){if(!("defaultPrevented"in a)){a.defaultPrevented=!1;var b=a.preventDefault;a.preventDefault=function(){this.defaultPrevented=!0;b.call(this)}}}var m={},l=1,r={};r.click=r.mousedown=r.mouseup=r.mousemove="MouseEvents";c.event={add:n,remove:f};c.proxy=
function(a,b){if(c.isFunction(a)){var g=function(){return a.apply(b,arguments)};g._zid=d(a);return g}if("string"==typeof b)return c.proxy(a[b],a);throw new TypeError("expected function");};c.fn.bind=function(a,b){return this.each(function(){n(this,a,b)})};c.fn.unbind=function(a,b){return this.each(function(){f(this,a,b)})};c.fn.one=function(a,b){return this.each(function(c,g){n(this,a,b,null,function(a,b){return function(){var c=a.apply(g,arguments);f(g,b,a);return c}})})};var o=function(){return!0},
g=function(){return!1},h={preventDefault:"isDefaultPrevented",stopImmediatePropagation:"isImmediatePropagationStopped",stopPropagation:"isPropagationStopped"};c.fn.delegate=function(a,b,g){var f=!1;if("blur"==b||"focus"==b)c.iswebkit?b="blur"==b?"focusout":"focus"==b?"focusin":b:f=!0;return this.each(function(h,d){n(d,b,g,a,function(b){return function(g){var f,h=c(g.target).closest(a,d).get(0);if(h)return f=c.extend(j(g),{currentTarget:h,liveFired:d}),b.apply(h,[f].concat([].slice.call(arguments,
1)))}},f)})};c.fn.undelegate=function(a,b,c){return this.each(function(){f(this,b,c,a)})};c.fn.live=function(a,b){c(document.body).delegate(this.selector,a,b);return this};c.fn.die=function(a,b){c(document.body).undelegate(this.selector,a,b);return this};c.fn.on=function(a,b,g){return void 0==b||c.isFunction(b)?this.bind(a,b):this.delegate(b,a,g)};c.fn.off=function(a,b,g){return void 0==b||c.isFunction(b)?this.unbind(a,b):this.undelegate(b,a,g)};c.fn.trigger=function(a,b){"string"==typeof a&&(a=c.Event(a));
k(a);a.data=b;return this.each(function(){"dispatchEvent"in this&&this.dispatchEvent(a)})};c.fn.triggerHandler=function(b,g){var f,h;this.each(function(d,i){f=j("string"==typeof b?c.Event(b):b);f.data=g;f.target=i;c.each(a(i,b.type||b),function(a,b){h=b.proxy(f);if(f.isImmediatePropagationStopped())return!1})});return h};"focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout change select keydown keypress keyup error".split(" ").forEach(function(a){c.fn[a]=
function(b){return this.bind(a,b)}});["focus","blur"].forEach(function(a){c.fn[a]=function(b){if(b)this.bind(a,b);else if(this.length)try{this.get(0)[a]()}catch(c){}return this}});c.Event=function(a,b){var c=document.createEvent(r[a]||"Events"),g=!0;if(b)for(var f in b)"bubbles"==f?g=!!b[f]:c[f]=b[f];c.initEvent(a,g,!0,null,null,null,null,null,null,null,null,null,null,null,null);return c}})(p);(function(c){function d(a){var b=this.os={},c=this.browser={},d=a.match(/WebKit\/([\d.]+)/),f=a.match(/(Android)\s+([\d.]+)/),
j=a.match(/(iPad).*OS\s([\d_]+)/),k=!j&&a.match(/(iPhone\sOS)\s([\d_]+)/),m=a.match(/(webOS|hpwOS)[\s\/]([\d.]+)/),l=m&&a.match(/TouchPad/),r=a.match(/Kindle\/([\d.]+)/),o=a.match(/Silk\/([\d._]+)/),g=a.match(/(BlackBerry).*Version\/([\d.]+)/);if(c.webkit=!!d)c.version=d[1];f&&(b.android=!0,b.version=f[2]);k&&(b.ios=b.iphone=!0,b.version=k[2].replace(/_/g,"."));j&&(b.ios=b.ipad=!0,b.version=j[2].replace(/_/g,"."));m&&(b.webos=!0,b.version=m[2]);l&&(b.touchpad=!0);g&&(b.blackberry=!0,b.version=g[2]);
r&&(b.kindle=!0,b.version=r[1]);o&&(c.silk=!0,c.version=o[1]);!o&&(b.android&&a.match(/Kindle Fire/))&&(c.silk=!0)}d.call(c,navigator.userAgent);c.__detect=d})(p);(function(c){var d=function(){function a(){if(!c)return null;var a=c.deref();return a&&document.contains(a)?a:null}function b(a){var b=a.target;if("AUDIO"===b.tagName||"VIDEO"===b.tagName)switch(a.type){case "play":c=new WeakRef(b),d=!0}}var c=null,d=!1;document.addEventListener("play",b,!0);document.addEventListener("ended",b,!0);document.addEventListener("pause",
b,!0);return{recoverLastPlayback:function(){var b=a();if(!b)return Promise.resolve();try{if(b.paused)return b.play()}catch(c){return Promise.reject(c)}return Promise.resolve()},isLastPlaybackEnded:function(){var b=a();if(!b&&d)return!0;try{return!(!b||!(0<b.readyState&&(b.ended||b.currentTime>=b.duration)))}catch(c){return!1}},isPlaying:function(){var b=a();if(!b)return!1;try{return!(!b||b.paused||b.ended||!(b.currentTime<b.duration))}catch(c){return!1}}}}();c.mediaTracker=d})(p);var P=function(){function c(a){return Object.prototype.toString.call(a).slice(8,
-1).toLowerCase()}var d=function(){d.cache.hasOwnProperty(arguments[0])||(d.cache[arguments[0]]=d.parse(arguments[0]));return d.format.call(null,d.cache[arguments[0]],arguments)};d.format=function(a,b){var d=1,n=a.length,f="",j=[],k,m,l,r;for(k=0;k<n;k++)if(f=c(a[k]),"string"===f)j.push(a[k]);else if("array"===f){l=a[k];if(l[2]){f=b[d];for(m=0;m<l[2].length;m++){if(!f.hasOwnProperty(l[2][m]))throw P('[sprintf] property "%s" does not exist',l[2][m]);f=f[l[2][m]]}}else f=l[1]?b[l[1]]:b[d++];if(/[^s]/.test(l[8])&&
"number"!=c(f))throw P("[sprintf] expecting number but found %s",c(f));switch(l[8]){case "b":f=f.toString(2);break;case "c":f=String.fromCharCode(f);break;case "d":f=parseInt(f,10);break;case "e":f=l[7]?f.toExponential(l[7]):f.toExponential();break;case "f":f=l[7]?parseFloat(f).toFixed(l[7]):parseFloat(f);break;case "o":f=f.toString(8);break;case "s":f=(f=""+f)&&l[7]?f.substring(0,l[7]):f;break;case "u":f=Math.abs(f);break;case "x":f=f.toString(16);break;case "X":f=f.toString(16).toUpperCase()}f=
/[def]/.test(l[8])&&l[3]&&0<=f?"+"+f:f;m=l[4]?"0"==l[4]?"0":l[4].charAt(1):" ";r=l[6]-(""+f).length;if(l[6]){for(var o=[];0<r;o[--r]=m);m=o.join("")}else m="";j.push(l[5]?f+m:m+f)}return j.join("")};d.cache={};d.parse=function(a){for(var b=[],c=[],d=0;a;){if(null!==(b=/^[^\x25]+/.exec(a)))c.push(b[0]);else if(null!==(b=/^\x25{2}/.exec(a)))c.push("%");else if(null!==(b=/^\x25(?:([1-9]\d*)\$|\(([^\)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-fosuxX])/.exec(a))){if(b[2]){var d=d|1,f=[],j=b[2],k=
[];if(null!==(k=/^([a-z_][a-z_\d]*)/i.exec(j)))for(f.push(k[1]);""!==(j=j.substring(k[0].length));)if(null!==(k=/^\.([a-z_][a-z_\d]*)/i.exec(j)))f.push(k[1]);else if(null!==(k=/^\[(\d+)\]/.exec(j)))f.push(k[1]);else throw"[sprintf] huh?";else throw"[sprintf] huh?";b[2]=f}else d|=2;if(3===d)throw"[sprintf] mixing positional and named placeholders is not (yet) supported";c.push(b)}else throw"[sprintf] huh?";a=a.substring(b[0].length)}return c};return d}(),t;t||(t={});(function(){function c(a){return 10>
a?"0"+a:a}function d(a){i.lastIndex=0;return i.test(a)?'"'+a.replace(i,function(a){var b=j[a];return"string"===typeof b?b:"\\u"+("0000"+a.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+a+'"'}function a(b,c){var i,j,g,h,w=n,s,u=c[b];"function"===typeof k&&(u=k.call(c,b,u));switch(typeof u){case "string":return d(u);case "number":return isFinite(u)?""+u:"null";case "boolean":case "null":return""+u;case "object":if(!u)return"null";n+=f;s=[];if("[object Array]"===Object.prototype.toString.apply(u)){h=
u.length;for(i=0;i<h;i+=1)s[i]=a(i,u)||"null";g=0===s.length?"[]":n?"[\n"+n+s.join(",\n"+n)+"\n"+w+"]":"["+s.join(",")+"]";n=w;return g}if(k&&"object"===typeof k){h=k.length;for(i=0;i<h;i+=1)"string"===typeof k[i]&&(j=k[i],(g=a(j,u))&&s.push(d(j)+(n?": ":":")+g))}else for(j in u)Object.prototype.hasOwnProperty.call(u,j)&&(g=a(j,u))&&s.push(d(j)+(n?": ":":")+g);g=0===s.length?"{}":n?"{\n"+n+s.join(",\n"+n)+"\n"+w+"}":"{"+s.join(",")+"}";n=w;return g}}"function"!==typeof Date.prototype.toJSON&&(Date.prototype.toJSON=
function(){return isFinite(this.valueOf())?this.getUTCFullYear()+"-"+c(this.getUTCMonth()+1)+"-"+c(this.getUTCDate())+"T"+c(this.getUTCHours())+":"+c(this.getUTCMinutes())+":"+c(this.getUTCSeconds())+"Z":null},String.prototype.toJSON=Number.prototype.toJSON=Boolean.prototype.toJSON=function(){return this.valueOf()});var b=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,i=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,
n,f,j={"\u0008":"\\b","\t":"\\t","\n":"\\n","\u000c":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},k;"function"!==typeof t.stringify&&(t.stringify=function(b,c,d){var i;f=n="";if(typeof d==="number")for(i=0;i<d;i=i+1)f=f+" ";else typeof d==="string"&&(f=d);if((k=c)&&typeof c!=="function"&&(typeof c!=="object"||typeof c.length!=="number"))throw Error("JSON.stringify");return a("",{"":b})});"function"!==typeof t.parse&&(t.parse=function(a,c){function d(a,b){var f,i,j=a[b];if(j&&typeof j==="object")for(f in j)if(Object.prototype.hasOwnProperty.call(j,
f)){i=d(j,f);i!==void 0?j[f]=i:delete j[f]}return c.call(a,b,j)}var f,a=""+a;b.lastIndex=0;b.test(a)&&(a=a.replace(b,function(a){return"\\u"+("0000"+a.charCodeAt(0).toString(16)).slice(-4)}));if(/^[\],:{}\s]*$/.test(a.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,""))){f=eval("("+a+")");return typeof c==="function"?d({"":f},""):f}throw new SyntaxError("JSON.parse");})})();var K=K||function(c,
d){var a={},b=a.lib={},i=function(){},n=b.Base={extend:function(a){i.prototype=this;var b=new i;a&&b.mixIn(a);b.hasOwnProperty("init")||(b.init=function(){b.$super.init.apply(this,arguments)});b.init.prototype=b;b.$super=this;return b},create:function(){var a=this.extend();a.init.apply(a,arguments);return a},init:function(){},mixIn:function(a){for(var b in a)a.hasOwnProperty(b)&&(this[b]=a[b]);a.hasOwnProperty("toString")&&(this.toString=a.toString)},clone:function(){return this.init.prototype.extend(this)}},
f=b.WordArray=n.extend({init:function(a,b){a=this.words=a||[];this.sigBytes=b!=d?b:4*a.length},toString:function(a){return(a||k).stringify(this)},concat:function(a){var b=this.words,c=a.words,d=this.sigBytes,a=a.sigBytes;this.clamp();if(d%4)for(var f=0;f<a;f++)b[d+f>>>2]|=(c[f>>>2]>>>24-8*(f%4)&255)<<24-8*((d+f)%4);else if(65535<c.length)for(f=0;f<a;f+=4)b[d+f>>>2]=c[f>>>2];else b.push.apply(b,c);this.sigBytes+=a;return this},clamp:function(){var a=this.words,b=this.sigBytes;a[b>>>2]&=4294967295<<
32-8*(b%4);a.length=c.ceil(b/4)},clone:function(){var a=n.clone.call(this);a.words=this.words.slice(0);return a},random:function(a){for(var b=[],d=0;d<a;d+=4)b.push(4294967296*c.random()|0);return new f.init(b,a)}}),j=a.enc={},k=j.Hex={stringify:function(a){for(var b=a.words,a=a.sigBytes,c=[],d=0;d<a;d++){var f=b[d>>>2]>>>24-8*(d%4)&255;c.push((f>>>4).toString(16));c.push((f&15).toString(16))}return c.join("")},parse:function(a){for(var b=a.length,c=[],d=0;d<b;d+=2)c[d>>>3]|=parseInt(a.substr(d,2),
16)<<24-4*(d%8);return new f.init(c,b/2)}},m=j.Latin1={stringify:function(a){for(var b=a.words,a=a.sigBytes,c=[],d=0;d<a;d++)c.push(String.fromCharCode(b[d>>>2]>>>24-8*(d%4)&255));return c.join("")},parse:function(a){for(var b=a.length,c=[],d=0;d<b;d++)c[d>>>2]|=(a.charCodeAt(d)&255)<<24-8*(d%4);return new f.init(c,b)}},l=j.Utf8={stringify:function(a){try{return decodeURIComponent(escape(m.stringify(a)))}catch(b){throw Error("Malformed UTF-8 data");}},parse:function(a){return m.parse(unescape(encodeURIComponent(a)))}},
r=b.BufferedBlockAlgorithm=n.extend({reset:function(){this._data=new f.init;this._nDataBytes=0},_append:function(a){"string"==typeof a&&(a=l.parse(a));this._data.concat(a);this._nDataBytes+=a.sigBytes},_process:function(a){var b=this._data,d=b.words,i=b.sigBytes,j=this.blockSize,n=i/(4*j),n=a?c.ceil(n):c.max((n|0)-this._minBufferSize,0),a=n*j,i=c.min(4*a,i);if(a){for(var k=0;k<a;k+=j)this._doProcessBlock(d,k);k=d.splice(0,a);b.sigBytes-=i}return new f.init(k,i)},clone:function(){var a=n.clone.call(this);
a._data=this._data.clone();return a},_minBufferSize:0});b.Hasher=r.extend({cfg:n.extend(),init:function(a){this.cfg=this.cfg.extend(a);this.reset()},reset:function(){r.reset.call(this);this._doReset()},update:function(a){this._append(a);this._process();return this},finalize:function(a){a&&this._append(a);return this._doFinalize()},blockSize:16,_createHelper:function(a){return function(b,c){return(new a.init(c)).finalize(b)}},_createHmacHelper:function(a){return function(b,c){return(new o.HMAC.init(a,
c)).finalize(b)}}});var o=a.algo={};return a}(Math);(function(){var c=K,d=c.lib,a=d.WordArray,b=d.Hasher,i=[],d=c.algo.SHA1=b.extend({_doReset:function(){this._hash=new a.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(a,b){for(var c=this._hash.words,d=c[0],m=c[1],l=c[2],r=c[3],o=c[4],g=0;80>g;g++){if(16>g)i[g]=a[b+g]|0;else{var h=i[g-3]^i[g-8]^i[g-14]^i[g-16];i[g]=h<<1|h>>>31}h=(d<<5|d>>>27)+o+i[g];h=20>g?h+((m&l|~m&r)+1518500249):40>g?h+((m^l^r)+1859775393):
60>g?h+((m&l|m&r|l&r)-1894007588):h+((m^l^r)-899497514);o=r;r=l;l=m<<30|m>>>2;m=d;d=h}c[0]=c[0]+d|0;c[1]=c[1]+m|0;c[2]=c[2]+l|0;c[3]=c[3]+r|0;c[4]=c[4]+o|0},_doFinalize:function(){var a=this._data,b=a.words,c=8*this._nDataBytes,d=8*a.sigBytes;b[d>>>5]|=128<<24-d%32;b[(d+64>>>9<<4)+14]=Math.floor(c/4294967296);b[(d+64>>>9<<4)+15]=c;a.sigBytes=4*b.length;this._process();return this._hash},clone:function(){var a=b.clone.call(this);a._hash=this._hash.clone();return a}});c.SHA1=b._createHelper(d);c.HmacSHA1=
b._createHmacHelper(d)})();var D=[],ha=1E3,L={},F={},z={},T="__msg_type",G="__callback_id",H="__event_id",aa="weixin://dispatch_message/",V=[],E={},U="__runOn3rd_apis",J="xx_yy",ea="__json_message",ca="__msg_queue",X="__context_key",W="",O="isUseMd5_check",R="__sha_key",da=S,ba=N,fa=x,ia=Y,ga=q,ma=window.alert;window.alert=function(c){if(!("yes"===document.__wxjsjs__isWebviewWillClosed||"yes"===document.__wxjsjs__isDisableAlertView))return ma(c)};var na=window.prompt;window.prompt=function(c,d){if(!("yes"===
document.__wxjsjs__isWebviewWillClosed||"yes"===document.__wxjsjs__isDisableAlertView))return na(c,d)};var B={invoke:q,call:q,on:Y,log:x,_getSelectedText:Z,_fetchQueue:N};try{Object.defineProperty(B,"_handleMessageFromWeixin",{value:S,writable:!1,configurable:!1})}catch(qa){return}if(window.WeixinJSBridge)p.extend(window.WeixinJSBridge,B);else try{Object.defineProperty(window,"WeixinJSBridge",{value:B,writable:!1,configurable:!1})}catch(ra){return}(function(){function c(a,b){for(var c=document.elementFromPoint(a,
b),d=c;d&&"IMG"!=d.tagName;)d=d.parentNode;if(!d)var f=function(a,b){if(!a)return null;for(var c in a.childNodes){if(b(c))return c;var d=f(c,b);if(d)return d}return null},d=f(c,function(a){return a&&"IMG"==a.tagName});return d&&"IMG"==d.tagName?d:null}A("menu:setfont",function(a){if("function"===typeof changefont){var b=parseInt(a.fontSize);changefont(b)}else if(!a.isFirstAutoSet||!("2"===a.fontSize||"100"===a.fontScale)){b=parseInt(a.fontScale);if(50<=b&&500>=b)a=a.fontScale+"%";else switch(a.fontSize){case "1":a=
"80%";break;case "2":a="100%";break;case "3":a="120%";break;case "4":a="140%";break;default:return}document.getElementsByTagName("body")[0].style.webkitTextSizeAdjust=a;if(window.__wxjs_ipadfontsolution&&p.os.ipad&&13<=parseFloat(p.os.version,"10")){for(var b=document.getElementsByTagName("body")[0],c=parseFloat(a,"10")/100,a=[],b=document.createTreeWalker(b,4);b.nextNode();){var d=b.currentNode.parentNode,f=d.getAttribute("mp-original-font-size");f||(f=getComputedStyle(d).fontSize,d.setAttribute("mp-original-font-size",
f));a.push([d,f])}a.forEach(function(a){a[0].style.fontSize=parseFloat(a[1])*c+"px"})}}});var d=function(a){var b=!1,c=function(c){b||(b=!0,a(c))},d=p("img");if(0==d.length)return c();for(var f={},j=[],k=0;k<d.length;k++){var m=d[k];if(!("none"==p(m).css("display")||"hidden"==p(m).css("visibility"))&&!f[m.src])f[m.src]=1,j.push(m)}for(var l=[],k=0;k<j.length&&100>k;k++)d=j[k],f=new Image,f.onload=function(){this.isLoaded=!0;for(var a=0,b=0;b<l.length;b++){var d=l[b];if(!d.isLoaded)break;a++;if(290<
d.width&&290<d.height){c(d);break}}a==l.length&&c()},f.src=d.src,l.push(f);setTimeout(function(){for(var a=0;a<l.length;a++){var b=l[a];if(b.isLoaded&&290<b.width&&290<b.height){c(b);return}}c()},1E3)};A("menu:share:timeline",function(a){x("share timeline");var b;"string"===typeof a.title?(b=a,q("shareTimeline",b)):(b={link:document.documentURI||z.init_url,desc:document.documentURI||z.init_url,title:document.title},d(function(a){a&&(b.img_url=a.src,b.img_width=a.width,b.img_height=a.height);q("shareTimeline",
b)}))});A("menu:share:qq",function(a){x("share QQ");var b;"string"===typeof a.title?(b=a,q("shareQQ",b)):(b={link:document.documentURI||z.init_url,desc:document.documentURI||z.init_url,title:document.title},d(function(a){a&&(b.img_url=a.src,b.img_width=a.width,b.img_height=a.height);q("shareQQ",b)}))});A("menu:share:weiboApp",function(a){x("share Weibo App");var b;"string"===typeof a.title?(b=a,q("shareWeiboApp",b)):(b={link:document.documentURI||z.init_url,desc:document.documentURI||z.init_url,title:document.title},
d(function(a){a&&(b.img_url=a.src,b.img_width=a.width,b.img_height=a.height);q("shareWeiboApp",b)}))});A("menu:share:QZone",function(a){x("share QZone");var b;"string"===typeof a.title?(b=a,q("shareQZone",b)):(b={link:document.documentURI||z.init_url,desc:document.documentURI||z.init_url,title:document.title},d(function(a){a&&(b.img_url=a.src,b.img_width=a.width,b.img_height=a.height);q("shareQZone",b)}))});A("general:share",function(a){x("general share");"friend"===a.shareTo||"favorite"===a.shareTo||
"connector"===a.shareTo||"wework"===a.shareTo||"weworklocal"===a.shareTo?C("menu:share:appmessage",a):"timeline"===a.shareTo?C("menu:share:timeline",a):"weiboApp"==a.shareTo?C("menu:share:weiboApp",a):"QQ"===a.shareTo?"function"===typeof E["menu:share:qq"]&&M("menu:share:qq")?C("menu:share:qq",a):C("menu:share:appmessage",a):"QZone"===a.shareTo?"function"===typeof E["menu:share:QZone"]&&M("menu:share:QZone")?C("menu:share:QZone",a):C("menu:share:appmessage",a):"weread"===a.shareTo&&C("menu:share:appmessage",
a)});A("menu:share:appmessage",function(a){x("share appmessage");var b;"string"===typeof a.title?(b=a,q("sendAppMessage",b)):(b={link:document.documentURI||z.init_url,desc:document.documentURI||z.init_url,title:document.title},d(function(a){a&&(b.img_url=a.src,b.img_width=a.width,b.img_height=a.height);q("sendAppMessage",b)}))});A("menu:share:email",function(a){x("share email");q("sendEmail","string"===typeof a.title?a:{content:document.documentURI||z.init_url,title:document.title})});A("menu:share:facebook",
function(a){x("share facebook");var b;"string"===typeof a.title?(b=a,q("shareFB",b)):(b={link:document.documentURI||z.init_url,desc:document.documentURI||z.init_url,title:document.title},d(function(a){a&&(b.img_url=a.src,b.img_width=a.width,b.img_height=a.height);q("shareFB",b)}))});A("ui:longpress",function(a){x("longpress at ("+a.x+","+a.y+","+a.webViewWidth+","+a.webViewHeight+")");var b=document.body.clientWidth/a.webViewWidth,d=c(a.x*b,a.y*b);d?ka(d,function(a,c,d){var c=c.src,i=d.width/b,m=
d.height/b,l=d.getBoundingClientRect().top/b,d=d.getBoundingClientRect().left/b,p={};try{"function"===typeof window.getWXLongPressImageEventConfig&&(p=window.getWXLongPressImageEventConfig())}catch(o){}q("saveImage",{base64DataString:a,url:c,elementWidth:i,elementHeight:m,elementTop:l,elementLeft:d,eventConfigDict:p})}):x("cannot find image at ("+a.x+","+a.y+","+b+")")});A("sys:init",function(a){z=a;a=document.createEvent("Events");a.initEvent("WeixinJSBridgeReady");document.dispatchEvent(a)});A("sys:bridged",
function(){try{la()}catch(a){x("error %s",a)}});A("sys:record",function(){x("sys:record");d(function(a){var b=1500;document.title&&(b=1);setTimeout(function(){data={title:document.title,source:window.location.hostname};a&&(data.img_url=a.src,data.img_width=a.width,data.img_height=a.height);q("recordHistory",data)},b)})})})();p.JSON=t;p.disableImageSelection=function(){for(var c=p("img"),d=0;d<c.length;d++)p(c[d])._wxjs_old_touch_callout=p(c[d]).css("-webkit-touch-callout"),p(c[d])._wxjs_old_user_select=
p(c[d]).css("-webkit-user-select");p("img").css({"-webkit-touch-callout":"none","-webkit-user-select":"none"})};p.restoreImageSelection=function(){for(var c=p("img"),d=0;d<c.length;d++)"undefined"!=typeof p(c[d])._wxjs_old_touch_callout&&p(c[d]).css({"-webkit-touch-callout":p(c[d])._wxjs_old_touch_callout,"-webkit-user-select":p(c[d])._wxjs_old_user_select})};p.disableAlertView=function(){window.__wxjs_sys_alert=window.alert;window.alert=null;window.__wxjs_sys_prompt=window.prompt;window.prompt=null;
window.__wxjs_sys_confirm=window.confirm;window.confirm=null};p.restoreAlertView=function(){window.alert=window.__wxjs_sys_alert;window.prompt=window.__wxjs_sys_prompt;window.confirm=window.__wxjs_sys_confirm;delete window.__wxjs_sys_alert;delete window.__wxjs_sys_prompt;delete window.__wxjs_sys_confirm};if(__ISWKWEBVIEW){window.__wxjs_is_wkwebview=!0;p("document").ready(function(){var c=document.oncopy;document.oncopy=function(){var d=Z();I(t.stringify({__onCopy:d}));if("undefined"!==typeof c&&null!==
c)return c()};I(t.stringify({__domReadyNotify:J}))});if(history.pushState){var oa=history.pushState;history.pushState=function(c,d,a){var b=$(a);I(t.stringify({__pageStateChange:b}));oa.apply(this,arguments)}}if(history.replaceState){var pa=history.replaceState;history.replaceState=function(c,d,a){var b=$(a);I(t.stringify({__pageStateChange:b}));pa.apply(this,arguments)}}}else window.__wxjs_is_wkwebview=!1})();window.__wxjs_is_injected_success="yes";
