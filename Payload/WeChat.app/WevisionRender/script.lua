stickers = {}
SELF_ID = 0
invalidNum = -10.0
scaleIn = -1
posXIn = 0.0
posYIn = 0.0
rotateIn = 0
MaxRotateAngle = math.pi * 2.0
animationMinimumSize = 0
renderWidth = 1080
renderHeight = 1920
texWidth = 720
texHeight = 720
baseWidth = 576.0
baseHeight = 1024.0
baseAS = 1.0
scaleCGI = 1.0
priorIn = 1000

elementName = "frame"


local StickerCreator = {id = 0}
StickerCreator.__index = StickerCreator

function StickerCreator:new(path, type)
    local self = {}
    setmetatable(self, StickerCreator)
    local ret = EffectContext.sdk:AddEffectMaterial({path = path, type = type})
    if (ret == nil or ret.id == nil) then
        self.valid = false
        print("Failed addding sticker effect material.", path, type)
        return self
    end
    self.valid = true
    self.id = ret.id
    self.config = ret.config
    self.elements = ret.config.params.elements
    self.param = ret.config.params[elementName]
    local position = self.param.pos
    baseAS = self.param.aspect
    texWidth = baseWidth*(position[3] - position[1])
    texHeight = baseHeight*(position[4] - position[2])
    print("tex w h   ", texWidth, texHeight)
    priorIn = ret.config.params.prior
    print("prior   ", priorIn)
    renderWidth = EffectContext.RenderWidth
    renderHeight = EffectContext.RenderHeight
    print("render w h   ", renderWidth, renderHeight)
    SELF_ID = EffectContext.getEnvVar("SELF_ID")

    return self
end

function StickerCreator:setpauseSelfUpdate(pause, switch)
    if (self.valid == false) then
        return
    end
    EffectContext.sdk:SetParam({id = self.id, param = {enable = switch}, pauseSelfUpdate = pause})
end

function setpauseSelfUpdate(stickers, pause, switch) stickers:setpauseSelfUpdate(pause, switch) end

function StickerCreator:destroy()
    if (self.valid == false) then
        return
    end
    EffectContext.sdk:RemoveObject(self.id)
end

function StickerCreator:UpdateSwitch(sw)
    if (self.valid == false) then
        return
    end
    EffectContext.sdk:SetParam({
        id = self.id,
        param = {
            enable = sw
        }
    })
end

function StickerCreator:UpdateRST(r,s,mx, my,prior)
    if (self.valid == false) then
        return
    end
    EffectContext.sdk:SetParam({
        id = self.id,
        param = { 
            rotate = {0.0,0.0,r},
            scale = { s, s, s },
            trans = { mx, my, 0.0},
            prior = prior
        }
    })
end

function updateParams(part)
    if (part.valid == false) then
        return
    end
    rotateV = rotateIn - (math.floor((rotateIn + math.pi) / (math.pi * 2)) * math.pi * 2)
    rotateV = math.min(rotateV, MaxRotateAngle)
    rotateV = math.max(rotateV, -MaxRotateAngle)
    local minScale = animationMinimumSize * renderWidth / texWidth
    scaleV = math.max(scaleIn*scaleCGI, minScale)
    posXV = posXIn * renderWidth
    posYV = posYIn * renderHeight
    part:UpdateRST(rotateV, scaleV, posXV, posYV, priorIn)
end

function onSelfUpdate(part)
    if (part.valid == false) then
        return
    end
    EffectContext.sdk:SendMessage({
        id = SELF_ID,
        message = {
            id = part.id
        }
    })


end


Funcs = {
    
    onInit = function()
        print("dabixin second lua init...")
        stickers["dabixin"] = StickerCreator:new("./Render")
        setpauseSelfUpdate(stickers["dabixin"], 1, 1)
        stickers["dabixin"]:UpdateSwitch(0)
    end,
    

    onMessage = function(mes)
        print("onMessage: get wv_render_params ----")
        if(mes["wv_render_params"]) then
            stickers["dabixin"]:UpdateSwitch(1)
            for k, v in pairs(mes["wv_render_params"]) do
                print(k, "->", v)
                if(k == "transX") then
                    posXIn = v
                elseif(k == "transY") then
                    posYIn = v
                elseif(k == "scale") then
                    scaleIn = v
                elseif(k == "rotate") then
                    rotateIn = v
                elseif(k == "maxRotationAngle") then
                    MaxRotateAngle = v
                elseif(k == "animationMinimumSize") then
                    animationMinimumSize = v
                elseif(k == "scaleCGI") then
                    scaleCGI = v
                elseif(k == "prior") then
                    priorIn = v
                end
            end
            updateParams(stickers["dabixin"])
        end
    end,


    onDraw = function()
        onSelfUpdate(stickers["dabixin"])
    end
}

return Funcs;
