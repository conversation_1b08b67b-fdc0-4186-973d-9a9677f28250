// 参考yywxjs实现
(function () {

    //防止重复注入
if (typeof window.document === 'object' && typeof window.document !== null && typeof window.document.__wx_web_prefetcher_js_isLoaded === 'string' && window.document.__wx_web_prefetcher_js_isLoaded == 'loaded') {
    return;
}

    var _sendMessageQueue = [], // page to native的消息队列
        _callback_count = 1000, // 第一个事件的id，后面递增。
        _callback_map = {}, // 回调表 'callbackID' => callback
        _CALLBACK_ID = '__callback_id',
        _MSG_QUEUE = '__msg_queue',
        _JSON_MESSAGE = '__json_message';

    var _handleMessageIdentifier = _handleMessageFromWeixin;
    var _fetchQueueIdentifier = _fetchQueue;
    var _callIdentifier = _call;


    function _sendMessage(message) {
        _sendMessageQueue.push(message);
        var fetchQueueMsg = _fetchQueue();
        window.webkit.messageHandlers.weixinWebPrefetcher.postMessage(fetchQueueMsg); //用提前注入的保护方法调用
    };


    function _fetchQueue() {
        var curFuncIdentifier = __WeixinPrefecherJSBridge._fetchQueue;
        if (curFuncIdentifier !== _fetchQueueIdentifier) {
            return '';
        }
        var messageQueueString = JSON.stringify(_sendMessageQueue);
        _sendMessageQueue = [];

        var retMap = {};
        retMap[_MSG_QUEUE] = messageQueueString;
        return JSON.stringify(retMap);
    };

    function _handleMessageFromWeixin(originmessage) {
        var curFuncIdentifier = __WeixinPrefecherJSBridge._handleMessageFromWeixin;

        if (curFuncIdentifier !== _handleMessageIdentifier) {
            return '{}';
        }

        var ret;
        var messageStr = originmessage[_JSON_MESSAGE];

        var message;
        try {
            message = JSON.parse(messageStr);
        } catch (e) {
            message = window.JSON.parse(messageStr);
        }

        if (typeof message[_CALLBACK_ID] === 'string' && typeof _callback_map[message[_CALLBACK_ID]] === 'function') {
            ret = _callback_map[message[_CALLBACK_ID]](message['__params']);
            delete _callback_map[message[_CALLBACK_ID]]; // can only call once
            return JSON.stringify(ret);
        }
        return JSON.stringify({
            '__err_code': 'cb404'
        });

    };



    function _call(func, params, callback) {
        var curFuncIdentifier = __WeixinPrefecherJSBridge.invoke;
        if (curFuncIdentifier !== _callIdentifier) {
            return;
        }
        if (!func || typeof func !== 'string') {
            return;
        }
        if (typeof params !== 'object') {
            params = {};
        }

        var callbackID = (_callback_count++).toString();

        if (typeof callback === 'function') {
            _callback_map[callbackID] = callback;
        }

        var msgObj = {
            'func': func,
            'params': params
        };
        msgObj[_CALLBACK_ID] = callbackID;

        _sendMessage(JSON.stringify(msgObj));
    };


    var __WeixinPrefecherJSBridge = {
        // public
        invoke: _call,
        // private
        _fetchQueue: _fetchQueue
    };

    try {
        Object.defineProperty(__WeixinPrefecherJSBridge, '_handleMessageFromWeixin', {
            value: _handleMessageFromWeixin,
            writable: false,
            configurable: false
        })
    } catch (e) {
        return;
    }

    if (!window.WeixinPrefecherJSBridge) {
        try {
            Object.defineProperty(window, 'WeixinPrefecherJSBridge', {
                value: __WeixinPrefecherJSBridge,
                writable: false,
                configurable: false
            })
        } catch (e) {
            return;
        }
    }
})();
