<!DOCTYPE html>
<!-- saved from url=(0061)http://3gimg.qq.com/Scan_Wi-Fi/wx_wifi_service_terms_chs.html -->
<html lang="zh-CN"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta charset="UTF-8">
    <title>服务声明</title>
    <meta name="viewport" content=" initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="format-detection" content="telephone=no">
    <!--<link rel="stylesheet" href="../publish/css/style-weixin-wifi-faq.css"/>-->
    <!--<link rel="stylesheet" href="http://3glogo.gtimg.com/o2ov-act/proj-weixin-wifi-h5/css/style-weixin-wifi-faq.css"/>-->
    <style>
        ul{
            list-style-type: none;
            margin: 0;
            padding: 0;
        }
        .ui-mt-medium{
            margin-top: 10px;
        }
        .mod-faq{
            min-height: 100%;
            color: #000;
            background-color: #ffffff;
            padding: 0 12px;
            font: 14px/1.5em "微软雅黑","Lucida Grande", "Lucida Sans Unicode", Helvetica, Arial, Verdana, sans-serif;
        }
        .mod-faq__header{
            padding: 41px 0 3px;
            font-weight: bold;
            font-size: 15px;
            border-bottom: 1px solid #b0bebe;
            margin: 0;
        }
        .mod-faq__content{
            padding: 26px 0;
        }
        .mod-faq__brief{
            margin-bottom: 26px;
        }
        .mod-faq__list-item{
            margin-bottom: 26px;
        }
        .mod-faq__authority{
            text-align: right;
            padding-bottom: 26px;
        }
        .mod-faq__quest-list{
            line-height: 18px;
        }
        .mod-faq__quest{
            font-weight: bold;
            margin-bottom: 6px;
        }
        .mod-faq__answer{
            position: relative;
            color: #5e5e5e;
            margin-left: 0;
            padding-left: 24px;
            margin-bottom: 26px;
        }
        .mod-faq__tail{
            position: absolute;
            left: 0;
            top: 0;
            font-style: normal;
        }

    </style>
<style id="style-1-cropbar-clipper">/* Copyright 2014 Evernote Corporation. All rights reserved. */
.en-markup-crop-options {
    top: 18px !important;
    left: 50% !important;
    margin-left: -100px !important;
    width: 200px !important;
    border: 2px rgba(255,255,255,.38) solid !important;
    border-radius: 4px !important;
}

.en-markup-crop-options div div:first-of-type {
    margin-left: 0px !important;
}
</style></head>
<body>
<div class="mod-faq">
    <h1 class="mod-faq__header">服务声明</h1>
    <div class="mod-faq__content">
        <div class="mod-faq__brief">
            “微信连Wi-Fi”是包括但不限于微信“扫一扫”等微信方式（具体以平台公布为准）即可连接互联网的方便联网方式，Wi-Fi服务由商家及第三方网络服务商提供。本声明为《腾讯微信软件许可及服务协议》不可分割的一部分，用户使用“微信连Wi-Fi”服务，需遵守上述协议及本声明如下条款：
        </div>
        <ul>
            <li class="mod-faq__list-item">
                1. 你理解并同意：微信及腾讯公司仅为用户提供包括但不限于微信“扫一扫”等微信方式即可连接互联网的方便联网方式，Wi-Fi服务均由商家及第三方网络服务商提供，微信及腾讯并非Wi-Fi服务提供商，也不对相关Wi-Fi服务的可用性及安全等承担责任。
            </li>
            <li class="mod-faq__list-item">
                2. “微信连Wi-Fi”功能使用微信帐号完成Wi-Fi登录鉴权，根据互联网相关管理法律法规和服务提供的需要，可能需要你进行信息登记，或者由腾讯公司对你的微信帐号绑定资料进行登记。未经你的允许，腾讯公司将不会向任何第三方泄漏你的个人信息，但国家有权机关依法需要腾讯公司配合提供的除外。用户使用“微信连Wi-Fi”功能需要登录微信帐号，为确保你的帐号安全，请你使用本人微信帐号登录。
            </li>
            <li class="mod-faq__list-item">
                3. “微信连Wi-Fi”功能仅在微信部分软件版本上提供，请你确认所使用的软件版本支持“微信连Wi-Fi”功能，否则无法使用。该服务在微信网页版、Mac版等其他版本中无法使用。
            </li>
            <li class="mod-faq__list-item">
                4. “微信连Wi-Fi”功能中Wi-Fi服务均由商家及第三方网络服务商提供，用户使用Wi-Fi服务时需要留意公共Wi-Fi环境上网安全，并关注商家及第三方网络服务商关于Wi-Fi安全使用的说明。腾讯提醒你不要随意打开无法确认安全性的链接、不要随意下载陌生应用、不要在不能确认的网站输入自己微信/QQ的帐号和密码、不要随意泄露自己的银行账号和密码等个人信息。如遇到Wi-Fi使用相关问题，你可以直接与商家及第三方网络服务商联系解决。
            </li>
            <li class="mod-faq__list-item">
                5. 你理解并同意：“微信连Wi-Fi”同大多数互联网服务一样，受限于包括网络质量、商家及第三方网络服务商政策或用户需求差异等因素的影响，腾讯公司尽最大努力为你提供良好的联网体验，但不能保证由商家及第三方网络服务商提供的互联网连接服务的带宽、允许接入时长、接通成功率及其他服务质量指标满足你的需求，因上述原因而导致你不能正常使用“微信连Wi-Fi”或者互联网连接服务的，腾讯公司不承担责任。
            </li>
            <li class="mod-faq__list-item">
                6. 若你有任何违反相关法律法规或本声明的规定，腾讯公司有权视你的行为性质，在不事先通知你的情况下，采取包括但不限于中止或终止服务、追究法律责任等措施，若因此造成腾讯公司或他人损失的，你应予赔偿。
            </li>
            <li class="mod-faq__list-item">
                7. 根据用户需求及产品更新的需要，腾讯公司可能会对“微信连Wi-Fi”功能及本免责声明条款进行修改。如果有上述修改时，我们会在产品和网页中显著的位置发布以便及时通知到用户。如果你选择继续使用我们的服务，即表示你同意接受。
            </li>
        </ul>
        <div class="mod-faq__authority">腾讯公司</div>
    </div>
</div>


</body></html>