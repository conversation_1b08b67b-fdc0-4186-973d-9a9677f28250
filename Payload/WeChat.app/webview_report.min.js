!function(e){var n={};function t(r){if(n[r])return n[r].exports;var i=n[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,t),i.l=!0,i.exports}t.m=e,t.c=n,t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:r})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,n){if(1&n&&(e=t(e)),8&n)return e;if(4&n&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(t.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var i in e)t.d(r,i,function(n){return e[n]}.bind(null,i));return r},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},t.p="",t(t.s=0)}([function(e,n){if(!document.referrer&&initialTime){var t={},r=!1;try{var i=window.navigator.userAgent.toLowerCase();if(!(i.indexOf("iphone")>=0||i.indexOf("ipad")>=0))return a(),u();var o=new MutationObserver((function(e){e.forEach((function(e){Array.prototype.forEach.call(e.addedNodes,(function(e){"BODY"===e.tagName&&(t.firstContentfulPaint=Date.now()-initialTime,o.disconnect())}))}))}));o.observe(document,{childList:!0,subtree:!0}),window.addEventListener("load",(function(){o.disconnect(),window.weixinPostMessageHandlers&&(a(),window.weixinPostMessageHandlers.monitorHandler.postMessage(JSON.stringify({event:"reportWebViewPerf",value:u()})))})),setTimeout((function(){o.disconnect()}),6e4)}catch(e){}}function a(){if(!r){if(r=!0,window.PerformanceTiming){var e=window.performance.timing;t.navigationStart=e.navigationStart-initialTime,t.domainLookupStart=e.domainLookupStart-initialTime,t.domainLookupEnd=e.domainLookupEnd-initialTime,t.domContentLoaded=e.domContentLoadedEventStart-initialTime,t.loadEvent=e.loadEventStart-initialTime}if(window.PerformanceEntry){var n=performance.getEntriesByType("resource").filter((function(e){return"link"===e.initiatorType||"script"===e.initiatorType})),i=n.reduce((function(e,n){return e+n.transferSize}),0);isNaN(i)||(t.resourceTransferSize=i),t.resourceDuration=n.reduce((function(e,n){return e+n.duration}),0)/n.length|0,t.resourceCacheHit=n.filter((function(e){return 0===e.transferSize&&e.duration<100||0===e.duration})).length/n.length*100}if(window.PerformancePaintTiming)performance.getEntriesByType("paint").forEach((function(e){"first-contentful-paint"===e.name&&(t.firstContentfulPaint=Math.floor(e.startTime+t.navigationStart))}))}}function u(){return[t.domainLookupStart,t.domainLookupEnd,t.navigationStart,t.firstContentfulPaint||0,t.domContentLoaded,t.loadEvent,t.resourceTransferSize||0,t.resourceDuration||0,t.resourceCacheHit||0].join(",")}}]);