(function(){function J(a){__ISWKWEBVIEW?(<PERSON>.push(a),a=P(),window.weixinPostMessageHandlers.weixinDispatchMessage.postMessage(a)):(<PERSON>.push(a),document.location=fa)}function P(){if(A._fetchQueue!==ga)return"";var a=t.stringify(K);K=[];var c=[];c[0]=a;c[1]=L;var b=c.join(""),c="";"yes"===Q&&(c=G.SHA1(b).toString());b={};b[ha]=a;b[U]=c;return t.stringify(b)}function V(a){if(A._handleMessageFromWeixin!==ia)return"{}";var c;c=a[ja];var a=a[U],b=[];b[0]=c;b[1]=L;var b=b.join(""),h="";if("yes"===Q&&(h=G.SHA1(b).toString(),
h!==a))return"{}";var d;try{d=t.parse(c)}catch(j){d="undefined"!==typeof window.weixinJSONParse?window.weixinJSONParse(c):window.JSON.parse(c)}switch(d[W]){case "callback":return"string"===typeof d[E]&&"function"===typeof M[d[E]]?(c=M[d[E]](d.__params),delete M[d[E]],t.stringify(c)):t.stringify({__err_code:"cb404"});case "event":"object"===typeof d[X]&&(Y=d[X]);Z=d[$];if("string"===typeof d[D]){b=N[d[D]];h=H[d[D]];c=I[d[D]];a=d.__params;d=O(d[D]);if("function"===typeof h&&d)return c=h(a),t.stringify(c);
if(Array.isArray(c)&&c.length&&d){d=[];for(b=0;b<c.length;b++)"function"===typeof c[b]&&(h=c[b](a),d.push(h));return t.stringify(d)}if("function"===typeof b)return c=b(a),t.stringify(c)}return t.stringify({__err_code:"ev404"})}return"{}"}function O(a){return Y.some(function(c){return c===a})}function w(a){if(A.log===ka){for(var c=[],b=0;b<arguments.length;b++)c.push(arguments[b]);var b=c.shift(),h;try{c.unshift(b),h=R.apply(null,c)}catch(d){h=a}o("log",{msg:h})}}function o(a,c,b){if(A.call===la&&
a&&"string"===typeof a){"object"!==typeof c&&(c={});var h=(ma++).toString();"function"===typeof b&&(M[h]=b);b=[];b[0]=Z;b[1]=L;var b=b.join(""),d="";"yes"===Q&&(d=G.SHA1(b).toString());c[$]=d;a={func:a,params:c};a[W]="call";a[E]=h;J(t.stringify(a))}}function y(a,c){a&&"string"===typeof a&&"function"===typeof c&&(N[a]=c)}function aa(a,c){A.on===na&&a&&"string"===typeof a&&"function"===typeof c&&(H[a]=c)}function ba(a,c){A.subscribe===oa&&a&&"string"===typeof a&&"function"===typeof c&&(I[a]?I[a].push(c):
I[a]=[c])}function pa(a,c){var b=I[a];b&&b.length&&(I[a]=b.filter(function(b){return b!==c}))}function C(a,c){if("function"===typeof H[a]&&O(a))H[a](c);else if("function"===typeof N[a])N[a](c)}function qa(){return S}function ra(a,c){var b=new Image,h=!1;b.onload=function(){h||(h=!0,c(b))};b.src=a;setTimeout(function(){h||(h=!0,c(b))},1E3)}function sa(a,c){ra(a.src,function(b){var h=/^data:image\/(png|jpg|jpeg|tiff|gif|bmp);base64,/i,d="";if(b.src.match(h))d=b.src;else if(629145.6>b.width*b.height){var j=
document.createElement("canvas");j.width=b.width;j.height=b.height;j.getContext("2d").drawImage(b,0,0);var e="jpg",p=b.src.match(/\.(png|jpg|jpeg|tiff|gif|bmp)$/i);p&&(e=p[1].toLowerCase());try{d=j.toDataURL("image/"+e)}catch(n){w(n.message)}}c(d.replace(h,""),b,a)})}function ca(){for(var a=r("audio"),c=0;c<a.length;c++)if(!a[c].paused&&!a[c].ended){o("audioStateChanged",{state:"play"});break}a.on("play",function(){o("audioStateChanged",{state:"play"})});a.on("ended",function(){o("audioStateChanged",
{state:"ended"})});a.on("pause",function(){o("audioStateChanged",{state:"pause"})});a=r("video");a.on("play",function(){o("videoStateChanged",{state:"play"})});a.on("ended",function(){o("videoStateChanged",{state:"ended"})});a.on("pause",function(){o("videoStateChanged",{state:"pause"})})}function ta(){for(var a=r("img"),c=0;c<a.length;c++)r(a[c])._wxjs_old_touch_callout=r(a[c]).css("-webkit-touch-callout"),r(a[c])._wxjs_old_user_select=r(a[c]).css("-webkit-user-select");r("img").css({"-webkit-touch-callout":"none",
"-webkit-user-select":"none"})}function ua(){for(var a=r("img"),c=0;c<a.length;c++)"undefined"!=typeof r(a[c])._wxjs_old_touch_callout&&r(a[c]).css({"-webkit-touch-callout":r(a[c])._wxjs_old_touch_callout,"-webkit-user-select":r(a[c])._wxjs_old_user_select})}function va(){window.__wxjs_sys_alert=window.alert;window.alert=null;window.__wxjs_sys_prompt=window.prompt;window.prompt=null;window.__wxjs_sys_confirm=window.confirm;window.confirm=null}function wa(){window.alert=window.__wxjs_sys_alert;window.prompt=
window.__wxjs_sys_prompt;window.confirm=window.__wxjs_sys_confirm;delete window.__wxjs_sys_alert;delete window.__wxjs_sys_prompt;delete window.__wxjs_sys_confirm}function xa(){function a(b,a){for(var c=document.elementFromPoint(b,a),j=c;j&&"IMG"!=j.tagName;)j=j.parentNode;if(!j)var e=function(b,a){if(!b)return null;for(var h in b.childNodes){if(a(h))return h;var c=e(h,a);if(c)return c}return null},j=e(c,function(b){return b&&"IMG"==b.tagName});return j&&"IMG"==j.tagName?j:null}y("menu:setfont",function(b){if("function"===
typeof changefont){var a=parseInt(b.fontSize);changefont(a)}else if(!b.isFirstAutoSet||!("2"===b.fontSize||"100"===b.fontScale)){a=parseInt(b.fontScale);if(50<=a&&500>=a)b=b.fontScale+"%";else switch(b.fontSize){case "1":b="80%";break;case "2":b="100%";break;case "3":b="120%";break;case "4":b="140%";break;default:return}if(window.__wxjs_ipadfontsolution&&r.os.ipad&&13<=parseFloat(r.os.version,"10")){for(var a=document.getElementsByTagName("body")[0],c=parseFloat(b,"10")/100,b=[],a=document.createTreeWalker(a,
4);a.nextNode();){var j=a.currentNode.parentNode,e=j.getAttribute("mp-original-font-size");e||(e=getComputedStyle(j).fontSize,j.setAttribute("mp-original-font-size",e));b.push([j,e])}b.forEach(function(b){b[0].style.fontSize=parseFloat(b[1])*c+"px"})}else document.getElementsByTagName("body")[0].style.webkitTextSizeAdjust=b}});var c=function(b){var a;a=(a=document.head.querySelector('meta[property~="og:image"],meta[name~="og:image"]'))&&a.getAttribute("content");b(a)};y("menu:share:timeline",function(b){w("share timeline");
var a;"string"===typeof b.title?(a=b,o("shareTimeline",a)):(a={link:document.documentURI||B.init_url,title:F()||document.title},c(function(b){b&&(a.img_url=b);o("shareTimeline",a)}))});y("menu:share:qq",function(b){w("share QQ");var a;"string"===typeof b.title?(a=b,o("shareQQ",a)):(a={link:document.documentURI||B.init_url,title:F()||document.title},c(function(b){b&&(a.img_url=b);o("shareQQ",a)}))});y("menu:share:weiboApp",function(b){w("share Weibo App");var a;"string"===typeof b.title?(a=b,o("shareWeiboApp",
a)):(a={link:document.documentURI||B.init_url,title:F()||document.title},c(function(b){b&&(a.img_url=b);o("shareWeiboApp",a)}))});y("menu:share:QZone",function(b){w("share QZone");var a;"string"===typeof b.title?(a=b,o("shareQZone",a)):(a={link:document.documentURI||B.init_url,title:F()||document.title},c(function(b){b&&(a.img_url=b);o("shareQZone",a)}))});y("general:share",function(b){w("general share");"friend"===b.shareTo||"favorite"===b.shareTo||"connector"===b.shareTo||"wework"===b.shareTo||
"weworklocal"===b.shareTo?C("menu:share:appmessage",b):"timeline"===b.shareTo?C("menu:share:timeline",b):"weiboApp"==b.shareTo?C("menu:share:weiboApp",b):"QQ"===b.shareTo?"function"===typeof H["menu:share:qq"]&&O("menu:share:qq")?C("menu:share:qq",b):C("menu:share:appmessage",b):"QZone"===b.shareTo?"function"===typeof H["menu:share:QZone"]&&O("menu:share:QZone")?C("menu:share:QZone",b):C("menu:share:appmessage",b):"weread"===b.shareTo&&C("menu:share:appmessage",b)});y("menu:share:appmessage",function(b){w("share appmessage");
var a;"string"===typeof b.title?(a=b,o("sendAppMessage",a)):(a={link:document.documentURI||B.init_url,title:F()||document.title},c(function(b){b&&(a.img_url=b);o("sendAppMessage",a)}))});y("menu:share:email",function(b){w("share email");b="string"===typeof b.title?b:{content:document.documentURI||B.init_url,title:F()||document.title};o("sendEmail",b)});y("menu:share:facebook",function(b){w("share facebook");var a;"string"===typeof b.title?(a=b,o("shareFB",a)):(a={link:document.documentURI||B.init_url,
title:F()||document.title},c(function(b){b&&(a.img_url=b);o("shareFB",a)}))});y("ui:longpress",function(b){w("longpress at ("+b.x+","+b.y+","+b.webViewWidth+","+b.webViewHeight+")");var c=document.body.clientWidth/b.webViewWidth,d=a(b.x*c,b.y*c);d?sa(d,function(b,a,d){var a=a.src,n=d.width/c,m=d.height/c,l=d.getBoundingClientRect().top/c,d=d.getBoundingClientRect().left/c,x={};try{"function"===typeof window.getWXLongPressImageEventConfig&&(x=window.getWXLongPressImageEventConfig())}catch(k){}o("saveImage",
{base64DataString:b,url:a,elementWidth:n,elementHeight:m,elementTop:l,elementLeft:d,eventConfigDict:x})}):w("cannot find image at ("+b.x+","+b.y+","+c+")")});y("sys:init",function(b){B=b;b=document.createEvent("Events");b.initEvent("WeixinJSBridgeReady");document.dispatchEvent(b)});y("sys:bridged",function(){try{ca()}catch(b){w("error %s",b)}});y("sys:auth",function(b){S=b.state;var a=document.createEvent("Events");a.initEvent("WeixinJSBridgeAuthChanged");a.state=S;a.fullUrl=b.fullUrl;document.dispatchEvent(a)});
y("sys:ready",function(a){if(!("object"===typeof document&&null!==typeof document&&"string"===typeof document.__wxjsjs__sysInit&&"yes"==document.__wxjsjs__sysInit)){B=a;a=document.createEvent("Events");a.initEvent("WeixinJSBridgeReady");document.dispatchEvent(a);try{ca()}catch(c){w("error %s",c)}document.__wxjsjs__sysInit="yes"}});y("sys:record",function(){w("sys:record");c(function(a){var c=1500;document.title&&(c=1);setTimeout(function(){data={title:document.title,source:window.location.hostname};
a&&(data.img_url=a);o("recordHistory",data)},c)})})}function da(){var a=window.getSelection().toString();if(a&&0<a.length)return a;for(var c=document.querySelectorAll("iframe"),b=0;b<c.length;++b)try{var h=c[b].contentWindow.getSelection().toString();if(0<h.length){a=h;break}}catch(d){}return a}function ea(a){var c=document.createElement("a");c.href=location.href;c.href=a;return c.href}function F(){var a=document.head.querySelector('meta[property~="og:title"],meta[name~="og:title"]');return a&&a.getAttribute("content")}
if(!("object"===typeof window.document&&null!==typeof window.document&&"string"===typeof window.document.__wxjsjs__isLoaded&&"loaded"==window.document.__wxjsjs__isLoaded)){"object"===typeof window.document&&(null!==typeof window.document&&"string"===typeof window.document.__wxjsjs__sysInit&&"yes"==window.document.__wxjsjs__sysInit)&&(document.__wxjsjs__sysInit="no");var r=function(){function a(i){return G.call(i)=="[object Function]"}function c(i){return i instanceof Object}function b(i){var b,c;
if(G.call(i)!=="[object Object]")return false;c=a(i.constructor)&&i.constructor.prototype;if(!c||!hasOwnProperty.call(c,"isPrototypeOf"))return false;for(b in i);return b===k||hasOwnProperty.call(i,b)}function h(i){return i instanceof Array}function d(i){return typeof i.length=="number"}function j(i){return i.filter(function(i){return i!==k&&i!==null})}function e(i){return i.replace(/::/g,"/").replace(/([A-Z]+)([A-Z][a-z])/g,"$1_$2").replace(/([a-z\d])([A-Z])/g,"$1_$2").replace(/_/g,"-").toLowerCase()}
function p(i){return i in o?o[i]:o[i]=RegExp("(^|\\s)"+i+"(\\s|$)")}function n(i,a){return a===k?f(i):f(i).filter(a)}function m(i,b,c,g){return a(b)?b.call(i,c,g):b}function l(i,a,b){var c=i%2?a:a.parentNode;c?c.insertBefore(b,!i?a.nextSibling:i==1?c.firstChild:i==2?a:null):f(b).remove()}function x(i,a){a(i);for(var b in i.childNodes)x(i.childNodes[b],a)}var k,g,f,s,u=[],q=u.slice,z=window.document,T={},o={},r=z.defaultView.getComputedStyle,t={"column-count":1,columns:1,"font-weight":1,"line-height":1,
opacity:1,"z-index":1,zoom:1},w=/^\s*<(\w+|!)[^>]*>/,y=[1,3,8,9,11],A=z.createElement("table"),B=z.createElement("tr"),C={tr:z.createElement("tbody"),tbody:A,thead:A,tfoot:A,td:B,th:B,"*":z.createElement("div")},F=/complete|loaded|interactive/,H=/^\.([\w-]+)$/,I=/^#([\w-]+)$/,J=/^[\w-]+$/,G={}.toString,v={},E,D,K=z.createElement("div");v.matches=function(i,a){if(!i||i.nodeType!==1)return false;var b=i.webkitMatchesSelector||i.mozMatchesSelector||i.oMatchesSelector||i.matchesSelector;if(b)return b.call(i,
a);var c;c=i.parentNode;(b=!c)&&(c=K).appendChild(i);c=~v.qsa(c,a).indexOf(i);b&&K.removeChild(i);return c};E=function(i){return i.replace(/-+(.)?/g,function(i,a){return a?a.toUpperCase():""})};D=function(a){return a.filter(function(b,c){return a.indexOf(b)==c})};v.fragment=function(a,b){b===k&&(b=w.test(a)&&RegExp.$1);b in C||(b="*");var c=C[b];c.innerHTML=""+a;return f.each(q.call(c.childNodes),function(){c.removeChild(this)})};v.Z=function(a,b){a=a||[];a.__proto__=arguments.callee.prototype;a.selector=
b||"";return a};v.isZ=function(a){return a instanceof v.Z};v.init=function(i,c){if(i){if(a(i))return f(z).ready(i);if(v.isZ(i))return i;var g;if(h(i))g=j(i);else if(b(i)){g=[f.extend({},i)];i=null}else if(y.indexOf(i.nodeType)>=0||i===window){g=[i];i=null}else if(w.test(i)){g=v.fragment(i.trim(),RegExp.$1);i=null}else{if(c!==k)return f(c).find(i);g=v.qsa(z,i)}return v.Z(g,i)}return v.Z()};f=function(a,b){return v.init(a,b)};f.extend=function(a){q.call(arguments,1).forEach(function(b){for(g in b)b[g]!==
k&&(a[g]=b[g])});return a};v.qsa=function(a,b){var c;return a===z&&I.test(b)?(c=a.getElementById(RegExp.$1))?[c]:u:a.nodeType!==1&&a.nodeType!==9?u:q.call(H.test(b)?a.getElementsByClassName(RegExp.$1):J.test(b)?a.getElementsByTagName(b):a.querySelectorAll(b))};f.isFunction=a;f.isObject=c;f.isArray=h;f.isPlainObject=b;f.inArray=function(a,b,c){return u.indexOf.call(b,a,c)};f.trim=function(a){return a.trim()};f.uuid=0;f.map=function(a,b){var c,g=[],s;if(d(a))for(s=0;s<a.length;s++){c=b(a[s],s);c!=null&&
g.push(c)}else for(s in a){c=b(a[s],s);c!=null&&g.push(c)}return g.length>0?[].concat.apply([],g):g};f.each=function(a,b){var c;if(d(a))for(c=0;c<a.length;c++){if(b.call(a[c],c,a[c])===false)break}else for(c in a)if(b.call(a[c],c,a[c])===false)break;return a};f.fn={forEach:u.forEach,reduce:u.reduce,push:u.push,indexOf:u.indexOf,concat:u.concat,map:function(a){return f.map(this,function(b,c){return a.call(b,c,b)})},slice:function(){return f(q.apply(this,arguments))},ready:function(a){F.test(z.readyState)?
a(f):z.addEventListener("DOMContentLoaded",function(){a(f)},false);return this},get:function(a){return a===k?q.call(this):this[a]},toArray:function(){return this.get()},size:function(){return this.length},remove:function(){return this.each(function(){this.parentNode!=null&&this.parentNode.removeChild(this)})},each:function(a){this.forEach(function(b,c){a.call(b,c,b)});return this},filter:function(a){return f([].filter.call(this,function(b){return v.matches(b,a)}))},add:function(a,b){return f(D(this.concat(f(a,
b))))},is:function(a){return this.length>0&&v.matches(this[0],a)},not:function(b){var c=[];if(a(b)&&b.call!==k)this.each(function(a){b.call(this,a)||c.push(this)});else{var g=typeof b=="string"?this.filter(b):d(b)&&a(b.item)?q.call(b):f(b);this.forEach(function(a){g.indexOf(a)<0&&c.push(a)})}return f(c)},eq:function(a){return a===-1?this.slice(a):this.slice(a,+a+1)},first:function(){var a=this[0];return a&&!c(a)?a:f(a)},last:function(){var a=this[this.length-1];return a&&!c(a)?a:f(a)},find:function(a){var b;
b=this.length==1?v.qsa(this[0],a):this.map(function(){return v.qsa(this,a)});return f(b)},closest:function(a,b){for(var c=this[0];c&&!v.matches(c,a);)c=c!==b&&c!==z&&c.parentNode;return f(c)},parents:function(a){for(var b=[],c=this;c.length>0;)c=f.map(c,function(a){if((a=a.parentNode)&&a!==z&&b.indexOf(a)<0){b.push(a);return a}});return n(b,a)},parent:function(a){return n(D(this.pluck("parentNode")),a)},children:function(a){return n(this.map(function(){return q.call(this.children)}),a)},siblings:function(a){return n(this.map(function(a,
b){return q.call(b.parentNode.children).filter(function(a){return a!==b})}),a)},empty:function(){return this.each(function(){this.innerHTML=""})},pluck:function(a){return this.map(function(){return this[a]})},show:function(){return this.each(function(){this.style.display=="none"&&(this.style.display=null);if(r(this,"").getPropertyValue("display")=="none"){var a=this.style,b=this.nodeName,c,g;if(!T[b]){c=z.createElement(b);z.body.appendChild(c);g=r(c,"").getPropertyValue("display");c.parentNode.removeChild(c);
g=="none"&&(g="block");T[b]=g}a.display=T[b]}})},replaceWith:function(a){return this.before(a).remove()},wrap:function(a){return this.each(function(){f(this).wrapAll(f(a)[0].cloneNode(false))})},wrapAll:function(a){if(this[0]){f(this[0]).before(a=f(a));a.append(this)}return this},unwrap:function(){this.parent().each(function(){f(this).replaceWith(f(this).children())});return this},clone:function(){return f(this.map(function(){return this.cloneNode(true)}))},hide:function(){return this.css("display",
"none")},toggle:function(a){return(a===k?this.css("display")=="none":a)?this.show():this.hide()},prev:function(){return f(this.pluck("previousElementSibling"))},next:function(){return f(this.pluck("nextElementSibling"))},html:function(a){return a===k?this.length>0?this[0].innerHTML:null:this.each(function(b){var c=this.innerHTML;f(this).empty().append(m(this,a,b,c))})},text:function(a){return a===k?this.length>0?this[0].textContent:null:this.each(function(){this.textContent=a})},attr:function(a,b){var s;
return typeof a=="string"&&b===k?this.length==0||this[0].nodeType!==1?k:a=="value"&&this[0].nodeName=="INPUT"?this.val():!(s=this[0].getAttribute(a))&&a in this[0]?this[0][a]:s:this.each(function(s){if(this.nodeType===1)if(c(a))for(g in a)this.setAttribute(g,a[g]);else this.setAttribute(a,m(this,b,s,this.getAttribute(a)))})},removeAttr:function(a){return this.each(function(){this.nodeType===1&&this.removeAttribute(a)})},prop:function(a,b){return b===k?this[0]?this[0][a]:k:this.each(function(c){this[a]=
m(this,b,c,this[a])})},data:function(a,b){var c=this.attr("data-"+e(a),b);return c!==null?c:k},val:function(a){return a===k?this.length>0?this[0].value:k:this.each(function(b){this.value=m(this,a,b,this.value)})},offset:function(){if(this.length==0)return null;var a=this[0].getBoundingClientRect();return{left:a.left+window.pageXOffset,top:a.top+window.pageYOffset,width:a.width,height:a.height}},css:function(a,b){if(b===k&&typeof a=="string")return this.length==0?k:this[0].style[E(a)]||r(this[0],"").getPropertyValue(a);
var c="";for(g in a)typeof a[g]=="string"&&a[g]==""?this.each(function(){this.style.removeProperty(e(g))}):c=c+(e(g)+":"+(typeof a[g]=="number"&&!t[e(g)]?a[g]+"px":a[g])+";");typeof a=="string"&&(b==""?this.each(function(){this.style.removeProperty(e(a))}):c=e(a)+":"+(typeof b=="number"&&!t[e(a)]?b+"px":b));return this.each(function(){this.style.cssText=this.style.cssText+(";"+c)})},index:function(a){return a?this.indexOf(f(a)[0]):this.parent().children().indexOf(this[0])},hasClass:function(a){return this.length<
1?false:p(a).test(this[0].className)},addClass:function(a){return this.each(function(b){s=[];var c=this.className;m(this,a,b,c).split(/\s+/g).forEach(function(a){f(this).hasClass(a)||s.push(a)},this);s.length&&(this.className=this.className+((c?" ":"")+s.join(" ")))})},removeClass:function(a){return this.each(function(b){if(a===k)return this.className="";s=this.className;m(this,a,b,s).split(/\s+/g).forEach(function(a){s=s.replace(p(a)," ")});this.className=s.trim()})},toggleClass:function(a,b){return this.each(function(c){c=
m(this,a,c,this.className);(b===k?!f(this).hasClass(c):b)?f(this).addClass(c):f(this).removeClass(c)})}};["width","height"].forEach(function(a){f.fn[a]=function(b){var c,g=a.replace(/./,function(a){return a[0].toUpperCase()});return b===k?this[0]==window?window["inner"+g]:this[0]==z?z.documentElement["offset"+g]:(c=this.offset())&&c[a]:this.each(function(c){var g=f(this);g.css(a,m(this,b,c,g[a]()))})}});["after","prepend","before","append"].forEach(function(a,b){f.fn[a]=function(){var a=f.map(arguments,
function(a){return c(a)?a:v.fragment(a)});if(a.length<1)return this;var g=this.length,s=g>1,u=b<2;return this.each(function(c,f){for(var e=0;e<a.length;e++){var q=a[u?a.length-e-1:e];x(q,function(a){a.nodeName!=null&&(a.nodeName.toUpperCase()==="SCRIPT"&&(!a.type||a.type==="text/javascript"))&&window.eval.call(window,a.innerHTML)});s&&c<g-1&&(q=q.cloneNode(true));l(b,f,q)}})};f.fn[b%2?a+"To":"insert"+(b?"Before":"After")]=function(b){f(b)[a](this);return this}});v.Z.prototype=f.fn;v.camelize=E;v.uniq=
D;f._WXJS=v;return f}();window._WXJS=r;(function(a){function c(a){return a._zid||(a._zid=l++)}function b(a,b,g,f){b=h(b);if(b.ns)var e=RegExp("(?:^| )"+b.ns.replace(" "," .* ?")+"(?: |$)");return(m[c(a)]||[]).filter(function(a){return a&&(!b.e||a.e==b.e)&&(!b.ns||e.test(a.ns))&&(!g||c(a.fn)===c(g))&&(!f||a.sel==f)})}function h(a){a=(""+a).split(".");return{e:a[0],ns:a.slice(1).sort().join(" ")}}function d(b,c,g){a.isObject(b)?a.each(b,g):b.split(/\s/).forEach(function(a){g(a,c)})}function j(b,g,f,
e,j,l){var l=!!l,k=c(b),p=m[k]||(m[k]=[]);d(g,f,function(c,g){var f=j&&j(g,c),u=f||g,q=function(a){var c=u.apply(b,[a].concat(a.data));c===false&&a.preventDefault();return c},f=a.extend(h(c),{fn:g,proxy:q,sel:e,del:f,i:p.length});p.push(f);b.addEventListener(f.e,q,l)})}function e(a,g,f,e){var h=c(a);d(g||"",f,function(c,g){b(a,c,g,e).forEach(function(b){delete m[h][b.i];a.removeEventListener(b.e,b.proxy,false)})})}function p(b){var c=a.extend({originalEvent:b},b);a.each(f,function(a,f){c[a]=function(){this[f]=
k;return b[a].apply(b,arguments)};c[f]=g});return c}function n(a){if(!("defaultPrevented"in a)){a.defaultPrevented=false;var b=a.preventDefault;a.preventDefault=function(){this.defaultPrevented=true;b.call(this)}}}var m={},l=1,x={};x.click=x.mousedown=x.mouseup=x.mousemove="MouseEvents";a.event={add:j,remove:e};a.proxy=function(b,g){if(a.isFunction(b)){var f=function(){return b.apply(g,arguments)};f._zid=c(b);return f}if(typeof g=="string")return a.proxy(b[g],b);throw new TypeError("expected function");
};a.fn.bind=function(a,b){return this.each(function(){j(this,a,b)})};a.fn.unbind=function(a,b){return this.each(function(){e(this,a,b)})};a.fn.one=function(a,b){return this.each(function(c,g){j(this,a,b,null,function(a,b){return function(){var c=a.apply(g,arguments);e(g,b,a);return c}})})};var k=function(){return true},g=function(){return false},f={preventDefault:"isDefaultPrevented",stopImmediatePropagation:"isImmediatePropagationStopped",stopPropagation:"isPropagationStopped"};a.fn.delegate=function(b,
c,g){var f=false;if(c=="blur"||c=="focus")a.iswebkit?c=c=="blur"?"focusout":c=="focus"?"focusin":c:f=true;return this.each(function(e,h){j(h,c,g,b,function(c){return function(g){var f,e=a(g.target).closest(b,h).get(0);if(e){f=a.extend(p(g),{currentTarget:e,liveFired:h});return c.apply(e,[f].concat([].slice.call(arguments,1)))}}},f)})};a.fn.undelegate=function(a,b,c){return this.each(function(){e(this,b,c,a)})};a.fn.live=function(b,c){a(document.body).delegate(this.selector,b,c);return this};a.fn.die=
function(b,c){a(document.body).undelegate(this.selector,b,c);return this};a.fn.on=function(b,c,g){return c==void 0||a.isFunction(c)?this.bind(b,c):this.delegate(c,b,g)};a.fn.off=function(b,c,g){return c==void 0||a.isFunction(c)?this.unbind(b,c):this.undelegate(c,b,g)};a.fn.trigger=function(b,c){typeof b=="string"&&(b=a.Event(b));n(b);b.data=c;return this.each(function(){"dispatchEvent"in this&&this.dispatchEvent(b)})};a.fn.triggerHandler=function(c,g){var f,e;this.each(function(h,d){f=p(typeof c==
"string"?a.Event(c):c);f.data=g;f.target=d;a.each(b(d,c.type||c),function(a,b){e=b.proxy(f);if(f.isImmediatePropagationStopped())return false})});return e};["focusin","focusout","load","resize","scroll","unload","click","dblclick","mousedown","mouseup","mousemove","mouseover","mouseout","change","select","keydown","keypress","keyup","error"].forEach(function(b){a.fn[b]=function(a){return this.bind(b,a)}});["focus","blur"].forEach(function(b){a.fn[b]=function(a){if(a)this.bind(b,a);else if(this.length)try{this.get(0)[b]()}catch(c){}return this}});
a.Event=function(a,b){var c=document.createEvent(x[a]||"Events"),g=true;if(b)for(var f in b)f=="bubbles"?g=!!b[f]:c[f]=b[f];c.initEvent(a,g,true,null,null,null,null,null,null,null,null,null,null,null,null);return c}})(r);(function(a){function c(a){var c=this.os={},d=this.browser={},j=a.match(/WebKit\/([\d.]+)/),e=a.match(/(Android)\s+([\d.]+)/),p=a.match(/(iPad).*OS\s([\d_]+)/),n=!p&&a.match(/(iPhone\sOS)\s([\d_]+)/),m=a.match(/(webOS|hpwOS)[\s\/]([\d.]+)/),l=m&&a.match(/TouchPad/),x=a.match(/Kindle\/([\d.]+)/),
k=a.match(/Silk\/([\d._]+)/),g=a.match(/(BlackBerry).*Version\/([\d.]+)/);if(d.webkit=!!j)d.version=j[1];if(e){c.android=true;c.version=e[2]}if(n){c.ios=c.iphone=true;c.version=n[2].replace(/_/g,".")}if(p){c.ios=c.ipad=true;c.version=p[2].replace(/_/g,".")}if(m){c.webos=true;c.version=m[2]}if(l)c.touchpad=true;if(g){c.blackberry=true;c.version=g[2]}if(x){c.kindle=true;c.version=x[1]}if(k){d.silk=true;d.version=k[1]}if(!k&&c.android&&a.match(/Kindle Fire/))d.silk=true}c.call(a,navigator.userAgent);
a.__detect=c})(r);(function(a){var c=function(){function a(){if(!d)return null;var b=d.deref();return b&&document.contains(b)?b:null}function c(a){var b=a.target;if(b.tagName==="AUDIO"||b.tagName==="VIDEO")switch(a.type){case "play":d=new WeakRef(b);j=true}}var d=null,j=false;document.addEventListener("play",c,true);document.addEventListener("ended",c,true);document.addEventListener("pause",c,true);return{recoverLastPlayback:function(){var c=a();if(!c)return Promise.resolve();try{if(c.paused)return c.play()}catch(d){return Promise.reject(d)}return Promise.resolve()},
isLastPlaybackEnded:function(){var c=a();if(!c&&j)return true;try{return!(!c||!(c.readyState>0&&(c.ended||c.currentTime>=c.duration)))}catch(d){return false}},isPlaying:function(){var c=a();if(!c)return false;try{return!(!c||c.paused||c.ended||!(c.currentTime<c.duration))}catch(d){return false}}}}();a.mediaTracker=c})(r);var R=function(){function a(a){return Object.prototype.toString.call(a).slice(8,-1).toLowerCase()}var c=function(){c.cache.hasOwnProperty(arguments[0])||(c.cache[arguments[0]]=c.parse(arguments[0]));
return c.format.call(null,c.cache[arguments[0]],arguments)};c.format=function(b,c){var d=1,j=b.length,e="",p=[],n,m,l,x;for(n=0;n<j;n++){e=a(b[n]);if(e==="string")p.push(b[n]);else if(e==="array"){l=b[n];if(l[2]){e=c[d];for(m=0;m<l[2].length;m++){if(!e.hasOwnProperty(l[2][m]))throw R('[sprintf] property "%s" does not exist',l[2][m]);e=e[l[2][m]]}}else e=l[1]?c[l[1]]:c[d++];if(/[^s]/.test(l[8])&&a(e)!="number")throw R("[sprintf] expecting number but found %s",a(e));switch(l[8]){case "b":e=e.toString(2);
break;case "c":e=String.fromCharCode(e);break;case "d":e=parseInt(e,10);break;case "e":e=l[7]?e.toExponential(l[7]):e.toExponential();break;case "f":e=l[7]?parseFloat(e).toFixed(l[7]):parseFloat(e);break;case "o":e=e.toString(8);break;case "s":e=(e=""+e)&&l[7]?e.substring(0,l[7]):e;break;case "u":e=Math.abs(e);break;case "x":e=e.toString(16);break;case "X":e=e.toString(16).toUpperCase()}e=/[def]/.test(l[8])&&l[3]&&e>=0?"+"+e:e;m=l[4]?l[4]=="0"?"0":l[4].charAt(1):" ";x=l[6]-(""+e).length;if(l[6]){for(var k=
[];x>0;k[--x]=m);m=k.join("")}else m="";p.push(l[5]?e+m:m+e)}}return p.join("")};c.cache={};c.parse=function(a){for(var c=[],d=[],j=0;a;){if((c=/^[^\x25]+/.exec(a))!==null)d.push(c[0]);else if((c=/^\x25{2}/.exec(a))!==null)d.push("%");else if((c=/^\x25(?:([1-9]\d*)\$|\(([^\)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-fosuxX])/.exec(a))!==null){if(c[2]){var j=j|1,e=[],p=c[2],n=[];if((n=/^([a-z_][a-z_\d]*)/i.exec(p))!==null)for(e.push(n[1]);(p=p.substring(n[0].length))!=="";)if((n=/^\.([a-z_][a-z_\d]*)/i.exec(p))!==
null)e.push(n[1]);else if((n=/^\[(\d+)\]/.exec(p))!==null)e.push(n[1]);else throw"[sprintf] huh?";else throw"[sprintf] huh?";c[2]=e}else j=j|2;if(j===3)throw"[sprintf] mixing positional and named placeholders is not (yet) supported";d.push(c)}else throw"[sprintf] huh?";a=a.substring(c[0].length)}return d};return c}(),t;t||(t={});(function(){function a(a){return a<10?"0"+a:a}function c(a){d.lastIndex=0;return d.test(a)?'"'+a.replace(d,function(a){var b=p[a];return typeof b==="string"?b:"\\u"+("0000"+
a.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+a+'"'}function b(a,d){var h,k,g,f,s=j,u,q=d[a];typeof n==="function"&&(q=n.call(d,a,q));switch(typeof q){case "string":return c(q);case "number":return isFinite(q)?""+q:"null";case "boolean":case "null":return""+q;case "object":if(!q)return"null";j=j+e;u=[];if(Object.prototype.toString.apply(q)==="[object Array]"){f=q.length;for(h=0;h<f;h=h+1)u[h]=b(h,q)||"null";g=u.length===0?"[]":j?"[\n"+j+u.join(",\n"+j)+"\n"+s+"]":"["+u.join(",")+"]";j=s;return g}if(n&&
typeof n==="object"){f=n.length;for(h=0;h<f;h=h+1)if(typeof n[h]==="string"){k=n[h];(g=b(k,q))&&u.push(c(k)+(j?": ":":")+g)}}else for(k in q)if(Object.prototype.hasOwnProperty.call(q,k))(g=b(k,q))&&u.push(c(k)+(j?": ":":")+g);g=u.length===0?"{}":j?"{\n"+j+u.join(",\n"+j)+"\n"+s+"}":"{"+u.join(",")+"}";j=s;return g}}if(typeof Date.prototype.toJSON!=="function"){Date.prototype.toJSON=function(){return isFinite(this.valueOf())?this.getUTCFullYear()+"-"+a(this.getUTCMonth()+1)+"-"+a(this.getUTCDate())+
"T"+a(this.getUTCHours())+":"+a(this.getUTCMinutes())+":"+a(this.getUTCSeconds())+"Z":null};String.prototype.toJSON=Number.prototype.toJSON=Boolean.prototype.toJSON=function(){return this.valueOf()}}var h=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,d=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,j,e,p={"\u0008":"\\b","\t":"\\t","\n":"\\n","\u000c":"\\f","\r":"\\r",
'"':'\\"',"\\":"\\\\"},n;if(typeof t.stringify!=="function")t.stringify=function(a,c,d){var h;e=j="";if(typeof d==="number")for(h=0;h<d;h=h+1)e=e+" ";else typeof d==="string"&&(e=d);if((n=c)&&typeof c!=="function"&&(typeof c!=="object"||typeof c.length!=="number"))throw Error("JSON.stringify");return b("",{"":a})};if(typeof t.parse!=="function")t.parse=function(a,b){function c(a,f){var e,d,h=a[f];if(h&&typeof h==="object")for(e in h)if(Object.prototype.hasOwnProperty.call(h,e)){d=c(h,e);d!==void 0?
h[e]=d:delete h[e]}return b.call(a,f,h)}var e,a=""+a;h.lastIndex=0;h.test(a)&&(a=a.replace(h,function(a){return"\\u"+("0000"+a.charCodeAt(0).toString(16)).slice(-4)}));if(/^[\],:{}\s]*$/.test(a.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,""))){e=eval("("+a+")");return typeof b==="function"?c({"":e},""):e}throw new SyntaxError("JSON.parse");}})();var G=G||function(a,c){var b={},h=b.lib=
{},d=function(){},j=h.Base={extend:function(a){d.prototype=this;var b=new d;a&&b.mixIn(a);b.hasOwnProperty("init")||(b.init=function(){b.$super.init.apply(this,arguments)});b.init.prototype=b;b.$super=this;return b},create:function(){var a=this.extend();a.init.apply(a,arguments);return a},init:function(){},mixIn:function(a){for(var b in a)a.hasOwnProperty(b)&&(this[b]=a[b]);a.hasOwnProperty("toString")&&(this.toString=a.toString)},clone:function(){return this.init.prototype.extend(this)}},e=h.WordArray=
j.extend({init:function(a,b){a=this.words=a||[];this.sigBytes=b!=c?b:4*a.length},toString:function(a){return(a||n).stringify(this)},concat:function(a){var b=this.words,c=a.words,e=this.sigBytes,a=a.sigBytes;this.clamp();if(e%4)for(var d=0;d<a;d++)b[e+d>>>2]=b[e+d>>>2]|(c[d>>>2]>>>24-8*(d%4)&255)<<24-8*((e+d)%4);else if(65535<c.length)for(d=0;d<a;d=d+4)b[e+d>>>2]=c[d>>>2];else b.push.apply(b,c);this.sigBytes=this.sigBytes+a;return this},clamp:function(){var b=this.words,c=this.sigBytes;b[c>>>2]=b[c>>>
2]&4294967295<<32-8*(c%4);b.length=a.ceil(c/4)},clone:function(){var a=j.clone.call(this);a.words=this.words.slice(0);return a},random:function(b){for(var c=[],d=0;d<b;d=d+4)c.push(4294967296*a.random()|0);return new e.init(c,b)}}),p=b.enc={},n=p.Hex={stringify:function(a){for(var b=a.words,a=a.sigBytes,c=[],e=0;e<a;e++){var d=b[e>>>2]>>>24-8*(e%4)&255;c.push((d>>>4).toString(16));c.push((d&15).toString(16))}return c.join("")},parse:function(a){for(var b=a.length,c=[],d=0;d<b;d=d+2)c[d>>>3]=c[d>>>
3]|parseInt(a.substr(d,2),16)<<24-4*(d%8);return new e.init(c,b/2)}},m=p.Latin1={stringify:function(a){for(var b=a.words,a=a.sigBytes,c=[],e=0;e<a;e++)c.push(String.fromCharCode(b[e>>>2]>>>24-8*(e%4)&255));return c.join("")},parse:function(a){for(var b=a.length,c=[],d=0;d<b;d++)c[d>>>2]=c[d>>>2]|(a.charCodeAt(d)&255)<<24-8*(d%4);return new e.init(c,b)}},l=p.Utf8={stringify:function(a){try{return decodeURIComponent(escape(m.stringify(a)))}catch(b){throw Error("Malformed UTF-8 data");}},parse:function(a){return m.parse(unescape(encodeURIComponent(a)))}},
o=h.BufferedBlockAlgorithm=j.extend({reset:function(){this._data=new e.init;this._nDataBytes=0},_append:function(a){"string"==typeof a&&(a=l.parse(a));this._data.concat(a);this._nDataBytes=this._nDataBytes+a.sigBytes},_process:function(b){var c=this._data,d=c.words,h=c.sigBytes,j=this.blockSize,n=h/(4*j),n=b?a.ceil(n):a.max((n|0)-this._minBufferSize,0),b=n*j,h=a.min(4*b,h);if(b){for(var p=0;p<b;p=p+j)this._doProcessBlock(d,p);p=d.splice(0,b);c.sigBytes=c.sigBytes-h}return new e.init(p,h)},clone:function(){var a=
j.clone.call(this);a._data=this._data.clone();return a},_minBufferSize:0});h.Hasher=o.extend({cfg:j.extend(),init:function(a){this.cfg=this.cfg.extend(a);this.reset()},reset:function(){o.reset.call(this);this._doReset()},update:function(a){this._append(a);this._process();return this},finalize:function(a){a&&this._append(a);return this._doFinalize()},blockSize:16,_createHelper:function(a){return function(b,c){return(new a.init(c)).finalize(b)}},_createHmacHelper:function(a){return function(b,c){return(new k.HMAC.init(a,
c)).finalize(b)}}});var k=b.algo={};return b}(Math);(function(){var a=G,c=a.lib,b=c.WordArray,h=c.Hasher,d=[],c=a.algo.SHA1=h.extend({_doReset:function(){this._hash=new b.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(a,b){for(var c=this._hash.words,h=c[0],m=c[1],l=c[2],o=c[3],k=c[4],g=0;80>g;g++){if(16>g)d[g]=a[b+g]|0;else{var f=d[g-3]^d[g-8]^d[g-14]^d[g-16];d[g]=f<<1|f>>>31}f=(h<<5|h>>>27)+k+d[g];f=20>g?f+((m&l|~m&o)+1518500249):40>g?f+((m^l^o)+1859775393):
60>g?f+((m&l|m&o|l&o)-1894007588):f+((m^l^o)-899497514);k=o;o=l;l=m<<30|m>>>2;m=h;h=f}c[0]=c[0]+h|0;c[1]=c[1]+m|0;c[2]=c[2]+l|0;c[3]=c[3]+o|0;c[4]=c[4]+k|0},_doFinalize:function(){var a=this._data,b=a.words,c=8*this._nDataBytes,d=8*a.sigBytes;b[d>>>5]=b[d>>>5]|128<<24-d%32;b[(d+64>>>9<<4)+14]=Math.floor(c/4294967296);b[(d+64>>>9<<4)+15]=c;a.sigBytes=4*b.length;this._process();return this._hash},clone:function(){var a=h.clone.call(this);a._hash=this._hash.clone();return a}});a.SHA1=h._createHelper(c);
a.HmacSHA1=h._createHmacHelper(c)})();var K=[],ma=1E3,M={},N={},B={},S="unauthorized",W="__msg_type",E="__callback_id",D="__event_id",fa="weixin://dispatch_message/",Y=[],H={},I={},X="__runOn3rd_apis",L="xx_yy",ja="__json_message",ha="__msg_queue",$="__context_key",Z="",Q="isUseMd5_check",U="__sha_key",ia=V,ga=P,ka=w,na=aa,oa=ba,la=o,ya=window.alert;window.alert=function(a){if(!(document.__wxjsjs__isWebviewWillClosed==="yes"||document.__wxjsjs__isDisableAlertView==="yes"))return ya(a)};var za=window.prompt;
window.prompt=function(a,c){if(!(document.__wxjsjs__isWebviewWillClosed==="yes"||document.__wxjsjs__isDisableAlertView==="yes"))return za(a,c)};var A={invoke:o,call:o,on:aa,subscribe:ba,unsubscribe:pa,log:w,state:qa,_getSelectedText:da,_fetchQueue:P};try{Object.defineProperty(A,"_handleMessageFromWeixin",{value:V,writable:!1,configurable:!1})}catch(Ca){return}if(window.WeixinJSBridge)r.extend(window.WeixinJSBridge,A);else try{Object.defineProperty(window,"WeixinJSBridge",{value:A,writable:!1,configurable:!1})}catch(Da){return}xa();
r.JSON=t;r.disableImageSelection=ta;r.restoreImageSelection=ua;r.disableAlertView=va;r.restoreAlertView=wa;if(__ISWKWEBVIEW){window.__wxjs_is_wkwebview=!0;r("document").ready(function(){var a=document.oncopy;document.oncopy=function(){var c=da();J(t.stringify({__onCopy:c}));if(typeof a!=="undefined"&&a!==null)return a()};J(t.stringify({__domReadyNotify:L}))});if(history.pushState){var Aa=history.pushState;history.pushState=function(a,c,b){var h=ea(b);J(t.stringify({__pageStateChange:h}));Aa.apply(this,
arguments)}}if(history.replaceState){var Ba=history.replaceState;history.replaceState=function(a,c,b){var h=ea(b);J(t.stringify({__pageStateChange:h}));Ba.apply(this,arguments)}}}else window.__wxjs_is_wkwebview=!1}})();window.__wxjs_is_injected_success="yes";_WXJS.disableImageSelection();
