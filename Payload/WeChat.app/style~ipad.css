#album_comment_view
{
    font : 15 bold;
    nick_label_font_size : 18 bold;
}

#finder_comment_view
{
    font : 15 ;
    small_text_font_size : 14 bold;
}

#finder_richtext_view_font
{
    font : 12;
    nick_label_font_size : 12 bold;
}

#bottom_bar
{
    height : 60;
    button_width : 60;
}

#themeMainframeItemView
{
	/*headimg outter frame*/
	HeadFrame : 5 0 54 54  ;
}

#login_common
{
    backgroundColor: #FFFFFF;
    textfield_leftview_width: 103;
    agree_checkbox_topMargin: 48;
    nextbtn_margin_agree_checkbox: 16;
    nextbtn_topMargin: 30;
    title_topMargin: 96;
    title_leftMargin: 0;
    newHeaderViewTitle_topMargin: 116;
    title_bottomMargin: 40;
}

#first_user_view_offset
{
    loginBtn : 15;
    languageBtnY : 3;
    languageBtnX : 40;
    loginBtnWidth : 190;
    loginBtnMarginBottom : 75;
    loginBtnMarginLeft : 134;
}

#message_load_count{
    first_page_msg_count : 18;
}

#message_content_viewController 
{
    content_sepm_margin : 20;
}

#message_node_view
{
    message_node_bkg_width : 350;
    message_node_bkg_shorter_width: 300;

    message_node_content_margin_top : 15;
    message_node_content_margin_left: 15;

    /*文本节点宽度*/
    text_message_node_width : 370 "ew";
    text_message_margin_top : 13;
    /*  mail节点单独计算  */
    mail_message_node_width : 350;
    /*头像*/
    message_node_headImgBorder_leftMargin : 16;
    
    message_node_timeNode_height : 25 dynamic;

    message_node_voiceNode_rate : 1.5;
    message_node_voiceNode_min_width : 92;
    message_node_imageNode_maxSizeLimit : 320;
    
    title_label_font: 19 dynamic;
    desc_label_font: 14 dynamic;
    title_to_desc_margin: 8;
    thumb_image_width: 68;
    
    appinfo_area_height: 24;
    appinfo_title_font_size: 12;
}

#input_tool_view
{
    tool_bar_height : 60;
}

/*输入框*/
#input_tool_view_tool
{
    /*tool view height*/
    tool_view_height : 60 dynamic;
    capture_view_x_inset : 85;
    
    tool_view_min_textView_height : 30;

    /*element inset*/
    tool_view_btn_topMargin : 12.5;
    tool_view_element_inner_margin : 5;
    tool_view_textView_topMargin : 5 dynamic;
    record_btn_topMargin : 5;
}

/* 输入框附件栏 */
#input_tool_view_attachment 
{
	/*icon的大小*/
    text_attachment_item_width : 8A;
    text_attachment_item_height : 8A;
    text_attachment_item_landscape_margin : 20;
    text_attachment_label_topMargin : 10;
	/*badge*/
	text_attachment_badge_offset : 38 -10;
    text_attachment_redcode_offset : 62 -5;
	/*键盘高度*/
	text_attachment_height:264;

    /*实际图片位置及大小*/
    text_attachement_rect : 0 0 59 59 ;
    
    attachment_view_margin : 100 "eh";
    attachment_view_margin_landscape : 120 "eh";
}

/*表情管理cell*/
#EmoticonManageCell
{
    buttonMarginRight_IOS7 : 15;
}

/*商店表情面板*/
#EmoticonGridView 
{
	m_row : 2;
	m_column : 4;
	m_edgeLeft : 20;
	m_edgeTop : 30;
	m_intervalY : 20;
	m_edgeTop_desc : 28;
	m_intervalY_desc : 28;
	m_intervalX : 18;
	m_itemWidth : 66.5;
	m_itemHeight : 66.5;
}

/*朋友圈timeline*/
#WCTimeLineViewController {
    head_view_height : 400;
    lineview_padding_left : 40;
    lineview_padding_right : 40;

    input_view_height : 56;
    input_view_textview_height : 40;
    inputView_width_inset : 56;
    input_view_margin_top : 8;
    expression_btn_topMargin : 12;
}

#WCTimelineCellView {
    content_width : 550 "ew";
    /*翻译Brand字体大小*/
    translate_brand_font_size: 12;

    /* sceenwidth - text_message_width_inset = text width */
    text_message_width_inset : 152 "ew";
    text_line_width : 616 "ew";

    headimage_view_len : 50;
    headimage_padding_right : 10;

    margin_left : 40 "ew";
    margin_top : 20 "ew";
    margin_right : 40 "ew";
    margin_bottom : 15 "ew";

    subview_padding_height : 15;
    ad_subview_padding_height : 15;
    subview_next_padding_height_L : 15;
    subview_next_padding_height_S : 15;

    default_lable_height : 20;

    nickname_label_padding_top : 5;
    nickname_label_padding_bottom : 10;
    nickname_label_padding_right : 5;

    operation_btn_right_margin : 0;
    fulltext_view_bottom_margin : 10;

    /*长文本计算*/
    text_single_line_height : 22.5;
    text_line_height : 23;
    text_desc_content_height : 44;
    
    advertise_action_btn_width : 30;
    advertise_action_btn_height : 22;
    advertise_action_btn_margin : 5;
    advertise_logo_font_size : 13;
    advertise_link_font_size : 18;
    
    operate_btn_horizonal_inset : 0;
    operate_btn_vertical_inset : 8;
    
    button_subview_padding_width : 8 dynamic;
    
    more_action_floatView_width : 666 "ew";
}

#WCListHeaderView {
    headimage_bkg_view_padding_right : 30;
    headimage_bkg_view_len : 120;
    nick_label_font_size : 25;
    sign_label_font_size : 16;
    sign_label_padding_left : 30;
    sign_label_padding_right : 30;
    sign_label_padding_top : 10;
}

#WCTimeLineCommentCellView {
    comment_view_width : 618 "ew";
    padding_left : 110 "ew";
    padding_left_offset : 0;
    
    text_font_size : 15;
    
    like_content_margin : 10;
    like_content_left_margin : 10;
    comment_content_margin_left : 10;
    comment_content_margin_top : 7;
    comment_content_margin_bottom : 7;
    
    /*翻译Brand字体大小*/
    comment_translate_brand_font_size: 13;
}

#WCTimeLineCellMediaContentView {
    /*template gird*/
    multi_image_len : 150 "ew";
    image_size_max_len : 300 "ew";
    image_size_superwide_max_len : 460;
    image_size_superheight_max_len : 152;
    image_size_superheight_min_len : 55;
    image_gird_step_len : 5;
    sight_size_max_len : 450 "ew";
    /*template news, 链接和类似链接都改这里*/
    music_area_width : 600 "ew";
    link_area_width : 600 "ew";
    client_rect_margin : 5;
    news_image_len : 70 "ew";
    news_none_image_height : 26;
    link_font_size : 14;
    link_title_font_size : 14;
    
    hotvideo_title_font_size : 15;
    
    subview_margin : 10;
    subview_vertical_margin : 10;
    title_top_margin : 15;
    desc_top_margin : 10;
    news_title_single_line_height : 20;
    
    original_icon_width: 40;
    original_icon_height: 17;

    /*template classic*/
    cover_image_len : 70 "ew";
    music_title_single_line_height : 24;
}

/* 新晒一晒 */
#WCContentItemViewTemplateNewSight {
    cover_image_width : 320 "ew";
    cover_image_height : 240 "ew";
}

/* 我的相册 */
#WCListView {
    head_view_height : 400;
}

#WCListViewRowView {
    time_label_padding_left : 0;
    time_label_margin_left : 30 "ew";
    time_label_width : 175 "ew";
    day_label_font_size : 30 ;
    month_label_font_size : 18 ;
    month_label_height : 20;
    month_label_move_bottom : 11;
    city_label_font_size : 16;
    city_label_padding_top : 20;
    city_label_width : 95;
    city_label_height : 29;
    section_padding_height : 40;
    section_cell_padding_height : 10;
    photo_item_size : 150 "ew";
    photo_item_label_padding_left : 20;
    photo_item_desc_label_font_size : 19;
    photo_item_count_label_font_size : 16;
    photo_item_count_label_height : 18;
    
    contentView_left_offset : 20;
}

#WCListViewRowContentView {
    content_view_title_desc_margin : 4;
    content_view_width : 555 "ew";
    content_view_height : 140 "ew";
    content_view_margin : 10;
    content_view_padding : 10;
    content_view_media_margin : 0;
    content_view_media_height : 120;
    default_text_font_size : 17;
    default_text_line_height : 23;

    year_label_left_margin : 30;
    year_label_top_margin : 8;
    year_label_bottom_margin : 32;
    year_label_font_size : 30;
}

/*朋友圈详情*/
#WCComentDetailViewControllerFB {
    headerView_head_image_len : 60;
    headerview_head_left_margin : 40 "ew";
    desc_content_inset : 0;
    operation_btn_right_margin : 40 "ew";
    comment_head_image_len : 45;
    comment_head_count_perline : 11;
    comment_view_left_margin : 110 "ew";
    comment_view_width : 618 "ew";
    like_image_content_font_size : 16;
}

#WCCommentView {
    time_font_size : 14;
    normal_font_size : 19;
    content_font_size : 19;
    content_top_margin : 15;
    content_bottom_margin : 15;
    content_right_margin : 10;
    time_label_right_margin : 15;
    head_image_right_margin : 10;
    head_image_left_margin : 38;
    name_label_bottom_margin : 6;
    comment_logo_view_len : 20;
    comment_logo_view_top_margin : 26;
}

#WCCommitViewController {
    textInput_view_topMargin : 10;
    textInput_view_inset : 15;
    gridimg_stepWidth : 5;
    gridimg_size:100 100;
    gridimg_single_sizeWidth:300;
}

#ShakeMainView {
    UpViewLogo_Width : 240 "ew";
    UpViewLogo_Height : 120 "ew";
    DownViewLogo_Width : 240 "ew";
    DownViewLogo_Height : 120 "ew";
    SingleView_Y : 128;
}

#CameraScan {
    qrcode_tips_height : 40;
    book_tips_height : 30;
    ocr_top : 104;
    
    book_crop_area_width : 677 "ew";
    book_crop_area_height : 643 "ew";
}

#VoipView
{
    warningTips_top_margin : 560;
    warningTips_left_margin : 30;
    warningTips_left_span : 15;
    warningTips_font_size : 13;
    warningTips_width : 260;
    warningTips_height : 30;
    warningTips_fill_color : "#E64340";
    warningTips_font_color : "#FFFFFF";
}

/*公众号会话视图*/
#reader_node_view {
    reader_item_lmargin: 20;
    reader_item_hmargin: 22;
    reader_view_width: 464 "ew";
    cover_image_height: 262 "ew";
    reader_view_max_height: 580;
    
    rich_text_font: 20.0 "dynamic";
    digest_font: 18.0 "dynamic";
    time_font_size: 16.0 "dynamic";
    read_all_font_size: 18.0 "dynamic";
    
    title_counting: 10;
    title_and_time_margin: 10;
    time_counting: 30;
    cover_counting: 25;
    digest_counting: 30;
    read_all_top_margin: 23;
    right_arrow_counting: 20;
}

/*公众号会话文本消息卡片视图*/
#reader_text_node_view {
    
    text_content_max_height: 262 "dynamic";  /* 单文本消息内容最大高度(除详情cell高度)*/
}

/*公众号会话语音消息卡片视图*/
#reader_voice_node_view {
    
    voice_top_item_height: 193 "ew";
    title_left_padding: 20;
    
    play_btn_width: 45;
    play_normal_btn_width: 30;
    
    play_btn_right_padding: 20;
    title_btn_spacing: 20;
}

/*公众号会话图片消息卡片视图*/
#reader_image_node_view {
    
    image_top_item_height: 262 "ew";
}

/*公众号会话视频消息卡片视图*/
#reader_video_node_view {
    
    video_top_item_height: 262 "ew";
    title_left_padding: 20;
    
    play_btn_width: 45;
    play_normal_btn_width: 30;
    
    play_btn_right_padding: 20;
    title_btn_spacing: 20;
}

/*公众号多文章视图子按钮*/
#read_item_view {
    reader_top_new_height: 262 "ew";
    reader_cover_height: 60 "dynamic";
    reader_item_lmargin: 20;
    reader_item_hmargin: 22;
    
    cover_title_font: 20.0 "dynamic";
    rich_text_default_font: 20.0 "dynamic";
    
    header_image_margin: 20;
    title_margin: 8;
    header_image_line_margin: 10;
    
    header_background_image_margin: 7;

    reader_top_item_digest_font: 18.0 "dynamic";
}

#ui_common_util {
    barbutton_width_max : 134;
}

#attributed_reader_message_node_view  {
    reader_view_width: 600 "ew";
    reader_item_lmargin: 20;
}

#location_message_node_view {
    location_image_height:130;
}

#mmasset_define {
    asset_view_margin : 12 "ew";
    asset_view_margin_lanscape : 24 "eh";
    asset_view_markView_offset : 8;
    asset_view_column_lanscape: 5;
}

#hardward_message_node_view {
    node_view_width: 320;
    node_view_height: 191;
    
    notify_node_width: 320;
    notify_node_heigt: 80;
}

#online_device_info {
    deviceViewY : 168;
    tipsLabelYOffset : 30;
    descriptionLabelYOffset : 15;
    logOutBtnWidth : 300;
    logOutBtnMarginDeviceViewTop: 450;
    logOutBtnMarginBottom: 96;
    bottomLineBottomOffset : 120;
    switchDescYOffset : 32;
    switchYOffset : 24;
}

#device_rank_view {
    Rank_View_Cell_Score_RightToBound : 70;
    tableheader_height : 477;
    champion_tip_font_size : 30;
    
    h_margin : 40;
    
    normal_height : 148;
    champion_height : 245;
    
    normal_header_image_width : 95;
    champion_header_image_width : 140;
    
    ranknum_width : 75;
    
    header_image_left : 93;
    
    progress_left : 208;
    progress_top : 92;
    progress_right : 175;
    
    scorelabel_top : 47;
    
    nicklabel_left : 208;
    nicklabel_top : 47;
    
    ranknum_font_size : 47;
    ranknum_2digits_font_size : 37;
    ranknum_3digits_font_size : 28;
    other_font_size : 30;
    
    other_Color : #787676;
    
    likebtn_width : 175;
    likebtn_height : 148;
    likecount_to_likeimg : 10;
    
    likebtn_bottom_champion : 39;
    likebtn_bottom_normal : 41;
}

#device_profile_view {
    tableheader_height : 477;
    h_margin : 40;
    header_image_width : 140;
    affect_image_width : 69;
    
    step_content_height : 510;
    step_v_margin : 95;
    step_h_margin : 10;
    step_values_height : 340;
    steps_header_height : 84;
    steps_footer_height : 60;
    steps_title_font_size : 43;
    steps_score_font_size : 50;
    steps_time_font_size : 20;
    steps_point_size : 10;

    tip_font_size : 30;
    tip_height : 66;
    tip_color: rgba(255,255,255,0.6);
    tip_to_affect : 10;
    affect_head_right : 20;
    affect_height : 69;
    
    tablefooter_height: 160;
    tablefooter_button_top : 2.5A;
    tablefooter_button_width : 230;
    tablefooter_button_height : 70;
    tablefooter_button_font_size : 28;
    
    time_switch_height : 46;
    time_switch_width : 92;
    time_knob_size : 40;
    time_switch_font_size : 24;
    time_switch_padding : 20;
    
    abroad_cell_height : 300;
    abroad_score_font_size: 50;
    abroad_score_title_font_size: 22;
}

/*手势密码*/
#pattern_lock_view {
    circle_radius : 34;
    circle_gap : 40;
    
    tipsView_y : 50;
    tipsView_bottom_offset : 56;
}

#NewSetFontVC
{
    slider_width : 400;
}

#brand_custom_style_ui_logic_controller {
    tool_bar_height: 60.0;
}

/*公众号自定义菜单*/
#custom_menu {
    tool_bar_height: 60.0;
}

#horizontal_table_view_cell {
    cell_height: 59.0;
    
}

#VoipView
{
    footerActionButtonWidth : 75;
    footerActionButtonHeight : 75;
    toastWindowMarginBottom : 340;
    toastWindowMarginBottomAdpatAudioMode : 340;
    toastWindowMarginBottomAdpatVideoReceiverWaitingMode : 340;
    toastWindowMarginBottomAdpatMinimizeMode : 95 dynamic;
    leftButtonMarginLeft : 230;
    rightButtonMarginRight : 230;
    footerButtonMarginButtom : 30;
    secondFooterButtonMarginBottom : 225;
    timerLabelMarginBottom : 145 dynamic;
    netStatusLabelMarginBottom : 55;
}

#RecordDetailView {
    head_left_margin : 84;
    right_margin : 84;
}

#text_actionSheet
{
    actionsheet_width : 500 "ew";
}

#BizMainHeaderView
{
    height : 135;
    padding-top :30;
    titleFontSize : 16;
    descFontSize : 13;
    margin : 15;
}

#BizMainSectionHeaderView
{
    height : 60;
    fontSize : 14;
}

#WCMallActivityView
{
    activityLabelFontSize:14;
    activityFunctionLabelFontSize:12;
    margin : 10;
}

#MsgSearchItem
{
    max_img_width : 300;
}

#FavComponent
{
    max_img_width : 300;
}

#WCOutView {
    /* round button pad */
    padBtnDiameter : 118;
    dialPadVerticalPadding : 1;
    dialPadHorizontalPadding : 1;
    /* dialview */
    inputArea_y: 100;

}

#MultiTalkTalkingOperateView
{
    title_margin_top : 20;
    title_margin_left : 20;
    title_height : 30;
    title_font_size : 24;
    title_text_color : "#ffffff";
    
    timer_margin_left : 20;
    timer_margin_top : 5;
    timer_height : 18;
    timer_font_size : 15;
    timer_text_color : "#ffffff";
    
    minimize_button_center_margin_left : 50;
    minimize_button_center_margin_bottom : 115;
    minimize_button_width : 60;
    minimize_button_height : 60;
    minimize_button_title_margin_top : 8;
    
    hangup_button_center_margin_bottom : 76;
    hangup_button_height : 75;
    
    righttop_button_center_margin_center : 110;
    righttop_button_center_margin_bottom : 150;
    righttop_button_width : 60;
    righttop_button_height : 78;
    righttop_title_margin_top : -19;
    
    middletop_button_center_margin_left : 60;
    middletop_button_center_margin_bottom : 150;
    middletop_button_width : 60;
    middletop_button_height : 78;
    middletop_title_margin_top : -19;
    
    lefttop_button_center_margin_center : 110;
    lefttop_button_center_margin_bottom : 150;
    lefttop_button_width : 60;
    lefttop_button_height : 78;
    lefttop_title_margin_top : -19;
}

#MultiTalkBottomOperatePanel
{
    description_button_width : 64;
    description_button_height : 83.2;
    description_button_title_offset : -19;
    description_button_left_right_margin : 64;
    description_button_top_margin : 40;

    hangup_button_size : 72;
    hangup_button_bottom_margin : 32;
    hangup_button_folded_size : 44;
    hangup_button_folded_bottom_margin : 24;

    arrow_button_size : 32;
    arrow_button_left_margin : 40;
    
    flip_camera_button_size : 64;
    flip_camera_button_right_margin : 64;
    flip_camera_button_bottom_margin : 36;

    panel_corner_radius : 16;
    panel_bottom_extra_height : 20;
    panel_height : 272;
    panel_folded_height : 92;
    
    mute_button_image_name : "multitalkMuteMode.png";
    mute_button_hl_image_name : "multitalkMuteModeHL.png";
    mute_button_selected_image_name : "multitalkMuteModeOn.png";
    
    speaker_button_image_name : "multitalkSpeakerMode.png";
    speaker_button_hl_image_name : "multitalkSpeakerModeHL.png";
    speaker_button_selected_image_name : "multitalkSpeakerModeOn.png";
    
    video_button_image_name : "multitalkVideo.png";
    video_button_hl_image_name : "multitalkVideoHL.png";
    video_button_selected_image_name : "multitalkVideoOn.png";
}

#MultiTalkBeforeTalkingOperateView
{
    hangup_button_margin_center : 50;
    hangup_button_center_margin_bottom : 76;
    hangup_button_height : 75;
    button_interval : 10;
}

#NewMultiSelectViewController
{
    select_display_view_height : 200;
    
    select_display_item_topOffset1 : 28;
    select_display_item_topOffset2 : 10;
    select_display_item_leftOffset1 : 30;
    select_display_item_leftOffset2 : 25;
    select_display_item_interSpacing1 : 30;
    select_display_item_interSpacing2 : 25;
    select_display_item_lineSpacing1 : 100;
    select_display_item_lineSpacing2 : 10;
}

#CardItemView
{
    card_left : 30.0;
    top_height : 7.0;
    normal_height : 136.0;
    tooth_height : 3.0;
    bottom_mask_height : 34.0;
    title_font : 13.0;
    title_2_sub : 8.0;
    sub_title_font : 25.0;
    bottom_font : 13.0;
    logo_width : 42.0;
    logo_left : 15.0;
    logo_top : 20.0;
    head_width: 54.0;
    head_left : 14.0;
    entry_title_font : 20.0;
    entry_sub_title_font : 16.0;
    invoice_title_left: 20;
}

#CardDetailView
{
    table_left_margin : 32.0;
    table_right_margin : 32.0;
}

#CardCodeView
{
    begin_y : 60.0;
    bottom_margin : 150.0;
    dashline_margin : 35.0;
    checkbox_fit_y  : 35.0;
    tips_fit_y : 100.0;
    qrcode_img_height : 168.0;
}

#CardConsumedView
{
    doneLabel_bottom : 82;
}

#DesignerEmoji {
    DesignerEmojiVC_CellNumInOneLine: 0; /* iPhone 固定个数然后算宽度，iPad保持最小宽度 */
    DesignerEmojiVC_CellMinWidth: 105;
}
#MusicPlayerMainViewController
{
    second_page_cover_height : 364;
    /*cover_image_offset = (first_page_cover_height-second_page_cover_height)/2 */
    cover_image_offset : 168;
    shade_Gradient_height : 240;
    airplay_right_margin : 60;
    airplay_bottom_margin : 125;
    music_title_above_cover_bottom : 5A;
}

#fts_common {
    searchbar_tag_max_width : 300;
}

#search_guide {
    sg_barbutton_top_margin : 58;
    sg_verts_title_top_margin : 37;
    sg_verts_title_bottom_margin_for_single_row : 34;
    sg_verts_title_bottom_margin_for_multi_row : 28.5;
    sg_verts_button_width : 200;
    sg_verts_button_intv: 33;
    
    sg_verts_title_font_size : 15.5 dynamic;
    sg_verts_title_font_size_for_discovery : 14.5 dynamic;
    sg_verts_button_title_font_size : 17.5 dynamic;
    sg_verts_button_title_font_size_for_discovery : 17.5 dynamic;
    sg_verts_button_title_small_font_size : 15.5 dynamic;
    sg_verts_button_title_small_font_size_for_discovery : 15.5 dynamic;
    
    sg_msg_verts_button_width : 200;
    
    sg_weapp_content_width : 300;
    sg_weapp_title_bottom_margin : 30;
    sg_weapp_title_top_margin : 50;
}

#search_discovery {
    discovery_search_text_font_size : 17;
}

#EmotionStorePersenalCell {
    view_count_per_row: 0;
}

#NewYearSnsFeedView
{
    imageview_width : 180;
    imageview_height : 135;
}

#WCNewYearEnterpriseHBDetailView
{
    default_padding : 30;
    bgView_top_margin : 62;
    bgView_left_margin : 22;
    
    coverFrame_top_margin : 27;
    coverFrame_left_margin : 12;
    
    headImage_top_margin : 15;
    headImage_size : 42;
    
    nameLabel_font_size : 14;
    nameLabel_top_margin : 10;
    
    numView_font_size : 40;
    numView_top_margin : 34;
    
    moneyLabel_font_size : 11;
    
    tipsLabel_font_size : 13;
    tipsLabel_top_margin : 36;
    
}

#WCPayFetchView
{
    top_padding_1: 20;
    top_padding_2: 20;
    top_padding_3: 15;
    top_padding_4: 9;
    cardInfo_leftMargin : 24;
    card_logo_len : 16;
    
    money_textfield_font : 56;
}

#ShareCardMemberCardView
{
    img_left_margin : 32;
    img_top_margin_to_bar : 20;
    img_top_margin_to_title : 40;
    secondary_field_top_padding : 26;
}

#WCPayOverseaTransferViewController
{
    head_image_view_top_margin : 15;
    display_name_label_top_margin : 10;
    panel_view_left_margin : 15;
    panel_view_top_margin : 15;
    panel_view_bottom_margin : 15;
    input_title_label_top_margin : 20;
    input_title_label_left_margin : 20;
    currency_input_view_left_margin : 20;
    currency_input_view_top_margin : 15;
    currency_input_view_height : 55;
    line_view_left_margin : 20;
    line_view_top_margin : 5;
    line_view_height : 0.5;
    comment_text_view_left_margin : 20;
    comment_text_view_top_margin : 10;
    comment_text_view_height : 16;
    confirm_button_left_margin : 30;
    confirm_button_top_margin : 30;
    hkMoneyLabelFont : 40;
}

#WCPayCurrencyInputView
{
    currency_label_font_size : 40;
    number_text_field_font_size : 55;
    number_text_field_height : 55;
}

#WCPayOverseaMainWalletCell
{
    item_height : 160;
    icon_view_margin_top : 45;
}

#WCPayOverseaMainWalletCollectionView
{
    row_count_protrait : 5;
    row_count_landscape : 6;
    footer_ext_height : 1024;
    banner_height : 135;
}

#WAHomeListView
{
    row_num_perpage : 4;
    row_count : 4;
    
    cell_width : 175.3;
    cell_height : 150;
    section_hon_margin : 20;
    section_ver_margin : 10;
}


#WCBackupEntryView
{
    icon_image_content_y : 180;
    icon_image_height : 100;
    icon_image_width : 160;
    icon_label_gap : 52;
    tip_max_content_x : 25;
    tip_tip_gap : 14;
    green_button_height : 35;
    grey_button_height : 17;
    button_button_gap : 66;
    grey_button_bottom : 32;
    left_corner_button_y : 20;
    left_corner_button_x : 16;
}

#MultitalkBannerView
{
    invite_nick_max_len : 18;
}

#WCPayTransferMoneyViewController
{
    currencyLabelFont : 30;
    moneyLabelFont : 50;
    hkMoneyLabelFont : 42;
    moneyLabelHeight : 55;
    moneyLabelTopMargin : 5;
    transferBtnTopMargin : 30;
}

#WCPayTransferMoneyPaidSuccessViewV2
{
    WeChatPayIcon_margin_top : 8.5A;
    WeChatPayIcon_height : 44;
    WeChatPayIcon_width : 50;
    
    successLabel_margin_top : 146;
    successLabel_font_size : 17;
    
    tipLabel_margin_top : 66;
    tipLabel_font_size : 17;
    tipLabel_font_color : "#353535";
    
    moneyLabel_margin_top : 14;
    moneyLabel_margin_top_on_transfer : 14;
    moneyLabel_font_size : 48;
    moneyLabel_font_color : "#353535";
    
    receiverLabel_margin_top : 64;
    dataLabel_margin_up : 9;
    dataLabel_margin_left : 35;
    
    favorLineMarginUp : 23;
    
    dataLabel_font_size : 17;
    dataLabel_font_color : "#888888";
    
    doneButton_margin_bottom : 96;
    doneButton_width : 180;
    
    head_image_size : 28;
    head_image_margin_right : 8;
    
    line_view_margin_top : 18;
    line_view_margin_top_on_transfer : 81;
    line_view_margin_left : 35;
    line_view_height : 0.5;
    line_view_color : "#C7C7C7";
}

#OfflinePayView
{
    actionbar_top_margin : 15;
    cardInfo_view_height : 80;
    actionbar_content_height : 64;
    
    bottom_button_content_height : 67;
    bottom_button_elem_title_font : 17;
    bottom_button_elem_subTitle_font : 17;
    
    codeview_noticecontent_margin : 30;
    codeview_barcode_container_height : 136;
    codeview_barcode_height : 102;
    codeview_qrcode_width : 147;
    codeview_qrcode_bottom_margin : 30;
    codeview_tips_font : 14;
    
    change_card_view_bankName_font : 17;
    change_card_view_bankDesc_font : 14;
    change_card_view_forbidDesc_font : 14;
}

#VoiceInputViewController
{
    halfScreenPadHeight: 330;
    cancel_Button_Left_Margin: 45;
    textViewDefaultHeight: 110;
}

#FaceHB
{
    get_AvatarWidth : 60;
    get_OpenedAvatarWidth : 70;
    get_OpenViewWidth : 325;
    get_OpenViewHeight : 430;
    get_mask_image_height : 245;
    get_OpenBtn_topMargin : 82;
    
    pay_QR_Width : 210;
    pay_QR_Top_Margin : 80;
    pay_HB_SmallWidth : 130;
    pay_HB_BigWidth : 400;
    pay_HB_Name_Font : 18;
    pay_HB_Yuan_Font : 18;
    pay_HB_Amount_Font : 50;
    pay_HB_SelectY : 35;
    
    pay_Receive_Top_Margin : 40;
    pay_Receive_HeadWidth : 50;
    pay_Receive_MaxShowCount : 7;
}

#PaidNewDetailView {
    logo_top_margin : 26;
    logo_top_margin_min : 6;
    logo_image_width : 50;
    logo_image_height : 44;
    brandLabel_top_margin : 12;
    sellerLabel_top_margin : 96;
    sellerLabel_top_margin_min : 96;
    moneyLabel_top_margin : 14;
    moneyLabel_fee_margin : 5;
    
    discount_content_top_margin : 6A;
    
    tinyApp_content_height : 80;
    tinyApp_line_leftright_margin : 35;
    tinyApp_logo_len : 50;
    tinyApp_small_logo_y : 1.5;
    tinyApp_small_logo_len : 15;
    
    finish_button_width : 180;
    finish_button_bottom_margin_normal : 100;
    finish_button_bottom_margin_with_activity : 70;
    finish_button_bottom_margin_with_activity_subscribe : 100;
    finish_button_top_margin_min : 70;
    subscribe_content_top_margin : 30;
    
    activity_content_height : 86;
    activity_content_leftright_margin : 15;
    activity_content_bottom_margin : 15;
    activity_logo_left_margin : 20;
    activity_button_innerMargin : 34;
    
    brandLabel_font : 17;
    fee_type_font : 30;
    money_font : 48;
    discount_content_font : 13;
    tinyApp_name_font : 13;
    tinyApp_desc_font : 20;
    
    lottery_maskview_scale_top_margin : 0;
    lottery_maskview_scale_left_margin : 150;
    
    backgroungImageLeftMargin : 25;
    backgroungImageTopMargin : 25;
}

#MobileChargeHistoryView {
    history_cell_height : 70;
    history_cell_phone_font : 19;
    history_cell_username_font : 13;
    history_cell_content_margin : 4;
    history_cell_padding_margin : 34;
    history_cell_maxrow : 2;
}

#UploadIDTipsViewController {
    icon_top_margin : 48;
    title_label_top_margin : 24;
    content_view_width : 343;
    content_view_height : 604;
    separate_line_margin : 24;
    
    title_font : 17;
    content_leftRight_margin : 24;
}

#FaceReco {
    icon_top : 104;
    icon_tip_margin : 40;
    info_footer_margin : 40;
    info_leftview_width : 113;
    title_top : 64;
    title_frame_margin : 0;
    frame_width : 300;
    frame_height : 400;
    big_tips_title_left_margin : 20;
    err_tips_left_margin : 20;
    
    confirm_icon_left_margin : 27;
    confirm_icon_top_margin : 63;
    confirm_icon_name_gap : 11;
    rules_info_screen_bottom_gap : 26;
    rules_tip_info_gap: 12;
    
    prepare_readnumber_y : 220;
    start_button_width : 144.3;
    start_button_height : 40;
    start_button_prepareLabel_gap : 200;
    
    result_close_btn_screen_gap : 110;
    
    guide_step_font : 20;
    guide_step_title_y : 90;
    guide_step_title_numbericon_gap : 60;
    guide_numberIcon_left_margin : 90;
    guide_numbericon_width : 24;
    guide_step_content_numbericon_gap : 10;
    guide_step_content_title_gap : 20;
    guide_step_image_content_gap : 25;
    guide_step_face_image_width : 80;
    guide_step_number_image_width : 120;
    guide_step_number_image_height : 35;
    guide_numbericons_gap : 180;
    guide_finish_btn_bottom_gap : 110;
    guide_finish_btn_width : 144.3;
    guide_finish_btn_height : 40;
    guide_title_left_margin : 10;
}

#WCFacingReceivePayerView {
    actionbar_top_margin : 15;
}

#WCPayOfflineCheckTipsView
{
    iconImgViewTopMargin : 78;
    tipsContentTopMargin : 30;
    confirmBtnBottomMargin : 64;
    confirmBtnBottomMargin_hasContent : 105;
    
    tipContentLabelFontSize : 17;
}

#WCPayOfflineAddNewCardTipsView
{
    iconImgViewTopMargin : 58;
    tipsContentFontSize : 17;
    tipsTitleTopMargin : 30;
    addNewCardBtnLeftMargin : 80;
    addNewCardBtnTopMargin : 40;
    viewPayCardBtnBottomMargin : 35;
    iconImgViewTopMargin : 48;
}

#OfflinePayPreConfirmView
{
    confirmview_icon_topMargin : 184;
    confirmview_content_fontsize : 22;
    confirmview_content_topMargin : 30;
    confirmView_content_leftRight_margin : 170;
    confirmview_btn_topMargin : 30;
    confirmview_btn_width : 390;
}

#MMTableViewIndex
{
    indexViewItemHeight : 16;
    indexRoundImageViewWidth : 14;
    indexViewFontSize : 10;
}

#LQTDetailView {
    header_logo_topMargin : 8;
    header_bankName_topMargin : 8;
    header_bankName_fontSize : 17;
    footer_fontsize : 14;
    header_view_bottomMargin : 24;
    
    content_view_height : 468;
    content_view_height_with_activity : 566;
    activity_btn_height : 56;
    activity_btn_bottom_margin : 32;
    content_title_fontSize : 14;
    content_title_topMargin : 40;
    content_uint_fontSize : 25;
    content_money_topMargin : 12;
    content_money_bottomMargin : 40;
    content_know_detail_fontSize : 14
    content_know_detail_top_margin : 8
    content_button_topMargin : 40;
    content_sepBar_height : 1;
    
    enterContent_view_height : 56;
    enterContent_fontsize : 17;
    
    tinyappContent_view_height : 70;
    tinyappContent_title_font : 13;
    tinyappContent_desc_font : 15;
    tinyappContent_name_font : 10;
    tinyappContent_topBottom_margin : 15;
    
    footer_btn_margin : 20;
    lct_ope_padding_top : 24;
}

#LQTMoneyView {
    inputContent_cell_height : 222;
    btn_cell_height : 106;
    btn_with_ptotocol_cell_height : 160;
    
    inputContent_topbar_topMargin : 29;
    inputContent_cell_topMargin : 16;
    
    inputContent_leftRight_margin : 32;
    inputContent_title_fontSize : 14;
    inputContent_title_topMargin : 18;
    inputContent_uint_fontSize : 30;
    inputContent_money_fontSize : 56;
    inputContent_money_height : 60;
    inputContent_cardInfo_leftMargin : 15;
    
    btn_cell_checkbox_len : 15;
    btn_cell_protocol_topMargin : 30;
    btn_cell_btn_topMargin : 30;
    btn_cell_btn_topMargin_withProtocol : 15;
    btn_cell_btn_height : 47;
    
    redeemMarginTop : 24;
    redeemTypeHeight : 64;
}

#WCPayLQTRedeemTypeCell {
    paddingLeft : 32;
    titleMarginUp : 12;
}

#LQTTransView {
    trans_succ_icon_topMargin : 50;
    trans_tips_topMargin : 25;
    trans_unit_fontSize : 30;
    trans_money_topMargin : 16;
    trans_button_topMargin : 128;
}

#AppFakeAuthView
{
    leftPadding: 32;
    appIconWidth: 64;
    appIconTopPadding: 96;
    confirmBtnBottomPadding: 96;
}

#WCWebSearchViewControllerNewH5{
    TextField_Top_Margin : 58;
}

#WCPayBizF2FViewController {
    leftRightMargin : 15;
    contentLeftRightMargin : 30;
    
    topBarMargin : 30;
    topBarHeight : 90;
    
    mch_image_len : 40;
    
    mch_name_font : 15;
    nick_name_font : 13;
    
    amountTitleTopMargin : 20;
    amountTitle_font : 14;
    unitLabel_font : 30;
    amountTextField_font : 56;
    amountTextField_height : 55;
    
    descLabelTopMargin : 24;
    fixAmountContainerViewTopMargin : 18;
    
    transferBtnTopMargin : 30;
    
    commentTextViewTopMargin : 30;
    contentViewBottomMargin : 23;
}

#PayCardListViewConrtoller
{
    paycardlist_table_footer_height : 180;
}

#WCPayRewardViewController {
    headImage_len : 62;
    headImage_topMargin : 50;
    
    view_container_cornerRadius : 3;
    view_leftRightMargin : 20;
    
    payer_detail_nickname_font : 17;
    payer_detail_desc_font : 30;
    payer_detail_amount_leftrightMargin : 17;
    payer_detail_amount_innerMargin : 10;
    payer_detail_amount_font : 21;
    
    editmoney_amount_container_topMargin : 23;
    editmoney_desc_container_topMargin : 20;
    editmoney_title_font : 20;
    editmoney_button_TopMargin : 177;
    editmoney_amount_container_height : 60;
    editmoney_amount_font : 17;
    
    confirm_money_title_font : 17;
    confirm_money_uint_font : 33;
    confirm_money_font : 48;
    confirm_money_margin : 7;
    confirm_money_continer_topMargin : 20;
    confirm_money_btn_topMargin : 177;
    
    intro_view_icon_topMargin : 96;
    intro_view_tips_leftrightMargin : 82;
    intro_view_btn_width : 180;
    intro_view_btn_topMargin : 40;
    intro_view_height : 444;
    
    setup_view_headImg_topMargin : 50;
    setup_view_amount_font : 20;
    setup_view_confirm_btn_topMargin : 36;
    setup_view_desc_container_height : 70;
    setup_view_tips_font : 17;
    setup_view_amount_container_height : 64;
    setup_view_bottom_tips_font : 13;
    
    codeView_len : 220;
    receiver_detail_codeview_topMargin : 100;
    receiver_detail_desc_font : 21;
    receiver_detail_saveBtn_topMargin : 120;
    receiver_detail_btn_innerMargin : 70;
    
    invalid_tipsView_height : 36;
    
    keyboard_topMargin : 30;
}

#PayCardListViewConrtoller
{
    paycardlist_table_footer_height : 180;
}

#WCPayTrasnferToBankCardViewController
{
    kBankIconViewTopMargin : 15;
    kBankInfoLabelTopMargin : 12;
    kCurrencyInputViewTopMargin : 15;
    kCurrencyInputViewHeight : 55;
    kLineViewTopMargin : 5;
    kConfirmButtonTopMargin : 30;
    kBottomDescLabelTopMargin : 20;
    kConfirmBottonMarginTop : 30;
    kConfirmBottonMarginBottom : 40;
}

#WCPayTransferToBankCardPaidSuccessViewController {
    kIconMarginTop : 114;
    kBriefViewHeight : 183;
    kBriefViewTitleFontSize : 18;
    kDetailLabelFontSize : 15;
    kDetailLabelMarginGap : 16;
}

#WCPayNewPwdViewController 
{
    kTitleTopMargin : 80;
    kPasswordTextFieldGap : 80;
    kPwdCrtlLabelFont : 15;
    kPwdCrtlLabelGap : 28;
    kBiotricBtnBottomGap : 150;
    kBiotricIconScale : 1;
    kOldPasswordTextFieldGap : 96;
}

#WCPayPaidSuccessStatusViewController {
    kIconMarginTop : 114;
    kBriefViewHeight : 183;
    kBriefViewTitleFontSize : 17;
    kDetailLabelFontSize : 15;
    kDetailLabelMarginGap : 8;
    kDetailLabelTipsFontSize : 14;
}

#WCPayBindCardSuccessViewController
{
    icon_size : 108;
    icon_margin_up : 109;
    icon_not_button_margin_up : 149;
    
    title_font_size : 24;
    title_color : "#353535";
    title_margin_up : 28;
    
    desc_font_size : 16;
    desc_color : "#888888";
    desc_margin_up : 18;
    
    topLine_color : "#E5E5E5";
    topLine_margin_left : 120;
    topLine_margin_up : 194;
    topLine_height : 0.5;
    
    bankIcon_size : 54;
    bankIcon_margin_left : 120;
    bankIcon_margin_up : 25;
    
    bankTitle_font_size : 16;
    bankTitle_color : "#B2B2B2";
    bankTitle_margin_left : 17.5;
    bankTitle_margin_up : 26;
    
    bankDesc_font_size : 24;
    bankDesc_color : "#353535";
    bankDesc_margin_left : 17.5;
    bankDesc_margin_up : 3;
    
    bankButton_width : 60;
    bankButton_margin_right : 120;
    bankButton_margin_up : 37;
    
    bottomLine_color : "#E5E5E5";
    bottomLine_margin_left : 120;
    bottomLine_margin_up : 25;
    bottomLine_height : 0.5;
    
    doneButton_width : 180;
    doneButton_margin_up : 740;
    doneButton_no_button_margin_up : 710;
}

#multi_select_tool_view {
    btn_margin_border : 22% width;
}

#WCPayAutoDeductVC {
    productNameFont : 17;
    productNameTopMargin : 20;
    sellerNameFont : 14;
    feeFont : 42;
    feeLabelTopMargin : 24;
    leftRightMargin : 20;
    switchContentTopMargin : 45;
    switchContentInnerMargin : 15;
    infoDescFont : 14;
    footerButtonTopMargin : 30;
}

#WCPayBalanceDetailUI
{
    mainLogoTopMargin : 48;
    mainContentViewBottomWhiteMargin : 40;
    
    footerButton_bottomMargin : 64;
    
    lqt_action_font : 16;
}

#ResetPwdViewController
{
    fillCredInfo_header_height : 110;
    fillCredInfo_headerTitle_topMargin : 56;
    fillCredInfo_headerTitle_font : 25;
    fillCredInfo_font : 20;
    fillCredInfo_itemLeftMargin : 30;
    fillCredInfo_leftRightMargin : 30;
    fillCredInfo_cell_Height : 60;
    fillCredInfo_button_width : 250;
    fillCredInfo_button_topMargin : 200;
    
    verifyWay_leftRightMargin : 24;
    verifyWay_arrowRightMargin : 15;
    verifyWay_titleFont : 20;
    verifyWay_descFont : 14;
}

#WCPayECardBankCardListViewController {
    title_topMargin : 48;
    bank_arrow_rightMargin : 48;
    leftRight_margin : 32;
}

#WCRedEnvelopesSelectSkinCellView {
    /*    cell_left_margin : 1.5A;*/
    /*    cell_right_margin : 0.5A;*/
    cell_top_margin : 1.5A;
    
    cell_height : 424;
    cell_width : 256;
    /*    collection_view_header : 3A;*/
    /*    collection_view_line_spacing : 1A;*/
    corp_name_font_size : 17;
    descript_label_font_size : 14;
    corp_name_label_bottom_margin : 4;
    over_time_label_bottom_magin : 14;
    /*    select_image_size : 3A;*/
    /*    select_image_right_margin : 1.5A;*/
    /*    select_image_top_margin : 1A;*/
    /*    corp_label_bottom_margin : 9;*/
    selected_image_view_size : 20;
    selected_image_right_margin : 24;
    selected_image_bottom_margin : 24;
    
    border_width : 2;
    over_time_label_font_size : 12;
    select_label_font_size : 14;
    /*    over_time_label_top_margin : 9;*/
    select_label_left_margin : 4;
    
    promotion_cell_top_margin : 12A;
    promotion_cell_height : 238;
    promotion_cell_width : 18A;
    promotion_cell_corp_name_font_size : 17;
    promotion_cell_wording_font_size : 14;
    promotion_cell_wording_top_margin : 4A;
}

#WCRedEnvelopesSelectSkinFlowLayout {
    cell_width : 280;
    cell_height : 496;
    cell_margin : 20;
    cell_top_margin : 5A;
    exipre_desc_margin_top : 16;
}

#WCRedEnvelopesReceiveHomeView { /* #WCRedEnvelopesReceiveHomeView */
    
    bg_image_view_width : 319;
    bg_image_view_height : 528;
    bg_image_mask_height : 422;
    
    head_bottom_image_view_height : 64.5;
    
    head_image_view_x : 16;
    head_image_view_width : 48;
    head_image_view_height : 48;
    head_Image_radius : 4.8;
    
    nick_name_title_margin_left : 15;
    nick_name_title_font : 17;
    
    desc_label_margin_top : 12;
    
    status_tips_title_y_margin : 6;
    status_tips_title_font : 14;
    
    wishing_tips_y_margin : 12;
    wishing_tips_title_font : 24;
    wishing_tips_bottom_margin : 25;
    
    bg_mask_image_height : 361.5;
    
    top_curve_image_height : 431;
    
    open_button_y_margin : 286.8;
    open_button_width : 110;
    open_button_height : 115;
    
    open_desc_title_font : 20;
    
    open_list_bottom_margin : 15;
    open_list_font : 15;
    
    money_icon_margin_bottom : 13;
    
    open_red_envelope_button_size : 106;
    open_red_envelope_button_font_size : 40;
    open_red_envelope_button_margin_bottom : 56;
    
    close_button_size : 13.2;
    close_button_margin_top : 4.5A;
    close_button_margin_right : 16.7;
    
    not_wish_laebl_left_margin : 30;
    not_wish_lable_font_size : 17;
    
    open_red_envelope_button_font : 25;
    adjust_offsetY : 20;
    adjust_offsetY_landscape : -16;
    close_button_margin_top_hasBtn : 28;
    float_send_btn_margin_top : 28;
}

#FestivalLuckyMoneyMoneyView
{
    top_margin : 199;/*201*/;
    font_width : 67;
    top_margin_1 : 217;
    top_margin_noscroll : 213;
    top_margin_noscroll_1 : 211;
    
    width: 319;
    height : 528;
    
    emoticon_margin_top : 51;
    emoticon_width : 240;
    emoticon_height : 240;
    
    emoticon_image_view_margin_top : 40;
    emoticon_image_view_margin_top_sender_show : 40;
    send_button_margin_bottom : 40;
    greeting_label_margin_top : 304;
    greeting_label_margin_top_receive : 335;
    greeting_label_margin_top_hk : 226;
    greeting_label_margin_top_with_emoticon_hk : 226;
    num_scroll_label_margin_top_hk : 0;
    change_num_label_margin_top_hk : 9;
    
    num_scroll_label_margin_top : 8;
    num_scroll_label_font_size : 60;
    
    num_scroll_unit_label_y : 37;
    
    change_num_label_margin_top : 16;
    change_num_label_font_size : 12;
    
    send_btn_margin_left : 24;
    send_btn_margin_bottom : 54;
    
    over_time_status_font_size : 20;
    
    save_balance_margin_top : 8;
    
    status_message_margin_bottom : 117;
    status_message_margin_bottom_with_emoticon : 117;
    
    add_emoticon_btn_width : 250;
    add_emoticon_btn_height : 100;
    
    add_emoticon_btn_label_offsetY : 4;
    add_emoticon_btn_image_offsetY : 0;
    
    add_emoticon_btn_margin_top : 160;
    
    close_btn_margin_top : 40;
    adjust_offsetY : 20;
    
    send_btn_bottom_padding: 30;
    image_box_left_padding: 30;
    title_image_top_padding: 40;
    greeting_tip_padding: 30;
    send_btn_left_padding: 30;
    
    emoticon_layer_corner_radius : 14;
    
    bgimageview_top_margin : 0;
    bgimageview_height : 210;
    bgimageview_bottom_margin: 10.2;
    bgimageview_bottom_margin_over_time : 96;
    
    sender_view_bottom : 22.3;
    
    /*    greeting_label_margin_top_with_emoticon : 35.9;*/
    greeting_label_margin_top_with_emoticon : 226;
    loading_gif_width : 42;
    loading_gif_height : 42;
    
    unit_lable_font_size : 15;
    
    topMaskViewHeight : 9;
    bottomMaskViewHeight : 9;
    
    snow_gif_height : 233;
    
    fail_retry_btn_width : 101;
    
    camera_btn_extra_width : 30;
}

#WCRedEnvelopesRedEnvelopesDetailViewController
{
    nickname_label_margin_y_new : 24;
    nickname_label_margin_y_new_hasEmoticon : 24;
    topCoverImg_topMargin : 100;
    topCoverImg_topMargin_receiver : 100;
    thx_emoticon_view_topMargin : 28;
    thx_emoticon_view_noSelect_topMargin : 28;
    money_label_margin_y : 16;
    thx_emoticon_view_topMargin : 26;
    thx_emoticon_view_noSelect_topMargin : 26;
    emoticon_view_loading_topMargin : 50;
    emoticonBoardView_cutMargin : 90;
    nickname_label_leftRight_maxMargin : 40;
}

#WCPayLQTPanelView {
    content_leftRight_margin : 40;
    
    title_topMargin : 24;
    
    button_height : 48;
    
    scroll_bottomMargin : 0;
    scroll_item_innerMargin : 32;
    scroll_item_img_leftMargin : 32;
    scroll_item_img_rightMargin : 24;
    scroll_item_img_len : 48;
    scroll_item_titleDesc_margin : 16;
    scroll_item_font : 17;
}

#WCPayOrderPayConfirmView
{
    touchIDButton_marginLeft : 68;
    background_width : 320;
}

#WCStoryBubbleView {
    bubble_view_max_width : 288;
}

#PayMoneyLogic
{
    modal_background_width : 320;
}


#RecordView
{
    recordtip_bg_center_y :  45% height;
    record_trans_offset : 16A;
}

#WCPaySelectVerifyTypeViewV2
{
    header_padding_leftRight : 32;
    button_marginBottom : 64;
}

#WCPayRealnameInfoViewV2
{
    button_marginBottom : 96;
}

#WCPayLQTChargeSetting {
    cell_leftRight_margin : 32;
    
    header_mainTitle_font : 22 dynamic;
    header_subTitle_font : 17 dynamic;
    header_mainTitle_topMargin : 80;
    header_sepLine_topMargin : 48;
    
    charge_time_font : 17 dynamic;
    cell_height : 64 dynamic;
}

#WCPayLQTDepositEntryViewController
{
    Confirm_Button_Bottom_Margin : 130;
}

#WCPayBalanceSaveView
{
    view_leftMargin : 32;
    
    card_view_topMargin : 32;
    card_view_title_font : 15;
    card_view_logo_leftMargin : 24;
    
    money_view_money_font : 56;
    money_view_top_margin : 16;
    money_view_textfield_height : 60;
    
    chargeEntry_font : 17;
    chargeEntry_subFont : 14;
    chargeEntry_margin : 18;
    chargeEntry_topMargin : 48;
}

#WCPayOverseaMainWalletGrayCell
{
    description_font_size: 16;
    extra_font_size: 13;
}

#WCPayLQTDepositNewPlanViewController
{
    kTableViewMarginLeft : 32;
    kDepositAmountFontSize : 14;
    kDepositAmountMarginTop : 40;
    kTextFieldMoneyFontSize : 48;
    kDepositErrorTipsFontSize : 14;
    kDepositErrorTipsColor : "#FA5151";
    kTextFieldAmountMargin : 16;
    kTextFieldHeight : 55;
    kTextFieldLineViewMargin : 8;
    kTipsLabelMarginBottom : 24;
    kCellSeparatorMargin : 20;
    kCellHeight : 56;
    kItemTitleRightMargin : 10;
    kAgreeBtn_LinkText_Margin : 4;
    kTableView_ScrollOffset : 33;
    protocolView_scrollOffset : 60;
    protocolView_scrollOffset_marign : 24;
    productWordLabel_font : 24;
    productWordLabel_mriginUp : 56;
    marketingWordLabel_font : 17;
    marketingWordLabel_marginUp : 16;
    protocol_view_bottomMargin : 58;
    keyboard_offset_margin : 0;
}
#WCPayCardDetailView
{
    bank_no_font : 32;
}

#WCPayAddressItem
{
    labelWidth : 220;
}

#verifyTouchLockView {
    icon_topMargin : 128;
}

#room_live
{
    unit_length: 8; /*_A*/
    group_pubbleInputViewLeftMargin_portrait_ipad: 20; /*X_A*/
    group_pubbleInputViewLeftMargin_landscape_ipad: 43;
    group_expressionButtonRightMargin_portrait_ipad: 20;
    group_expressionButtonRightMargin_landscape_ipad: 43;
    
    group_controlWidgetLeftMargin_ipad: 4;
    group_controlWidgetRightMargin_ipad: 4;
    
    topStatusBarTopMargin_ipad: 3;
    connectedMicAudienceViewTopMargin_portrait_ipad: 3;
    connectedMicAudienceViewAvatarSize_ipad: 5;
    complainButtonTopMargin_ipad: 3;
    group_topControlWidgetTopMargin_ipad: 2;
    group_bottomControlWidgetBottomMargin_ipad: 4;
    finder_bottomControlWidgetBottomMargin_ipad: 4;
    
    commentListViewHeight_ipad: 45.5;
    commentListViewAudienceHeight_ipad: 45.5;
    commentListViewWidth_ipad: 37;
    commentListViewWidth_landscape_ipad: 37;
    commentListViewBottomMargin_ipad: 4;
    commentCellToCellMargin_ipad: 2.5;
    
    connectMicButtonRightMargin: 2;
    
    startLive_bottomButtonBottomMargin: 15;
    startLive_bottomLineBottomMargin: 20;
    startLive_GrowTextViewLeftRightMargin: 40;
    startLive_CountDownTipsBottomMargin_ipad: 35;
    startLive_BottomGradientLayerHeight: 40.75;
    startLive_ControlPanelViewLeftRightMargin: 1.5;
    startLive_ControlPanelViewButtonWidth: 6.5;
    startLive_ContentLeftRightMargin: 3;
    
    finder_circle_button_size_portrait_ipad: 5;
    finder_circle_button_size_landscape_ipad: 5;
    
    finder_bottom_action_button_distance_portrait_ipad: 2;
    finder_bottom_action_button_distance_landscape_ipad: 2;
    finder_comment_button_width_portrait_ipad: 14;
    finder_comment_button_width_landscape_ipad: 12;
    finder_anchor_assistant_comment_button_width_portrait_ipad: 14;
    finder_anchor_assistant_comment_button_width_landscape_ipad: 12;
    finder_pause_view_content_center_y_ratio_ipad: 0.29;
}

#WCPayQRCoverPageView
{
    topbar_height : 57;
    topLeftIcon_marginLeft : 20;
    topLeftTitle_fontSize : 16;
}

#WCPayBottomPanelView
{
    title_font : 18;
    content_font : 16;
}

#WCPayCoinView
{
    width : 120;
    coin_amount_text_size : 24;
}

#WCCoinMoneyInputView {
    inputContent_cell_height : 222;
    btn_cell_height : 106;
    btn_with_ptotocol_cell_height : 160;
    
    inputContent_topbar_topMargin : 29;
    inputContent_cell_topMargin : 29;
    
    inputContent_leftRight_margin : 32;
    inputContent_title_fontSize : 14;
    inputContent_title_topMargin : 18;
    inputContent_uint_fontSize : 30;
    inputContent_money_fontSize : 56;
    inputContent_money_height : 60;
    inputContent_cardInfo_leftMargin : 15;
    
    btn_cell_checkbox_len : 15;
    btn_cell_protocol_topMargin : 30;
    btn_cell_btn_topMargin : 30;
    btn_cell_btn_topMargin_withProtocol : 15;
    btn_cell_btn_height : 47;
    
    redeemMarginTop : 24;
    redeemTypeHeight : 64;
}

#WCPayBalanceSelectCardView
{
    content_size : 390;
}

#RemarkPageSheet {
    bottomView_topMargin : 7A;
    bottomView_bottomMargin : 8A;
}
