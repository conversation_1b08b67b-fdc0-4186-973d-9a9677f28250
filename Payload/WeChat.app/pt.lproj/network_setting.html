<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="Content-Language" content="en" charset="utf-8">
	<meta charset="UTF-8">
	<meta id="viewport" name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1; user-scalable=no;" />
	<style> html,p,h1,ul,li{margin:0px;padding:0px;} ul{list-style-type:none;} body{color:#000;font:14px/1.5 微软雅黑,Helvetica,&quot;Helvetica Neue&quot;,&quot;segoe UI Light&quot;,&quot;Kozuka Gothic Pro&quot;;} h1{font-size:16px; padding-bottom:12px;margin-bottom:5px;} .articleTitle{margin-bottom:6px;} .sec_body { margin:25px 15px;} p{margin-bottom:6px;} .bold{font-weight:bold;} .article{margin-bottom:32px;} .sec_body .content { padding:10px;border-width:0px; } @media (prefers-color-scheme: dark) { body { background-color: #232323; color: rgba(255, 255, 255, .8); } } </style>
	<title>Sem conex&atilde;o com a Internet</title>
</head>
<body class="sec_body">
	<div class="container">
		<h1>Seu dispositivo n&atilde;o est&aacute; conectado &agrave; Internet</h1>
		<div class="article">
			<p class="articleTitle">Para se conectar &agrave; Internet, tente os m&eacute;todos abaixo:</p>
			<ul>
				<li>No seu dispositivo, acesse &quot;<strong>Ajustes</strong>&quot; - &quot;<strong>Wi-Fi</strong>&quot; e conecte-se a uma rede Wi-Fi dispon&iacute;vel.</li>
				<li>No seu dispositivo, acesse &quot;<strong>Ajustes</strong>&quot; - &quot;<strong>Celular</strong>&quot;, e ative &quot;<strong>Dados Celulares</strong>&quot;. (Pode haver cobran&ccedil;a de uso de dados pelo provedor.)</li>
			</ul>
		</div>
		<div class="article">
			<p class="articleTitle">Se voc&ecirc; emparelhou seu dispositivo com um Apple Watch:</p>
			<ul>
				<li>Abra o aplicativo &quot;<strong>Watch</strong>&quot; - &quot;<strong>Dados Celulares</strong>&quot; - &quot;<strong>WeChat</strong>&quot; e conceda permiss&atilde;o ao WeChat para acessar os dados.</li>
			</ul>
		</div>
		<div class="article">
			<p class="articleTitle">Se voc&ecirc; se conectou a uma rede Wi-Fi:</p>
			<ul>
				<li>Verifique se o hotspot Wi-Fi est&aacute; conectado &agrave; Internet ou se seu dispositivo tem permiss&atilde;o para acessar o hotspot.</li>
			</ul>
		</div>
	</div>
</body>
</html>

